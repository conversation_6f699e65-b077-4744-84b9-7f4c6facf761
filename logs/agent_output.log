load conf file: config/agent_config.yaml
2025-05-27 16:45:16.288225000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:35	DevInsight Agent 启动中...	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "version": "2.0.0-pipeline"}
2025-05-27 16:45:16.288578000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:45	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-27 16:45:16.288602000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:54	Initializing plugin manager	{"plugins_dir": "plugins/build"}
2025-05-27 16:45:16.289741000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:122	Loading plugin	{"path": "plugins/build/email_alerter-1.0.0.so"}
2025-05-27 16:45:16.891752000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:179	Plugin loaded successfully	{"name": "email_alerter", "version": "1.0.0", "type": "alerter"}
2025-05-27 16:45:16.891881000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:122	Loading plugin	{"path": "plugins/build/multi_file_example-1.0.0.so"}
2025-05-27 16:45:17.370306000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:179	Plugin loaded successfully	{"name": "multi_file_example", "version": "1.0.0", "type": "collector"}
2025-05-27 16:45:17.370403000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:122	Loading plugin	{"path": "plugins/build/mysql_collector-1.0.0.so"}
2025-05-27 16:45:17.997504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:179	Plugin loaded successfully	{"name": "mysql_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 16:45:17.997573000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:122	Loading plugin	{"path": "plugins/build/system_collector-1.0.0.so"}
2025-05-27 16:45:18.604530000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:179	Plugin loaded successfully	{"name": "system_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 16:45:18.604612000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:122	Loading plugin	{"path": "plugins/build/threshold_processor-1.0.0.so"}
2025-05-27 16:45:19.021532000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:179	Plugin loaded successfully	{"name": "threshold_processor", "version": "1.0.0", "type": "processor"}
2025-05-27 16:45:19.021588000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:66	Plugin manager initialized successfully	{"loaded_plugins": 5}
2025-05-27 16:45:19.021605000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:75	Connecting to Control Plane	{"server": "localhost:50051"}
2025-05-27 16:45:19.301899000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:162	Agent registered successfully	{"message": "注册成功"}
2025-05-27 16:45:19.301967000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:103	注册Agent成功	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.301980000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:111	Starting pipeline manager	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.302011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:131	Pipeline manager started successfully
2025-05-27 16:45:19.302023000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:77	DevInsight Agent 启动完成，等待流水线配置...
2025-05-27 16:45:19.302113000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:193	流水线流创建成功
2025-05-27 16:45:20.359552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:84	接收到退出信号，正在关闭 Agent...
2025-05-27 16:45:20.359670000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	DevInsight Agent 已关闭
2025-05-27 16:45:20.359681000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:145	Stopping pipeline manager
2025-05-27 16:45:20.359708000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:164	Pipeline manager stopped
2025-05-27 16:45:20.359719000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:117	Disconnecting from Control Plane
2025-05-27 16:45:20.359824000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:385	Pipeline stream error	{"error": "failed to receive pipeline command: rpc error: code = Canceled desc = context canceled", "retry_count": 1, "max_retries": 5}
aiops/agent/internal/pipeline.(*Manager).handlePipelineStream
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/manager.go:385
2025-05-27 16:45:20.360089000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/control_plane_client.go:137	Disconnected from Control Plane
