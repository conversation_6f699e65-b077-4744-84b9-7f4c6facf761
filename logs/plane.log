2025-05-27 16:45:11.477031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-27 16:45:11.477608000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-27 16:45:11.621361000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-27 16:45:11.621432000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-27 16:45:11.621444000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-27 16:45:11.654025000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-27 16:45:11.654065000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-27 16:45:11.654098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-27 16:45:11.654430000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-27 16:45:11.655680000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-27 16:45:11.655999000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-27 16:45:11.656010000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-27 16:45:11.656019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-27 16:45:11.656027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-27 16:45:11.656035000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-27 16:45:19.227697000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "RegisterAgent", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "agentIP": "127.0.0.1"}
2025-05-27 16:45:19.250355000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "2.208ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"1b974dfb-f795-43d5-85c1-778cec3a64ef\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:250
aiops/control_plane/internal/controller/grpc.(*AgentController).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/agent.go:37
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:120
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:303
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-27 16:45:19.250443000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:259	注册新 Agent	{"AgentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "IP": "127.0.0.1", "支持的采集器类型": ["system", "mysql", "redis", "http"]}
2025-05-27 16:45:19.284725000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:272	新 Agent 注册成功	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307093000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307166000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307176000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.314542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.359528000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:129	正在关闭服务...
2025-05-27 16:45:20.359669000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:109	接收到停止信号，退出心跳监控循环
2025-05-27 16:45:20.359717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:96	Agent心跳监控已停止
2025-05-27 16:45:20.359775000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:111	正在停止 HTTP API 服务器
2025-05-27 16:45:20.359893000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:111	正在停止 gRPC 服务器
2025-05-27 16:45:20.360027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线stream context已完成", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.360031000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-27 16:45:20.383407000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:437	Unregistered agent command queue	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383428000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383437000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:591	Agent断开连接	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:612	Agent状态已更新为离线	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.384617000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.384720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:142	DevInsight Control Plane 已关闭
2025-05-27 19:39:22.998403000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-27 19:39:23.018583000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-27 19:39:23.120319000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-27 19:39:23.120404000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-27 19:39:23.120417000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-27 19:39:23.130238000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-27 19:39:23.130305000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-27 19:39:23.130427000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-27 19:39:23.131277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-27 19:39:23.131748000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-27 19:39:23.132084000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-27 19:39:23.132100000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-27 19:39:23.132111000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-27 19:39:23.132120000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-27 19:39:23.132127000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-27 19:40:04.815343000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "RegisterAgent", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "agentIP": "127.0.0.1"}
2025-05-27 19:40:04.816119000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.217ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"9122d9f7-f686-4238-b7d0-defd7fc0f1fc\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:250
aiops/control_plane/internal/controller/grpc.(*AgentController).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/agent.go:37
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:120
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:303
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-27 19:40:04.816226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:259	注册新 Agent	{"AgentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "IP": "127.0.0.1", "支持的采集器类型": ["system", "mysql", "redis", "http"]}
2025-05-27 19:40:04.816547000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:272	新 Agent 注册成功	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:40:04.817302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:40:04.817332000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:40:04.817343000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:40:04.817720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:40:23.242289000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "9f837f47-8b09-496d-a2d1-922b7365258f"}
2025-05-27 19:40:23.252650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "9f837f47-8b09-496d-a2d1-922b7365258f", "offline_duration": 10607.480249}
2025-05-27 19:40:23.252994000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "162808db-fc2e-4507-bb13-2c57dbbab7e1"}
2025-05-27 19:40:23.253016000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "162808db-fc2e-4507-bb13-2c57dbbab7e1", "offline_duration": 10579.077586}
2025-05-27 19:40:23.253182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "0220d26e-dbb6-4326-a8af-6439fda13805"}
2025-05-27 19:40:23.253206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "0220d26e-dbb6-4326-a8af-6439fda13805", "offline_duration": 10560.395865}
2025-05-27 19:40:23.253383000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 19:40:23.253395000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "offline_duration": 10503.88004}
2025-05-27 19:40:23.253405000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:204	Agent状态检查完成	{"offline_marked": 0, "auto_deleted": 4, "total_checked": 5}
2025-05-27 19:41:04.817508000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:41:04.819400000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:363	Agent 心跳更新	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "interval": 60.002457}
2025-05-27 19:42:04.817116000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 19:42:04.938344000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:363	Agent 心跳更新	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "interval": 60.074099}
2025-05-27 22:35:44.862825000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-27 22:35:45.015328000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-27 22:35:45.314999000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-27 22:35:45.315112000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-27 22:35:45.315125000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-27 22:35:45.325131000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-27 22:35:45.325200000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-27 22:35:45.325315000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-27 22:35:45.326084000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-27 22:35:45.326514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-27 22:35:45.326928000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-27 22:35:45.326952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-27 22:35:45.326963000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-27 22:35:45.326972000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-27 22:35:45.327007000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-27 22:36:45.436892000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "109.742ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-27 19:40:04.816\",`updated_at`=\"2025-05-27 22:36:45.327\",`deleted_at`=NULL,`agent_id`=\"9122d9f7-f686-4238-b7d0-defd7fc0f1fc\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"offline\",`last_heartbeat`=\"2025-05-27 19:42:04.892\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 5"}
2025-05-27 22:36:45.474755000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:146	Agent因心跳超时被标记为离线	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "timeout": 10480.43429}
2025-05-27 22:36:45.475701000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc"}
2025-05-27 22:36:45.475718000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "9122d9f7-f686-4238-b7d0-defd7fc0f1fc", "offline_duration": 10480.43429}
2025-05-27 22:36:45.475742000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:204	Agent状态检查完成	{"offline_marked": 1, "auto_deleted": 1, "total_checked": 1}
2025-05-27 22:37:29.021146000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "90.282ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:29.063763000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:30.807864000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.408ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:30.809425000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:37.043157000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.374ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:37.279268000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:38.175388000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.159ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:38.175484000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:39.022638000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.450ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:39.023093000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:44.182760000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.276ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:44.183396000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:45.090892000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.226ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:45.090964000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:46.459945000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.141ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:46.460042000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:47.048669000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.136ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:47.048777000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:37:53.627775000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "admin", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:40:45.676241000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "129.201ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:41:36.858578000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "120.331ms", "rows": 1, "sql": "SELECT * FROM `users` WHERE username = \"admin\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
2025-05-27 22:41:36.986275000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "admin", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:49.674937000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.330ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:49.772157000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:50.928504000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.387ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:50.928701000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:51.799675000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.281ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:51.799855000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:59.587687000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.449ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:41:59.690687000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:42:02.751830000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.272ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:42:02.751965000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:42:46.181621000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "0.187ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:42:55.192755000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:43:45.691201000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "415.063ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:44:45.673806000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "136.508ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:45:17.787654000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "171.541ms", "rows": 1, "sql": "SELECT * FROM `users` WHERE username = \"admin\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
2025-05-27 22:47:46.239698000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "208.700ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:48:46.272918000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "578.309ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:49:46.261241000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "137.849ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:50:46.057609000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "235.643ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-27 22:51:11.400292000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56	trace	{"error": "record not found", "elapsed": "28.269ms", "rows": 0, "sql": "SELECT * FROM `users` WHERE username = \"123123\" AND `users`.`deleted_at` IS NULL ORDER BY `users`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*userRepositoryImpl).GetUserByUsername
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/user_repo.go:56
aiops/control_plane/internal/service.(*UserService).Authenticate
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:161
aiops/control_plane/internal/service.(*UserService).ValidateUser
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:187
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:42
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-27 22:51:11.429700000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44	用户登录失败	{"username": "123123", "error": "用户名或密码错误"}
aiops/control_plane/internal/controller/http.(*AuthController).Login
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/http/auth.go:44
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).corsMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:278
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-28 11:47:03.847048000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-28 11:47:03.884819000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-28 11:47:03.979048000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-28 11:47:03.979145000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-28 11:47:03.979177000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-28 11:47:03.988397000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-28 11:47:03.988449000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-28 11:47:03.988532000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-28 11:47:04.013069000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-28 11:47:04.013467000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-28 11:47:04.013817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-28 11:47:04.013846000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-28 11:47:04.013865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-28 11:47:04.013875000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-28 11:47:04.013882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-28 11:47:37.887508000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "RegisterAgent", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa", "agentIP": "127.0.0.1"}
2025-05-28 11:47:37.892016000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "3.463ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"79fcf87b-0608-4936-ba98-1226b4fc1cfa\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:250
aiops/control_plane/internal/controller/grpc.(*AgentController).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/agent.go:37
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:120
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:303
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-28 11:47:37.892163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:259	注册新 Agent	{"AgentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa", "IP": "127.0.0.1", "支持的采集器类型": ["system", "mysql", "redis", "http"]}
2025-05-28 11:47:37.902514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:272	新 Agent 注册成功	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:37.904244000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:37.904366000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:37.904385000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:37.905592000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.575773000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-28 11:47:46.575842000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线stream context已完成", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.576738000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:437	Unregistered agent command queue	{"agent_id": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.576778000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.576797000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:591	Agent断开连接	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.678178000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "101.029ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 11:47:37.892\",`updated_at`=\"2025-05-28 11:47:46.577\",`deleted_at`=NULL,`agent_id`=\"79fcf87b-0608-4936-ba98-1226b4fc1cfa\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"offline\",`last_heartbeat`=\"2025-05-28 11:47:37.892\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 6"}
2025-05-28 11:47:46.678226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:612	Agent状态已更新为离线	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:47:46.678272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 11:48:03.992015000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:195	Agent连接恢复	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa", "disconnect_duration": 26.098974}
2025-05-28 11:53:04.349478000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "338.376ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 11:47:37.892\",`updated_at`=\"2025-05-28 11:53:04.011\",`deleted_at`=NULL,`agent_id`=\"79fcf87b-0608-4936-ba98-1226b4fc1cfa\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"offline\",`last_heartbeat`=\"2025-05-28 11:47:37.892\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 6"}
2025-05-28 11:53:04.368063000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:146	Agent因心跳超时被标记为离线	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa", "timeout": 326.118817}
2025-05-28 11:53:04.368104000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:204	Agent状态检查完成	{"offline_marked": 1, "auto_deleted": 0, "total_checked": 1}
2025-05-28 12:03:04.357111000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/device_repo.go:84	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "343.099ms", "rows": 0, "sql": "SELECT * FROM `devices` WHERE agent_id = \"79fcf87b-0608-4936-ba98-1226b4fc1cfa\" AND `devices`.`deleted_at` IS NULL"}
2025-05-28 12:03:04.475363000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:63	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "116.516ms", "rows": 1, "sql": "UPDATE `agents` SET `deleted_at`=\"2025-05-28 12:03:04.358\" WHERE agent_id = \"79fcf87b-0608-4936-ba98-1226b4fc1cfa\" AND `agents`.`deleted_at` IS NULL"}
2025-05-28 12:03:04.475408000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:243	自动删除Agent成功	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa"}
2025-05-28 12:03:04.475421000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:164	自动删除长时间离线的Agent	{"agentID": "79fcf87b-0608-4936-ba98-1226b4fc1cfa", "offline_duration": 926.121794}
2025-05-28 12:03:04.475434000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:204	Agent状态检查完成	{"offline_marked": 0, "auto_deleted": 1, "total_checked": 1}
2025-05-28 12:06:04.506909000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "493.109ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 12:15:04.493556000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "478.831ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 12:18:04.497160000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "480.833ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 12:21:04.363596000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "346.137ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 12:36:27.565668000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-28 12:36:27.566190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-28 12:36:27.632056000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-28 12:36:27.632161000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-28 12:36:27.632175000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-28 12:36:27.642998000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-28 12:36:27.643060000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-28 12:36:27.643156000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-28 12:36:27.656091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-28 12:36:27.656637000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-28 12:36:27.657128000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-28 12:36:27.657155000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-28 12:36:27.657167000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-28 12:36:27.657176000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-28 12:36:27.657185000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-28 12:37:27.980057000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "335.346ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 12:42:43.003051000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "RegisterAgent", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "agentIP": "127.0.0.1"}
2025-05-28 12:42:43.033896000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "21.397ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"6ff77712-1944-435b-be39-77166eaf0e80\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:264
aiops/control_plane/internal/controller/grpc.(*AgentController).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/agent.go:37
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:120
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:303
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-28 12:42:43.034037000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:273	注册新 Agent	{"AgentID": "6ff77712-1944-435b-be39-77166eaf0e80", "IP": "127.0.0.1", "支持的采集器类型": ["system", "mysql", "redis", "http"]}
2025-05-28 12:42:43.045219000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:286	新 Agent 注册成功	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:42:43.046238000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:42:43.046304000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:42:43.046317000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:42:43.047929000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:43:43.051039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:43:43.818075000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "217.550ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:43:43.6\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:43:43.6\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:43:43.818122000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.566463}
2025-05-28 12:44:43.047690000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:44:43.531816000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "456.426ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:44:43.075\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:44:43.075\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:44:43.531859000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.474853}
2025-05-28 12:45:43.048466000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:45:43.050177000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.974272}
2025-05-28 12:46:43.048620000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:46:43.672864000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "621.511ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:46:43.051\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:46:43.051\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:46:43.672920000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001657}
2025-05-28 12:47:43.048782000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:47:43.388662000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "337.051ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:47:43.051\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:47:43.051\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:47:43.388714000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000267}
2025-05-28 12:48:43.049796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:48:43.392519000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "340.510ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:48:43.051\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:48:43.051\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:48:43.392548000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.00042}
2025-05-28 12:49:43.050825000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:49:43.470271000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.402664}
2025-05-28 12:50:43.048963000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:50:43.449780000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.979224}
2025-05-28 12:51:43.042105000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:51:43.415621000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "345.910ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:51:43.069\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:51:43.069\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:51:43.415661000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.635805}
2025-05-28 12:52:43.041866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:52:43.043609000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.973375}
2025-05-28 12:53:43.042329000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:53:43.396076000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "350.927ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:53:43.045\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:53:43.045\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:53:43.396123000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.002049}
2025-05-28 12:54:43.042698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:54:43.392019000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "348.208ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:54:43.043\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:54:43.043\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:54:43.392073000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998654}
2025-05-28 12:55:43.042469000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:55:43.379990000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "334.721ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:55:43.045\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:55:43.045\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:55:43.380038000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001468}
2025-05-28 12:56:43.042741000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:56:43.044705000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998735}
2025-05-28 12:57:43.042028000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:57:43.408236000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "363.350ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:57:43.044\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:57:43.044\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:57:43.408284000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000879}
2025-05-28 12:58:43.042908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:58:43.045235000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.999235}
2025-05-28 12:59:43.042527000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 12:59:43.387616000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "343.816ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 12:59:43.043\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 12:59:43.043\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 12:59:43.387768000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.999563}
2025-05-28 13:00:43.043065000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:00:43.046523000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.002314}
2025-05-28 13:01:43.043070000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:01:43.044881000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998298}
2025-05-28 13:02:43.043082000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:02:43.504162000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "458.337ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:02:43.045\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:02:43.045\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:02:43.504230000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001518}
2025-05-28 13:03:43.043290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:03:43.420825000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "362.391ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:03:43.058\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:03:43.058\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:03:43.420864000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.012627}
2025-05-28 13:04:43.043847000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:04:43.550907000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.411042}
2025-05-28 13:05:43.042308000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:05:43.451832000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.963737}
2025-05-28 13:06:43.043153000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:06:43.397931000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "351.650ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:06:43.046\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:06:43.046\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:06:43.397988000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.61303}
2025-05-28 13:07:43.043109000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:07:43.044777000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998024}
2025-05-28 13:08:43.044853000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:08:43.910992000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "138.118ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:08:43.772\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:08:43.772\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:08:43.911036000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.728625}
2025-05-28 13:09:43.043362000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:09:43.524341000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.738095}
2025-05-28 13:10:43.043809000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:10:43.492046000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "119.147ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:10:43.372\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:10:43.372\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:10:43.492095000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.861938}
2025-05-28 13:11:43.043664000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:11:43.045728000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.672086}
2025-05-28 13:12:43.043416000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:12:43.383078000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "336.745ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:12:43.046\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:12:43.046\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:12:43.383124000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001323}
2025-05-28 13:13:43.044178000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:13:43.045902000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.999052}
2025-05-28 13:14:43.044089000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:43.394757000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "347.809ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:14:43.046\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:14:43.046\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:14:43.394802000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001542}
2025-05-28 13:14:51.550432000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线stream context已完成", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.550493000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-28 13:14:51.556180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.556324000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.557995000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.686649000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:437	Unregistered agent command queue	{"agent_id": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.688389000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.686663000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "命令队列已关闭", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.688427000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:605	Agent断开连接	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.689283000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:626	Agent状态已更新为离线	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:14:51.689322000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:15:27.641854000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:202	Agent连接恢复	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "disconnect_duration": 44.594109}
2025-05-28 13:15:43.509235000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:15:43.509837000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.462845}
2025-05-28 13:16:43.043984000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:16:43.386715000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "339.857ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:16:43.046\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:16:43.046\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:16:43.386754000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.537086}
2025-05-28 13:17:43.042582000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:17:43.376980000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "331.572ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:17:43.045\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:17:43.045\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:17:43.377030000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998534}
2025-05-28 13:18:43.044091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:18:43.388944000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "341.871ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:18:43.047\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:18:43.046\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:18:43.388979000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001673}
2025-05-28 13:19:43.044180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:19:43.388850000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "341.770ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:19:43.047\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:19:43.047\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:19:43.388895000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000013}
2025-05-28 13:20:43.044832000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:20:43.444355000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.355067}
2025-05-28 13:21:43.044810000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:21:43.418117000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "345.665ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:21:43.072\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:21:43.072\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:21:43.418156000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.670331}
2025-05-28 13:22:43.044763000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:22:43.385027000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "337.426ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:22:43.047\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:22:43.047\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:22:43.385075000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.975118}
2025-05-28 13:23:43.045126000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:23:43.047062000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998908}
2025-05-28 13:24:43.045021000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:24:43.392293000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "344.435ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:24:43.047\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:24:43.047\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:24:43.392345000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001346}
2025-05-28 13:25:43.045027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:25:43.046920000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998398}
2025-05-28 13:26:28.003198000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "361.797ms", "rows": 1, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 13:26:43.044575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:26:43.047806000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.999404}
2025-05-28 13:27:43.045180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:27:43.046996000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000726}
2025-05-28 13:28:43.045363000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:28:43.050793000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001816}
2025-05-28 13:29:43.045333000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:29:43.047139000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998345}
2025-05-28 13:30:43.046031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:30:43.385878000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:54	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "338.584ms", "rows": 1, "sql": "UPDATE `agents` SET `created_at`=\"2025-05-28 12:42:43.034\",`updated_at`=\"2025-05-28 13:30:43.047\",`deleted_at`=NULL,`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`agent_ip`=\"127.0.0.1\",`supported_collector_types`=\"[system mysql redis http]\",`status`=\"online\",`last_heartbeat`=\"2025-05-28 13:30:43.047\",`device_config`=\"\" WHERE `agents`.`deleted_at` IS NULL AND `id` = 7"}
2025-05-28 13:30:43.386079000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000603}
2025-05-28 13:31:43.045496000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:31:43.051358000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001262}
2025-05-28 13:32:43.045125000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:32:43.046807000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.997922}
2025-05-28 13:33:43.045823000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:33:43.054709000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.002352}
2025-05-28 13:34:43.046234000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:34:43.051684000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.000339}
2025-05-28 13:35:43.046095000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:35:43.406921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.357034}
2025-05-28 13:36:28.422871000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "780.998ms", "rows": 1, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-28 13:36:43.045124000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:36:43.050818000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.641324}
2025-05-28 13:37:43.046196000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:37:43.047865000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.999979}
2025-05-28 13:38:43.046077000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:38:43.049480000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.00158}
2025-05-28 13:39:43.046474000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:39:43.048395000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.998753}
2025-05-28 13:40:43.045232000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:40:43.587803000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.464629}
2025-05-28 13:41:43.046042000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:41:43.423223000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.892362}
2025-05-28 13:42:43.045901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:42:43.046450000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.64167}
2025-05-28 13:43:43.046054000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:43:43.048291000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001035}
2025-05-28 13:44:43.046566000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:44:43.051841000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.002184}
2025-05-28 13:45:43.050285000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:43.051807000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.001827}
2025-05-28 13:45:45.296207000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-28 13:45:45.390812000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.390891000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.390947000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.710427000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/pipeline_repo.go:174	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "319.123ms", "rows": 1, "sql": "UPDATE `pipeline_statuses` SET `created_at`=\"2025-05-27 16:26:35.268\",`updated_at`=\"2025-05-28 13:45:45.391\",`deleted_at`=NULL,`pipeline_id`=\"\",`agent_id`=\"6ff77712-1944-435b-be39-77166eaf0e80\",`status`=\"connected\",`error_message`=\"\",`start_timestamp`=0,`last_update_timestamp`=1748411145,`metrics_data`=\"\" WHERE `pipeline_statuses`.`deleted_at` IS NULL AND `id` = 1"}
2025-05-28 13:45:45.710535000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.724530000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:437	Unregistered agent command queue	{"agent_id": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.724551000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.724551000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "命令队列已关闭", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.724563000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:605	Agent断开连接	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.724993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:626	Agent状态已更新为离线	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:45:45.725007000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:46:27.646105000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:202	Agent连接恢复	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "disconnect_duration": 44.593919}
2025-05-28 13:46:43.045884000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:46:43.047488000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 59.995641}
2025-05-28 13:47:43.048626000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:47:43.156095000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.009901}
2025-05-28 13:48:43.046024000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Heartbeat", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:48:43.098363000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:377	Agent 心跳更新	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80", "interval": 60.010267}
2025-05-28 13:49:40.130628000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-28 13:49:40.233537000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:49:40.233649000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:605	Agent断开连接	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:49:40.303804000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:626	Agent状态已更新为离线	{"agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
2025-05-28 13:49:40.303845000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "6ff77712-1944-435b-be39-77166eaf0e80"}
