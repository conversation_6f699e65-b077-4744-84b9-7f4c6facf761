load conf file: config/control_plane.yaml
2025-05-27 16:45:11.477031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025/05/27 16:45:11 配置加载完成: HTTP端口=8080, gRPC端口=50051, 数据库路径=data/control_plane.db?_busy_timeout=5000, SMTP服务器=:587, 告警阈值=90.00
2025-05-27 16:45:11.477608000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-27 16:45:11.621361000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-27 16:45:11.621432000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-27 16:45:11.621444000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-27 16:45:11.654025000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:74	数据库迁移完成
2025-05-27 16:45:11.654065000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-27 16:45:11.654098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:79	Agent心跳监控已启动	{"heartbeat_timeout": 300, "cleanup_interval": 60}
2025-05-27 16:45:11.654430000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:249	管理员用户已存在，跳过初始化
2025-05-27 16:45:11.655680000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:98	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> aiops/control_plane/internal/transport/http.(*Server).handleHealth-fm (2 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> aiops/control_plane/internal/controller/http.(*AuthController).Login-fm (3 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> aiops/control_plane/internal/controller/http.(*AuthController).Logout-fm (3 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> aiops/control_plane/internal/controller/http.(*AuthController).RefreshToken-fm (3 handlers)
[GIN-debug] GET    /api/v1/agents            --> aiops/control_plane/internal/controller/http.(*AgentController).GetAllAgents-fm (4 handlers)
[GIN-debug] GET    /api/v1/agents/:id        --> aiops/control_plane/internal/controller/http.(*AgentController).GetAgent-fm (4 handlers)
[GIN-debug] DELETE /api/v1/agents/:id        --> aiops/control_plane/internal/controller/http.(*AgentController).DeleteAgent-fm (4 handlers)
[GIN-debug] PUT    /api/v1/agents/:id/status --> aiops/control_plane/internal/controller/http.(*AgentController).UpdateAgentStatus-fm (4 handlers)
[GIN-debug] GET    /api/v1/devices           --> aiops/control_plane/internal/controller/http.(*DeviceController).GetAllDevices-fm (4 handlers)
[GIN-debug] POST   /api/v1/devices           --> aiops/control_plane/internal/controller/http.(*DeviceController).CreateDevice-fm (4 handlers)
[GIN-debug] GET    /api/v1/devices/:id       --> aiops/control_plane/internal/controller/http.(*DeviceController).GetDevice-fm (4 handlers)
[GIN-debug] PUT    /api/v1/devices/:id       --> aiops/control_plane/internal/controller/http.(*DeviceController).UpdateDevice-fm (4 handlers)
[GIN-debug] DELETE /api/v1/devices/:id       --> aiops/control_plane/internal/controller/http.(*DeviceController).DeleteDevice-fm (4 handlers)
[GIN-debug] GET    /api/v1/tasks             --> aiops/control_plane/internal/controller/http.(*TaskController).GetAllTasks-fm (4 handlers)
[GIN-debug] POST   /api/v1/tasks             --> aiops/control_plane/internal/controller/http.(*TaskController).CreateTask-fm (4 handlers)
[GIN-debug] GET    /api/v1/tasks/:id         --> aiops/control_plane/internal/controller/http.(*TaskController).GetTask-fm (4 handlers)
[GIN-debug] PUT    /api/v1/tasks/:id         --> aiops/control_plane/internal/controller/http.(*TaskController).UpdateTask-fm (4 handlers)
[GIN-debug] DELETE /api/v1/tasks/:id         --> aiops/control_plane/internal/controller/http.(*TaskController).DeleteTask-fm (4 handlers)
[GIN-debug] POST   /api/v1/tasks/:id/start   --> aiops/control_plane/internal/controller/http.(*TaskController).StartTask-fm (4 handlers)
[GIN-debug] POST   /api/v1/tasks/:id/stop    --> aiops/control_plane/internal/controller/http.(*TaskController).StopTask-fm (4 handlers)
[GIN-debug] GET    /api/v1/metrics           --> aiops/control_plane/internal/controller/http.(*MetricController).GetMetrics-fm (4 handlers)
[GIN-debug] GET    /api/v1/metrics/devices/:deviceId --> aiops/control_plane/internal/controller/http.(*MetricController).GetDeviceMetrics-fm (4 handlers)
[GIN-debug] GET    /api/v1/metrics/summary   --> aiops/control_plane/internal/controller/http.(*MetricController).GetMetricsSummary-fm (4 handlers)
[GIN-debug] GET    /api/v1/alerts            --> aiops/control_plane/internal/controller/http.(*AlertController).GetAllAlerts-fm (4 handlers)
[GIN-debug] GET    /api/v1/alerts/rules      --> aiops/control_plane/internal/controller/http.(*AlertController).GetAllAlertRules-fm (4 handlers)
[GIN-debug] POST   /api/v1/alerts/rules      --> aiops/control_plane/internal/controller/http.(*AlertController).CreateAlertRule-fm (4 handlers)
[GIN-debug] GET    /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/controller/http.(*AlertController).GetAlertRule-fm (4 handlers)
[GIN-debug] PUT    /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/controller/http.(*AlertController).UpdateAlertRule-fm (4 handlers)
[GIN-debug] DELETE /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/controller/http.(*AlertController).DeleteAlertRule-fm (4 handlers)
[GIN-debug] GET    /api/v1/users             --> aiops/control_plane/internal/controller/http.(*UserController).GetAllUsers-fm (5 handlers)
[GIN-debug] POST   /api/v1/users             --> aiops/control_plane/internal/controller/http.(*UserController).CreateUser-fm (5 handlers)
[GIN-debug] GET    /api/v1/users/:id         --> aiops/control_plane/internal/controller/http.(*UserController).GetUser-fm (5 handlers)
[GIN-debug] PUT    /api/v1/users/:id         --> aiops/control_plane/internal/controller/http.(*UserController).UpdateUser-fm (5 handlers)
[GIN-debug] DELETE /api/v1/users/:id         --> aiops/control_plane/internal/controller/http.(*UserController).DeleteUser-fm (5 handlers)
[GIN-debug] POST   /api/v1/users/:id/password --> aiops/control_plane/internal/controller/http.(*UserController).ChangePassword-fm (5 handlers)
[GIN-debug] GET    /api/v1/pipelines         --> aiops/control_plane/internal/controller/http.(*PipelineController).GetAllPipelines-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines         --> aiops/control_plane/internal/controller/http.(*PipelineController).CreatePipeline-fm (4 handlers)
[GIN-debug] GET    /api/v1/pipelines/:id     --> aiops/control_plane/internal/controller/http.(*PipelineController).GetPipeline-fm (4 handlers)
[GIN-debug] PUT    /api/v1/pipelines/:id     --> aiops/control_plane/internal/controller/http.(*PipelineController).UpdatePipeline-fm (4 handlers)
[GIN-debug] DELETE /api/v1/pipelines/:id     --> aiops/control_plane/internal/controller/http.(*PipelineController).DeletePipeline-fm (4 handlers)
[GIN-debug] GET    /api/v1/pipelines/:id/status --> aiops/control_plane/internal/controller/http.(*PipelineController).GetPipelineStatus-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/:id/start --> aiops/control_plane/internal/controller/http.(*PipelineController).StartPipeline-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/:id/stop --> aiops/control_plane/internal/controller/http.(*PipelineController).StopPipeline-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/:id/restart --> aiops/control_plane/internal/controller/http.(*PipelineController).RestartPipeline-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/:id/reload --> aiops/control_plane/internal/controller/http.(*PipelineController).ReloadPipeline-fm (4 handlers)
[GIN-debug] GET    /api/v1/pipelines/agent/:agentId --> aiops/control_plane/internal/controller/http.(*PipelineController).GetPipelinesByAgent-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/agent/:agentId/start/:pipelineId --> aiops/control_plane/internal/controller/http.(*PipelineController).StartPipelineOnAgent-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/agent/:agentId/stop/:pipelineId --> aiops/control_plane/internal/controller/http.(*PipelineController).StopPipelineOnAgent-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/agent/:agentId/deploy --> aiops/control_plane/internal/controller/http.(*PipelineController).DeployPipelineToAgent-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/batch/start --> aiops/control_plane/internal/controller/http.(*PipelineController).BatchStartPipelines-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/batch/stop --> aiops/control_plane/internal/controller/http.(*PipelineController).BatchStopPipelines-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/batch/deploy --> aiops/control_plane/internal/controller/http.(*PipelineController).BatchDeployPipelines-fm (4 handlers)
[GIN-debug] GET    /api/v1/pipelines/templates --> aiops/control_plane/internal/controller/http.(*PipelineController).GetPipelineTemplates-fm (4 handlers)
[GIN-debug] POST   /api/v1/pipelines/validate --> aiops/control_plane/internal/controller/http.(*PipelineController).ValidatePipelineConfig-fm (4 handlers)
[GIN-debug] GET    /api/v1/logs              --> aiops/control_plane/internal/controller/http.(*LogController).GetLogs-fm (4 handlers)
[GIN-debug] GET    /api/v1/logs/devices/:deviceId --> aiops/control_plane/internal/controller/http.(*LogController).GetDeviceLogs-fm (4 handlers)
[GIN-debug] DELETE /api/v1/logs/cleanup      --> aiops/control_plane/internal/controller/http.(*LogController).CleanupLogs-fm (5 handlers)
[GIN-debug] GET    /api/v1/supported-metrics --> aiops/control_plane/internal/controller/http.(*SupportedMetricController).GetAllSupportedMetrics-fm (4 handlers)
[GIN-debug] POST   /api/v1/supported-metrics --> aiops/control_plane/internal/controller/http.(*SupportedMetricController).CreateSupportedMetric-fm (4 handlers)
[GIN-debug] GET    /api/v1/supported-metrics/:id --> aiops/control_plane/internal/controller/http.(*SupportedMetricController).GetSupportedMetric-fm (4 handlers)
[GIN-debug] PUT    /api/v1/supported-metrics/:id --> aiops/control_plane/internal/controller/http.(*SupportedMetricController).UpdateSupportedMetric-fm (4 handlers)
[GIN-debug] DELETE /api/v1/supported-metrics/:id --> aiops/control_plane/internal/controller/http.(*SupportedMetricController).DeleteSupportedMetric-fm (4 handlers)
2025-05-27 16:45:11.655999000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-27 16:45:11.656010000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已启动
2025-05-27 16:45:11.656019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:119	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-27 16:45:11.656027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-27 16:45:11.656035000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	按 Ctrl+C 退出
2025-05-27 16:45:19.227697000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "RegisterAgent", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "agentIP": "127.0.0.1"}
2025-05-27 16:45:19.250355000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "2.208ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"1b974dfb-f795-43d5-85c1-778cec3a64ef\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:250
aiops/control_plane/internal/controller/grpc.(*AgentController).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/agent.go:37
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:120
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:303
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-27 16:45:19.250443000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:259	注册新 Agent	{"AgentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef", "IP": "127.0.0.1", "支持的采集器类型": ["system", "mysql", "redis", "http"]}
2025-05-27 16:45:19.284725000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:272	新 Agent 注册成功	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307093000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "Agent连接到流水线流", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307166000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:425	Registered agent command queue	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.307176000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注册Agent命令队列", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:19.314542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线管理命令发送器已启动", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.359528000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:129	正在关闭服务...
2025-05-27 16:45:20.359669000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:109	接收到停止信号，退出心跳监控循环
2025-05-27 16:45:20.359717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:96	Agent心跳监控已停止
2025-05-27 16:45:20.359775000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:111	正在停止 HTTP API 服务器
2025-05-27 16:45:20.359893000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:111	正在停止 gRPC 服务器
2025-05-27 16:45:20.360027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "流水线stream context已完成", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.360031000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25	gRPC operation failed	{"operation": "接收流水线状态", "error": "rpc error: code = Canceled desc = context canceled", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
aiops/control_plane/internal/controller/grpc.(*BaseController).LogError
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:25
aiops/control_plane/internal/controller/grpc.(*PipelineController).StreamPipelines.func2
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/pipeline.go:95
2025-05-27 16:45:20.383407000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/pipeline_service.go:437	Unregistered agent command queue	{"agent_id": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383428000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "注销Agent命令队列", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383437000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:591	Agent断开连接	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.383679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:612	Agent状态已更新为离线	{"agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.384617000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/controller/grpc/base.go:32	gRPC operation	{"operation": "通知Agent断连", "agentID": "1b974dfb-f795-43d5-85c1-778cec3a64ef"}
2025-05-27 16:45:20.384720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:142	DevInsight Control Plane 已关闭
