pipeline_id: "test_pipeline"
name: "测试流水线"
description: "这是一个用于测试的系统指标采集流水线"
version: "1.0.0"
enabled: true

# 基础配置
buffer_size: 100
worker_count: 2
timeout_seconds: 30
retry_attempts: 3
retry_delay_seconds: 5

# 采集器配置（使用系统指标收集器）
collector:
  name: "system_collector"
  type: "system"
  interval_seconds: 10
  enabled: true
  config:
    collect_cpu: "true"
    collect_memory: "true"
    collect_disk: "true"
    collect_network: "true"

# 处理器配置（使用阈值处理器）
processors:
  - name: "cpu_threshold"
    type: "threshold"
    enabled: true
    order: 1
    config:
      metric_key: "cpu_usage_percent"
      threshold: "80"
      operator: ">"
      alert_message: "CPU使用率超过80%"
  
  - name: "memory_threshold"
    type: "threshold"
    enabled: true
    order: 2
    config:
      metric_key: "memory_usage_percent"
      threshold: "90"
      operator: ">"
      alert_message: "内存使用率超过90%"

# 监控配置
enable_metrics: true
enable_tracing: false
metrics_interval_seconds: 30

# 错误处理配置
error_handling:
  strategy: "retry"
  max_retries: 3
  retry_delay_seconds: 5

# 资源限制
resource_limits:
  max_cpu_percent: 50
  max_memory_mb: 100
