plugin:
  # 插件系统配置
  enabled: true

  # 插件目录配置
  plugin_dirs:
    - "plugins/build"
    - "plugins/builtin"
    - "plugins/external"
    - "plugins"

  # 自动加载配置
  auto_load: true

  # 监控模式 (开发时可启用文件变化监控)
  watch_mode: false

  # 插件默认配置
  defaults:
    # 采集器插件默认配置
    collector:
      timeout: 30s
      retry_count: 3
      retry_interval: 5s
      max_concurrent_tasks: 10

    # 处理器插件默认配置
    processor:
      batch_size: 100
      flush_interval: 10s
      max_queue_size: 1000

    # 告警器插件默认配置
    alerter:
      timeout: 15s
      retry_count: 3
      rate_limit: 10

    # 分析器插件默认配置
    analyzer:
      window_size: 1000
      confidence_threshold: 0.8
      anomaly_threshold: 2.0

  # 内置插件配置
  builtin_plugins:
    # 采集器插件
    - name: "system-collector"
      version: "1.0.0"
      type: "collector"
      enabled: true
      path: "./plugins/build/system-collector.so"
      config:
        collect_interval: 60s
        metrics:
          - "cpu.usage"
          - "memory.usage"
          - "disk.usage"
          - "network.io"

    - name: "mysql-collector"
      version: "1.0.0"
      type: "collector"
      enabled: false
      path: "./plugins/build/mysql-collector.so"
      config:
        collect_interval: 30s
        connection_pool_size: 5
        timeout: 10s
        host: "localhost"
        port: 3306
        username: "monitor"
        password: "${MYSQL_PASSWORD}"

    # 告警器插件
    - name: "email-alerter"
      version: "1.0.0"
      type: "alerter"
      enabled: true
      path: "./plugins/build/email-alerter.so"
      config:
        smtp_server: "${SMTP_SERVER:-smtp.gmail.com}"
        smtp_port: 587
        username: "${EMAIL_USERNAME}"
        password: "${EMAIL_PASSWORD}"
        from: "${EMAIL_FROM}"
        to:
          - "${EMAIL_TO}"

    # 分析器插件
    - name: "simple-analyzer"
      version: "1.0.0"
      type: "analyzer"
      enabled: true
      path: "./plugins/build/simple-analyzer.so"
      config:
        window_size: 100
        threshold: 2.0
        analysis_interval: 300s

    # 活动分析器插件
    - name: "activity-analyzer"
      version: "1.0.0"
      type: "analyzer"
      enabled: true
      path: "./plugins/build/activity-analyzer.so"
      config:
        max_history_size: 1000
        analysis:
          algorithm: "statistical_anomaly"
          sensitivity: 0.05
          thresholds:
            large_activity_threshold: 10000
            immediate_start_threshold: 300
            z_score_threshold: 2.0
            iqr_multiplier: 1.5
          parameters:
            enable_z_score: true
            enable_iqr: true
            enable_pattern_match: true

    # 业务数据采集器插件
    - name: "business-collector"
      version: "1.0.0"
      type: "collector"
      enabled: true
      path: "./plugins/build/business-collector.so"
      config:
        interval: "30s"
        enable_activity: true
        enable_user: true
        activity_count: 5

  # 安全配置
  security:
    # 是否启用插件签名验证
    verify_signature: false

    # 可信任的插件发布者
    trusted_publishers:
      - "devinsight-official"
      - "community-verified"

    # 插件沙箱配置
    sandbox:
      enabled: false
      resource_limits:
        max_memory: "128MB"
        max_cpu: "0.5"
        max_disk: "1GB"
        max_network_connections: 10

  # 插件市场配置 (未来功能)
  marketplace:
    enabled: false
    api_endpoint: "https://plugins.devinsight.io/api/v1"
    auto_update: false
    update_check_interval: "24h"

  # 日志配置
  logging:
    level: "info"
    file: "./logs/plugins.log"
    max_size: "100MB"
    max_backups: 5
    max_age: 30

  # 性能监控
  monitoring:
    enabled: true
    metrics_interval: 60s
    health_check_interval: 30s
    performance_profiling: false
