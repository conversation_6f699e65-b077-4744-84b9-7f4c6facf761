# 活动分析流水线配置示例
# 这个流水线演示了如何使用数据分析插件系统进行活动异常检测

pipeline:
  id: "activity_analysis_pipeline"
  name: "Activity Analysis Pipeline"
  description: "Pipeline for analyzing business activity data and detecting anomalies"
  version: "1.0.0"
  enabled: true
  
  # 基础配置
  buffer_size: 1000
  worker_count: 2
  timeout_seconds: 60
  retry_attempts: 3
  retry_delay_seconds: 5
  
  # 监控配置
  enable_metrics: true
  enable_tracing: true
  metrics_interval_seconds: 30
  
  # 采集器配置
  collector:
    name: "business-collector"
    type: "collector"
    enabled: true
    config:
      interval: "30s"
      enable_activity: true
      enable_user: false  # 只关注活动数据
      activity_count: 10  # 每次生成10个活动用于测试
      
  # 处理器链路配置
  processors:
    # 活动分析器
    - name: "activity-analyzer"
      type: "analyzer"
      enabled: true
      order: 1
      config:
        max_history_size: 500
        analysis:
          algorithm: "statistical_anomaly"
          sensitivity: 0.05
          training_window: "168h"  # 7天训练窗口
          update_interval: "1h"
          features: ["time_to_start", "participant_count"]
          thresholds:
            large_activity_threshold: 5000   # 降低阈值用于测试
            immediate_start_threshold: 600   # 10分钟
            z_score_threshold: 2.0
            iqr_multiplier: 1.5
          parameters:
            enable_z_score: true
            enable_iqr: true
            enable_pattern_match: true
          enable_learning: true
          
    # 邮件告警器
    - name: "email-alerter"
      type: "alerter"
      enabled: true
      order: 2
      config:
        smtp_server: "smtp.gmail.com"
        smtp_port: 587
        username: "${EMAIL_USERNAME}"
        password: "${EMAIL_PASSWORD}"
        from: "${EMAIL_FROM}"
        recipients:
          - "<EMAIL>"
          - "<EMAIL>"
        templates:
          activity_anomaly: |
            Subject: 🚨 Activity Anomaly Detected
            
            An anomaly has been detected in activity data:
            
            Activity ID: {{.activity_id}}
            Anomaly Type: {{.anomaly_type}}
            Severity: {{.severity}}
            Description: {{.description}}
            Detection Time: {{.detection_time}}
            
            Please investigate immediately.
          security_alert: |
            Subject: 🔒 Security Alert - Suspicious Activity Pattern
            
            A suspicious activity pattern has been detected:
            
            Activity ID: {{.activity_id}}
            Pattern: {{.pattern_type}}
            Severity: {{.severity}}
            Description: {{.description}}
            
            This may indicate a security incident. Please review immediately.
            
  # 错误处理配置
  error_handling:
    strategy: "continue"  # continue, stop, retry
    max_errors: 10
    error_window: "5m"
    dead_letter_queue: true
    
  # 资源限制配置
  resource_limits:
    max_memory: "256MB"
    max_cpu: "0.5"
    max_disk: "1GB"
    max_network_connections: 50
    
  # 数据保留配置
  data_retention:
    raw_data_days: 7
    processed_data_days: 30
    anomaly_data_days: 90
    alert_data_days: 180
