package analysis

import (
	"context"
	"time"
)

// AnalysisEngine 分析引擎接口
type AnalysisEngine interface {
	// 启动分析引擎
	Start(ctx context.Context) error
	// 停止分析引擎
	Stop() error
	// 注册分析插件
	RegisterPlugin(plugin AnalysisPlugin) error
	// 执行分析
	Analyze(ctx context.Context, request *AnalysisRequest) (*AnalysisResult, error)
}

// AnalysisPlugin 分析插件接口
type AnalysisPlugin interface {
	// 插件元信息
	Name() string
	Version() string
	Description() string

	// 插件生命周期
	Initialize(config map[string]interface{}) error
	Start(ctx context.Context) error
	Stop() error

	// 分析能力
	Analyze(ctx context.Context, data *AnalysisData) (*AnalysisResult, error)

	// 配置能力
	GetConfigSchema() *ConfigSchema
	ValidateConfig(config map[string]interface{}) error
}

// AnalysisRequest 分析请求
type AnalysisRequest struct {
	RequestID  string                 `json:"request_id"`
	Type       AnalysisType           `json:"type"`
	DeviceID   string                 `json:"device_id,omitempty"`
	MetricKeys []string               `json:"metric_keys,omitempty"`
	TimeRange  TimeRange              `json:"time_range"`
	Plugins    []string               `json:"plugins,omitempty"` // 指定使用的插件
	Config     map[string]interface{} `json:"config,omitempty"`
}

// AnalysisData 分析数据
type AnalysisData struct {
	Type      AnalysisType           `json:"type"`
	DeviceID  string                 `json:"device_id"`
	Metrics   []*MetricPoint         `json:"metrics"`
	Events    []*EventPoint          `json:"events"`
	Logs      []*LogPoint            `json:"logs"`
	TimeRange TimeRange              `json:"time_range"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	RequestID       string                 `json:"request_id"`
	PluginName      string                 `json:"plugin_name"`
	Type            AnalysisType           `json:"type"`
	DeviceID        string                 `json:"device_id"`
	Status          AnalysisStatus         `json:"status"`
	Score           float64                `json:"score"`      // 异常评分 0-1
	Confidence      float64                `json:"confidence"` // 置信度 0-1
	Anomalies       []*Anomaly             `json:"anomalies"`
	Insights        []*Insight             `json:"insights"`
	Predictions     []*Prediction          `json:"predictions"`
	Correlations    []*Correlation         `json:"correlations"`
	Recommendations []*Recommendation      `json:"recommendations"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
	ProcessingTime  time.Duration          `json:"processing_time"`
}

// AnalysisType 分析类型
type AnalysisType string

const (
	AnalysisTypeAnomalyDetection AnalysisType = "anomaly_detection"
	AnalysisTypeTrendAnalysis    AnalysisType = "trend_analysis"
	AnalysisTypeRootCause        AnalysisType = "root_cause"
	AnalysisTypeCorrelation      AnalysisType = "correlation"
	AnalysisTypePrediction       AnalysisType = "prediction"
	AnalysisTypeCapacityPlanning AnalysisType = "capacity_planning"
)

// AnalysisStatus 分析状态
type AnalysisStatus string

const (
	AnalysisStatusSuccess    AnalysisStatus = "success"
	AnalysisStatusFailed     AnalysisStatus = "failed"
	AnalysisStatusProcessing AnalysisStatus = "processing"
	AnalysisStatusSkipped    AnalysisStatus = "skipped"
)

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// MetricPoint 指标数据点
type MetricPoint struct {
	MetricKey string            `json:"metric_key"`
	Value     float64           `json:"value"`
	Timestamp time.Time         `json:"timestamp"`
	Labels    map[string]string `json:"labels,omitempty"`
}

// EventPoint 事件数据点
type EventPoint struct {
	EventType string                 `json:"event_type"`
	Severity  string                 `json:"severity"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// LogPoint 日志数据点
type LogPoint struct {
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// Anomaly 异常
type Anomaly struct {
	ID          string    `json:"id"`
	MetricKey   string    `json:"metric_key"`
	Value       float64   `json:"value"`
	Expected    float64   `json:"expected"`
	Deviation   float64   `json:"deviation"`
	Severity    string    `json:"severity"` // low, medium, high, critical
	Timestamp   time.Time `json:"timestamp"`
	Description string    `json:"description"`
	Algorithm   string    `json:"algorithm"`
}

// Insight 洞察
type Insight struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Impact      string                 `json:"impact"`
	Confidence  float64                `json:"confidence"`
	Evidence    map[string]interface{} `json:"evidence"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Prediction 预测
type Prediction struct {
	ID             string                 `json:"id"`
	MetricKey      string                 `json:"metric_key"`
	PredictedValue float64                `json:"predicted_value"`
	Confidence     float64                `json:"confidence"`
	TimeHorizon    time.Duration          `json:"time_horizon"`
	PredictedAt    time.Time              `json:"predicted_at"`
	Algorithm      string                 `json:"algorithm"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// Correlation 关联关系
type Correlation struct {
	ID           string        `json:"id"`
	MetricKey1   string        `json:"metric_key1"`
	MetricKey2   string        `json:"metric_key2"`
	Coefficient  float64       `json:"coefficient"`          // 相关系数 -1 到 1
	Significance float64       `json:"significance"`         // 显著性
	Relationship string        `json:"relationship"`         // positive, negative, none
	TimeDelay    time.Duration `json:"time_delay,omitempty"` // 时间延迟
	Algorithm    string        `json:"algorithm"`
}

// Recommendation 建议
type Recommendation struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"` // optimization, alert, maintenance
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	Priority     string                 `json:"priority"` // low, medium, high, urgent
	Impact       string                 `json:"impact"`
	Effort       string                 `json:"effort"` // low, medium, high
	Actions      []string               `json:"actions"`
	ExpectedGain map[string]interface{} `json:"expected_gain,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
}

// ConfigSchema 配置模式
type ConfigSchema struct {
	Properties map[string]*PropertySchema `json:"properties"`
	Required   []string                   `json:"required"`
}

// PropertySchema 属性模式
type PropertySchema struct {
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Default     interface{} `json:"default,omitempty"`
	Minimum     *float64    `json:"minimum,omitempty"`
	Maximum     *float64    `json:"maximum,omitempty"`
	Enum        []string    `json:"enum,omitempty"`
}
