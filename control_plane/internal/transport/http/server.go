package http

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"aiops/control_plane/config"
	httpController "aiops/control_plane/internal/controller/http"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server 是 HTTP API 服务器的实现
type Server struct {
	config     *config.Config
	router     *gin.Engine
	httpServer *http.Server
	logger     *log.Logger

	// Controllers
	agentController           *httpController.AgentController
	deviceController          *httpController.DeviceController
	taskController            *httpController.TaskController
	metricController          *httpController.MetricController
	alertController           *httpController.AlertController
	userController            *httpController.UserController
	pipelineController        *httpController.PipelineController
	authController            *httpController.AuthController
	logController             *httpController.LogController
	supportedMetricController *httpController.SupportedMetricController
}

// NewServer 创建一个新的 HTTP API 服务器
func NewServer(
	config *config.Config,
	agentService *service.AgentService,
	deviceService *service.DeviceService,
	taskService *service.CollectorTaskService,
	metricService *service.MetricService,
	alertService *service.AlertService,
	userService *service.UserService,
	logService service.LogService,
	supportedMetricService service.SupportedMetricService,
	pipelineService *service.PipelineService,
	logger *log.Logger,
) *Server {
	router := gin.New()
	gin.SetMode(gin.DebugMode)
	router.Use(gin.Recovery())

	// 创建控制器
	agentController := httpController.NewAgentController(agentService, logger)
	deviceController := httpController.NewDeviceController(deviceService, logger)
	taskController := httpController.NewTaskController(taskService, logger)
	metricController := httpController.NewMetricController(metricService, logger)
	alertController := httpController.NewAlertController(alertService, logger)
	userController := httpController.NewUserController(userService, logger)
	pipelineController := httpController.NewPipelineController(pipelineService, logger)
	authController := httpController.NewAuthController(userService, logger)
	logController := httpController.NewLogController(logService, logger)
	supportedMetricController := httpController.NewSupportedMetricController(supportedMetricService, logger)

	server := &Server{
		config:                    config,
		router:                    router,
		logger:                    logger,
		agentController:           agentController,
		deviceController:          deviceController,
		taskController:            taskController,
		metricController:          metricController,
		alertController:           alertController,
		userController:            userController,
		pipelineController:        pipelineController,
		authController:            authController,
		logController:             logController,
		supportedMetricController: supportedMetricController,
	}

	server.setupRoutes()

	return server
}

// Start 启动 HTTP API 服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("0.0.0.0:%d", s.config.HTTPPort)
	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.router,
	}

	s.logger.Info("启动 HTTP API 服务器", zap.String("监听地址", addr))

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP 服务器异常退出", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止 HTTP API 服务器
func (s *Server) Stop() error {
	if s.httpServer != nil {
		s.logger.Info("正在停止 HTTP API 服务器")

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		return s.httpServer.Shutdown(ctx)
	}
	return nil
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 健康检查
	s.router.GET("/health", s.handleHealth)

	// API 路由组
	api := s.router.Group("/api/v1")
	api.Use(s.corsMiddleware())

	// 认证路由 - 不需要认证
	auth := api.Group("/auth")
	{
		auth.POST("/login", s.authController.Login)
		auth.POST("/logout", s.authController.Logout)
		auth.POST("/refresh", s.authController.RefreshToken)
	}

	// 需要认证的路由
	authenticated := api.Group("")
	authenticated.Use(s.authMiddleware())
	{
		// Agent 管理
		agents := authenticated.Group("/agents")
		{
			agents.GET("", s.agentController.GetAllAgents)
			agents.GET("/:id", s.agentController.GetAgent)
			agents.DELETE("/:id", s.agentController.DeleteAgent)
			agents.PUT("/:id/status", s.agentController.UpdateAgentStatus)
		}

		// 设备管理
		devices := authenticated.Group("/devices")
		{
			devices.GET("", s.deviceController.GetAllDevices)
			devices.POST("", s.deviceController.CreateDevice)
			devices.GET("/:id", s.deviceController.GetDevice)
			devices.PUT("/:id", s.deviceController.UpdateDevice)
			devices.DELETE("/:id", s.deviceController.DeleteDevice)
		}

		// 任务管理
		tasks := authenticated.Group("/tasks")
		{
			tasks.GET("", s.taskController.GetAllTasks)
			tasks.POST("", s.taskController.CreateTask)
			tasks.GET("/:id", s.taskController.GetTask)
			tasks.PUT("/:id", s.taskController.UpdateTask)
			tasks.DELETE("/:id", s.taskController.DeleteTask)
			tasks.POST("/:id/start", s.taskController.StartTask)
			tasks.POST("/:id/stop", s.taskController.StopTask)
		}

		// 指标管理
		metrics := authenticated.Group("/metrics")
		{
			metrics.GET("", s.metricController.GetMetrics)
			metrics.GET("/devices/:deviceId", s.metricController.GetDeviceMetrics)
			metrics.GET("/summary", s.metricController.GetMetricsSummary)
		}

		// 告警管理
		alerts := authenticated.Group("/alerts")
		{
			alerts.GET("", s.alertController.GetAllAlerts)
			alerts.GET("/rules", s.alertController.GetAllAlertRules)
			alerts.POST("/rules", s.alertController.CreateAlertRule)
			alerts.GET("/rules/:id", s.alertController.GetAlertRule)
			alerts.PUT("/rules/:id", s.alertController.UpdateAlertRule)
			alerts.DELETE("/rules/:id", s.alertController.DeleteAlertRule)
		}

		// 用户管理
		users := authenticated.Group("/users")
		{
			users.GET("", s.adminOnly, s.userController.GetAllUsers)
			users.POST("", s.adminOnly, s.userController.CreateUser)
			users.GET("/:id", s.userOwnOrAdmin, s.userController.GetUser)
			users.PUT("/:id", s.userOwnOrAdmin, s.userController.UpdateUser)
			users.DELETE("/:id", s.adminOnly, s.userController.DeleteUser)
			users.POST("/:id/password", s.userOwnOrAdmin, s.userController.ChangePassword)
		}

		// 流水线管理
		if s.pipelineController != nil {
			pipelines := authenticated.Group("/pipelines")
			{
				// 基本 CRUD 操作
				pipelines.GET("", s.pipelineController.GetAllPipelines)
				pipelines.POST("", s.pipelineController.CreatePipeline)
				pipelines.GET("/:id", s.pipelineController.GetPipeline)
				pipelines.PUT("/:id", s.pipelineController.UpdatePipeline)
				pipelines.DELETE("/:id", s.pipelineController.DeletePipeline)

				// 流水线状态管理
				pipelines.GET("/:id/status", s.pipelineController.GetPipelineStatus)
				pipelines.POST("/:id/start", s.pipelineController.StartPipeline)
				pipelines.POST("/:id/stop", s.pipelineController.StopPipeline)
				pipelines.POST("/:id/restart", s.pipelineController.RestartPipeline)
				pipelines.POST("/:id/reload", s.pipelineController.ReloadPipeline)

				// Agent 相关的流水线操作
				pipelines.GET("/agent/:agentId", s.pipelineController.GetPipelinesByAgent)
				pipelines.POST("/agent/:agentId/start/:pipelineId", s.pipelineController.StartPipelineOnAgent)
				pipelines.POST("/agent/:agentId/stop/:pipelineId", s.pipelineController.StopPipelineOnAgent)
				pipelines.POST("/agent/:agentId/deploy", s.pipelineController.DeployPipelineToAgent)

				// 批量操作
				pipelines.POST("/batch/start", s.pipelineController.BatchStartPipelines)
				pipelines.POST("/batch/stop", s.pipelineController.BatchStopPipelines)
				pipelines.POST("/batch/deploy", s.pipelineController.BatchDeployPipelines)

				// 模板和验证
				pipelines.GET("/templates", s.pipelineController.GetPipelineTemplates)
				pipelines.POST("/validate", s.pipelineController.ValidatePipelineConfig)
			}
		}

		// 日志管理
		logs := authenticated.Group("/logs")
		{
			logs.GET("", s.logController.GetLogs)
			logs.GET("/devices/:deviceId", s.logController.GetDeviceLogs)
			logs.DELETE("/cleanup", s.adminOnly, s.logController.CleanupLogs)
		}

		// 支持的指标管理
		supportedMetrics := authenticated.Group("/supported-metrics")
		{
			supportedMetrics.GET("", s.supportedMetricController.GetAllSupportedMetrics)
			supportedMetrics.POST("", s.supportedMetricController.CreateSupportedMetric)
			supportedMetrics.GET("/:id", s.supportedMetricController.GetSupportedMetric)
			supportedMetrics.PUT("/:id", s.supportedMetricController.UpdateSupportedMetric)
			supportedMetrics.DELETE("/:id", s.supportedMetricController.DeleteSupportedMetric)
		}
	}
}

// handleHealth 健康检查
func (s *Server) handleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
	})
}

// corsMiddleware CORS 中间件
func (s *Server) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, Authorization, X-CSRF-Token")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// authMiddleware 认证中间件
func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现真正的JWT验证
		// 目前使用简化的认证逻辑
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "未提供认证token",
			})
			c.Abort()
			return
		}

		// 设置用户信息
		c.Set("userId", uint(1))
		c.Set("role", "admin")

		c.Next()
	}
}

// adminOnly 管理员权限中间件
func (s *Server) adminOnly(c *gin.Context) {
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "需要管理员权限",
		})
		c.Abort()
		return
	}
	c.Next()
}

// userOwnOrAdmin 用户自己或管理员权限中间件
func (s *Server) userOwnOrAdmin(c *gin.Context) {
	userID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效的用户信息",
		})
		c.Abort()
		return
	}

	role, _ := c.Get("role")
	paramUserID := c.Param("id")

	// 管理员可以访问所有用户信息
	if role == "admin" {
		c.Next()
		return
	}

	// 普通用户只能访问自己的信息
	if fmt.Sprintf("%v", userID) == paramUserID {
		c.Next()
		return
	}

	c.JSON(http.StatusForbidden, gin.H{
		"success": false,
		"message": "无权访问该资源",
	})
	c.Abort()
}
