package grpc

import (
	"context"
	"fmt"
	"net"
	"time"

	"aiops/control_plane/config"
	grpcController "aiops/control_plane/internal/controller/grpc"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
)

// Server 是 gRPC 服务器的实现
type Server struct {
	pb.UnimplementedAgentServiceServer
	config     *config.Config
	logger     *log.Logger
	grpcServer *grpc.Server

	// Controllers
	agentController    *grpcController.AgentController
	metricController   *grpcController.MetricController
	logController      *grpcController.LogController
	pipelineController *grpcController.PipelineController
}

// NewServer 创建一个新的 gRPC 服务器
func NewServer(
	config *config.Config,
	agentService *service.AgentService,
	metricService *service.MetricService,
	logService service.LogService,
	supportedMetricService service.SupportedMetricService,
	pipelineService *service.PipelineService,
	logger *log.Logger,
) *Server {
	// 创建控制器
	agentController := grpcController.NewAgentController(agentService, logger)
	metricController := grpcController.NewMetricController(metricService, logger)
	logController := grpcController.NewLogController(logService, logger)
	pipelineController := grpcController.NewPipelineController(pipelineService, agentService, logger)

	return &Server{
		config:             config,
		logger:             logger,
		agentController:    agentController,
		metricController:   metricController,
		logController:      logController,
		pipelineController: pipelineController,
	}
}

// Start 启动 gRPC 服务器
func (s *Server) Start() error {
	// 创建监听器
	addr := fmt.Sprintf("0.0.0.0:%d", s.config.GRPCPort)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen: %v", err)
	}

	// 创建 gRPC 服务器，设置最大消息大小为 50MB 以支持插件分发
	maxMsgSize := 50 * 1024 * 1024 // 50MB
	s.grpcServer = grpc.NewServer(
		grpc.MaxRecvMsgSize(maxMsgSize),
		grpc.MaxSendMsgSize(maxMsgSize),
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     15 * time.Minute, // 如果连接空闲超过15分钟，发送GoAway
			MaxConnectionAge:      2 * time.Hour,    // 如果连接存在超过2小时，发送GoAway (减少重连频率)
			MaxConnectionAgeGrace: 5 * time.Second,  // 强制关闭连接前等待的时间
			Time:                  30 * time.Second, // 如果客户端超过30秒没有活动，发送ping
			Timeout:               5 * time.Second,  // 等待ping响应的时间
		}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             30 * time.Second, // 客户端两次ping之间的最小间隔
			PermitWithoutStream: true,             // 允许客户端在没有流时发送ping
		}),
	)

	// 注册服务
	pb.RegisterAgentServiceServer(s.grpcServer, s)

	// 注册反射服务，方便测试
	reflection.Register(s.grpcServer)

	// 启动服务
	s.logger.Info("启动 gRPC 服务器", zap.String("监听地址", addr))
	go func() {
		if err := s.grpcServer.Serve(lis); err != nil {
			s.logger.Error("gRPC 服务器异常退出", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止 gRPC 服务器
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.logger.Info("正在停止 gRPC 服务器")
		s.grpcServer.GracefulStop()
	}
}

// ==================== gRPC 方法实现 ====================

// RegisterAgent 实现 gRPC RegisterAgent 方法
func (s *Server) RegisterAgent(ctx context.Context, req *pb.RegisterAgentRequest) (*pb.RegisterAgentResponse, error) {
	return s.agentController.RegisterAgent(ctx, req)
}

// Heartbeat 实现 gRPC Heartbeat 方法
func (s *Server) Heartbeat(ctx context.Context, req *pb.HeartbeatRequest) (*pb.HeartbeatResponse, error) {
	return s.agentController.Heartbeat(ctx, req)
}

// StreamCollectorTasks 实现 AgentService 的 StreamCollectorTasks 方法
func (s *Server) StreamCollectorTasks(stream pb.AgentService_StreamCollectorTasksServer) error {
	// 暂时返回未实现错误，等待AgentController实现该方法
	return status.Error(codes.Unimplemented, "StreamCollectorTasks功能暂未实现")
}

// StreamMetricData 实现 AgentService 的 StreamMetricData 方法
func (s *Server) StreamMetricData(stream pb.AgentService_StreamMetricDataServer) error {
	return s.metricController.StreamMetricData(stream)
}

// StreamLogData 实现 AgentService 的 StreamLogData 方法
func (s *Server) StreamLogData(stream pb.AgentService_StreamLogDataServer) error {
	return s.logController.StreamLogData(stream)
}

// StreamPipelines 处理流水线管理命令和状态的双向流
func (s *Server) StreamPipelines(stream pb.AgentService_StreamPipelinesServer) error {
	return s.pipelineController.StreamPipelines(stream)
}

// GetPipelineTemplates 获取流水线模板
func (s *Server) GetPipelineTemplates(ctx context.Context, req *pb.PipelineTemplateRequest) (*pb.PipelineTemplateResponse, error) {
	return s.pipelineController.GetPipelineTemplates(ctx, req)
}

// ValidatePipelineConfig 验证流水线配置
func (s *Server) ValidatePipelineConfig(ctx context.Context, req *pb.PipelineConfigValidationRequest) (*pb.PipelineConfigValidationResponse, error) {
	return s.pipelineController.ValidatePipelineConfig(ctx, req)
}

// ==================== TODO: 实现剩余的gRPC方法 ====================

// RequestPlugin 用于 Agent 请求特定的插件
func (s *Server) RequestPlugin(ctx context.Context, req *pb.PluginRequest) (*pb.PluginResponse, error) {
	// TODO: 实现插件请求逻辑
	s.logger.Info("收到插件请求", zap.String("agentID", req.AgentId), zap.String("pluginName", req.PluginName))

	return &pb.PluginResponse{
		Success: false,
		Message: "插件请求功能暂未实现",
	}, nil
}

// StreamPluginUpdates 是服务器流，用于 Control Plane 推送插件更新通知
func (s *Server) StreamPluginUpdates(req *emptypb.Empty, stream pb.AgentService_StreamPluginUpdatesServer) error {
	// TODO: 实现插件更新推送逻辑
	s.logger.Info("Agent请求插件更新流")

	// 暂时返回错误，表示功能未实现
	return status.Error(codes.Unimplemented, "插件更新推送功能暂未实现")
}

// ReportPluginStatus 用于 Agent 上报插件状态
func (s *Server) ReportPluginStatus(ctx context.Context, req *pb.PluginStatusReport) (*pb.PluginStatusResponse, error) {
	// TODO: 实现插件状态上报逻辑
	s.logger.Info("收到插件状态上报", zap.String("agentID", req.AgentId))

	return &pb.PluginStatusResponse{
		Success: false,
		Message: "插件状态上报功能暂未实现",
	}, nil
}
