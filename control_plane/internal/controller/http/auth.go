package http

import (
	"time"

	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AuthController 认证控制器
type AuthController struct {
	*BaseController
	userService *service.UserService
}

// NewAuthController 创建认证控制器
func NewAuthController(userService *service.UserService, logger *log.Logger) *AuthController {
	return &AuthController{
		BaseController: NewBaseController(logger),
		userService:    userService,
	}
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// Login 用户登录
func (ac *AuthController) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证用户凭据
	user, err := ac.userService.ValidateUser(req.Username, req.Password)
	if err != nil {
		ac.logger.Error("用户登录失败", zap.String("username", req.Username), zap.Error(err))
		ac.UnauthorizedResponse(c, "用户名或密码错误")
		return
	}

	// 生成token
	token, err := ac.userService.GenerateToken(user)
	if err != nil {
		ac.logger.Error("生成token失败", zap.Error(err))
		ac.InternalErrorResponse(c, err)
		return
	}

	// 返回登录成功响应
	data := gin.H{
		"token":    token,
		"userId":   user.ID,
		"username": user.Username,
		"role":     user.Role,
	}

	ac.SuccessResponse(c, "登录成功", data)
}

// Logout 用户登出
func (ac *AuthController) Logout(c *gin.Context) {
	// TODO: 实现token失效逻辑（如果使用黑名单机制）
	ac.SuccessResponse(c, "登出成功", nil)
}

// RefreshToken 刷新token
func (ac *AuthController) RefreshToken(c *gin.Context) {
	// TODO: 实现token刷新逻辑
	token := c.GetHeader("Authorization")
	if token == "" {
		ac.UnauthorizedResponse(c, "未提供认证token")
		return
	}

	// 简化实现，实际应该验证旧token并生成新token
	data := gin.H{
		"token": "new_token_here",
	}
	ac.SuccessResponse(c, "Token刷新成功", data)
}

// Health 健康检查
func (ac *AuthController) Health(c *gin.Context) {
	data := gin.H{
		"status":    "ok",
		"message":   "DevInsight Control Plane is running",
		"timestamp": time.Now().Unix(),
	}
	ac.SuccessResponse(c, "服务正常", data)
}
