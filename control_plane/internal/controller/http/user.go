package http

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	*BaseController
	userService *service.UserService
}

// NewUserController 创建用户控制器
func NewUserController(userService *service.UserService, logger *log.Logger) *UserController {
	return &UserController{
		BaseController: NewBaseController(logger),
		userService:    userService,
	}
}

// GetAllUsers 获取所有用户（仅管理员）
func (uc *UserController) GetAllUsers(c *gin.Context) {
	if !uc.IsAdmin(c) {
		uc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// TODO: 实现获取所有用户的逻辑
	uc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// CreateUser 创建用户（仅管理员）
func (uc *UserController) CreateUser(c *gin.Context) {
	if !uc.IsAdmin(c) {
		uc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// TODO: 实现创建用户的逻辑
	uc.CreatedResponse(c, model.MsgCreateSuccess, nil)
}

// GetUser 获取指定用户
func (uc *UserController) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		uc.BadRequestResponse(c, "用户ID不能为空")
		return
	}

	// 验证用户权限：只能获取自己的信息或管理员可以获取任何用户信息
	currentUserID, exists := uc.GetUserIDFromContext(c)
	if !exists {
		uc.UnauthorizedResponse(c, model.MsgUnauthorized)
		return
	}

	if userID != string(rune(currentUserID)) && !uc.IsAdmin(c) {
		uc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// TODO: 实现获取用户的逻辑
	uc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// UpdateUser 更新用户
func (uc *UserController) UpdateUser(c *gin.Context) {
	// TODO: 实现更新用户的逻辑，验证权限
	uc.SuccessResponse(c, model.MsgUpdateSuccess, nil)
}

// DeleteUser 删除用户（仅管理员）
func (uc *UserController) DeleteUser(c *gin.Context) {
	if !uc.IsAdmin(c) {
		uc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// TODO: 实现删除用户的逻辑
	uc.SuccessResponse(c, model.MsgDeleteSuccess, nil)
}

// ChangePassword 修改密码
func (uc *UserController) ChangePassword(c *gin.Context) {
	// TODO: 实现修改密码的逻辑，验证权限
	uc.SuccessResponse(c, "密码修改成功", nil)
}
