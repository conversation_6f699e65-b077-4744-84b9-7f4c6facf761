package http

import (
	"net/http"
	"strconv"

	"aiops/control_plane/internal/model"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// BaseController 基础控制器，提供通用方法
type BaseController struct {
	logger *log.Logger
}

// NewBaseController 创建基础控制器
func NewBaseController(logger *log.Logger) *BaseController {
	return &BaseController{
		logger: logger,
	}
}

// SuccessResponse 返回成功响应
func (bc *BaseController) SuccessResponse(c *gin.Context, message string, data interface{}) {
	response := model.NewSuccessResponse(message, data)
	c.JSON(http.StatusOK, response)
}

// CreatedResponse 返回创建成功响应
func (bc *BaseController) CreatedResponse(c *gin.Context, message string, data interface{}) {
	response := model.NewSuccessResponse(message, data)
	c.JSON(http.StatusCreated, response)
}

// ErrorResponse 返回错误响应
func (bc *BaseController) ErrorResponse(c *gin.Context, statusCode int, message string, code string, details string) {
	response := model.NewErrorResponse(message, code, details)
	c.JSON(statusCode, response)
}

// InternalErrorResponse 返回内部错误响应
func (bc *BaseController) InternalErrorResponse(c *gin.Context, err error) {
	bc.logger.Error("Internal server error", zap.Error(err))
	bc.ErrorResponse(c, http.StatusInternalServerError, model.MsgInternalError, model.ErrCodeInternal, err.Error())
}

// BadRequestResponse 返回参数错误响应
func (bc *BaseController) BadRequestResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, http.StatusBadRequest, message, model.ErrCodeInvalidParam, "")
}

// NotFoundResponse 返回未找到响应
func (bc *BaseController) NotFoundResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, http.StatusNotFound, message, model.ErrCodeNotFound, "")
}

// UnauthorizedResponse 返回未授权响应
func (bc *BaseController) UnauthorizedResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, http.StatusUnauthorized, message, model.ErrCodeUnauthorized, "")
}

// ForbiddenResponse 返回权限不足响应
func (bc *BaseController) ForbiddenResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, http.StatusForbidden, message, model.ErrCodeForbidden, "")
}

// ServiceUnavailableResponse 返回服务不可用响应
func (bc *BaseController) ServiceUnavailableResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, http.StatusServiceUnavailable, message, model.ErrCodeServiceUnavailable, "")
}

// PaginatedResponse 返回分页响应
func (bc *BaseController) PaginatedResponse(c *gin.Context, message string, data interface{}, pagination *model.Pagination) {
	response := model.NewPaginatedResponse(message, data, pagination)
	c.JSON(http.StatusOK, response)
}

// GetPaginationParams 获取分页参数
func (bc *BaseController) GetPaginationParams(c *gin.Context) (page, pageSize int) {
	page = 1
	pageSize = 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("page_size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			pageSize = s
		}
	}

	return page, pageSize
}

// GetUserIDFromContext 从上下文获取用户ID
func (bc *BaseController) GetUserIDFromContext(c *gin.Context) (uint, bool) {
	if userID, exists := c.Get("userId"); exists {
		if id, ok := userID.(uint); ok {
			return id, true
		}
	}
	return 0, false
}

// GetUserRoleFromContext 从上下文获取用户角色
func (bc *BaseController) GetUserRoleFromContext(c *gin.Context) (string, bool) {
	if role, exists := c.Get("role"); exists {
		if r, ok := role.(string); ok {
			return r, true
		}
	}
	return "", false
}

// IsAdmin 检查是否为管理员
func (bc *BaseController) IsAdmin(c *gin.Context) bool {
	role, exists := bc.GetUserRoleFromContext(c)
	return exists && role == "admin"
}
