package http

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PipelineController 流水线控制器
type PipelineController struct {
	*BaseController
	pipelineService *service.PipelineService
}

// NewPipelineController 创建流水线控制器
func NewPipelineController(pipelineService *service.PipelineService, logger *log.Logger) *PipelineController {
	return &PipelineController{
		BaseController:  NewBaseController(logger),
		pipelineService: pipelineService,
	}
}

// GetAllPipelines 获取所有流水线
func (pc *PipelineController) GetAllPipelines(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	// 解析查询参数
	page := c.<PERSON><PERSON><PERSON>("page", "1")
	pageSize := c.<PERSON><PERSON>ult<PERSON><PERSON>("pageSize", "10")
	agentID := c.Query("agentId")

	pageInt, err := strconv.Atoi(page)
	if err != nil || pageInt < 1 {
		pageInt = 1
	}

	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil || pageSizeInt < 1 || pageSizeInt > 100 {
		pageSizeInt = 10
	}

	offset := (pageInt - 1) * pageSizeInt

	var pipelines []*model.PipelineConfig

	if agentID != "" {
		// 获取指定Agent的流水线
		pipelines, err = pc.pipelineService.GetPipelinesByAgent(agentID)
		if err != nil {
			pc.logger.Error("获取Agent流水线失败", zap.String("agentId", agentID), zap.Error(err))
			pc.InternalErrorResponse(c, err)
			return
		}
	} else {
		// 获取所有流水线
		pipelines, err = pc.pipelineService.ListPipelines(offset, pageSizeInt)
		if err != nil {
			pc.logger.Error("获取流水线列表失败", zap.Error(err))
			pc.InternalErrorResponse(c, err)
			return
		}
	}

	response := map[string]interface{}{
		"pipelines": pipelines,
		"page":      pageInt,
		"pageSize":  pageSizeInt,
		"total":     len(pipelines),
	}

	pc.SuccessResponse(c, model.MsgQuerySuccess, response)
}

// CreatePipeline 创建流水线
func (pc *PipelineController) CreatePipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	var req pb.PipelineConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证必要字段
	if req.PipelineId == "" || req.Name == "" {
		pc.BadRequestResponse(c, "流水线ID和名称不能为空")
		return
	}

	// 创建流水线
	pipeline, err := pc.pipelineService.CreatePipeline(&req)
	if err != nil {
		pc.logger.Error("创建流水线失败",
			zap.String("pipelineId", req.PipelineId),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.CreatedResponse(c, model.MsgCreateSuccess, pipeline)
}

// GetPipeline 获取指定流水线
func (pc *PipelineController) GetPipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	// 获取流水线配置
	pipeline, err := pc.pipelineService.GetPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("获取流水线失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.NotFoundResponse(c, "流水线不存在")
		return
	}

	pc.SuccessResponse(c, model.MsgQuerySuccess, pipeline)
}

// UpdatePipeline 更新流水线
func (pc *PipelineController) UpdatePipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	var req pb.PipelineConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 确保请求中的ID与URL参数一致
	req.PipelineId = pipelineID

	// 更新流水线
	pipeline, err := pc.pipelineService.UpdatePipeline(pipelineID, &req)
	if err != nil {
		pc.logger.Error("更新流水线失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, model.MsgUpdateSuccess, pipeline)
}

// DeletePipeline 删除流水线
func (pc *PipelineController) DeletePipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	// 删除流水线
	err := pc.pipelineService.DeletePipeline(pipelineID)
	if err != nil {
		pc.logger.Error("删除流水线失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, model.MsgDeleteSuccess, nil)
}

// GetPipelineStatus 获取流水线状态
func (pc *PipelineController) GetPipelineStatus(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	// 获取流水线状态
	status, err := pc.pipelineService.GetPipelineStatus(pipelineID)
	if err != nil {
		pc.logger.Error("获取流水线状态失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, model.MsgQuerySuccess, status)
}

// StartPipeline 启动流水线
func (pc *PipelineController) StartPipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	err := pc.pipelineService.StartPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("启动流水线失败", zap.String("pipelineID", pipelineID), zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线启动命令已发送", nil)
}

// StopPipeline 停止流水线
func (pc *PipelineController) StopPipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	err := pc.pipelineService.StopPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("停止流水线失败", zap.String("pipelineID", pipelineID), zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线停止命令已发送", nil)
}

// RestartPipeline 重启流水线
func (pc *PipelineController) RestartPipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	// 先停止流水线
	if err := pc.pipelineService.StopPipeline(pipelineID); err != nil {
		pc.logger.Warn("停止流水线失败，继续重启操作",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
	}

	// 再启动流水线
	err := pc.pipelineService.StartPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("重启流水线失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线重启成功", nil)
}

// DeployPipelineToAgent 部署流水线到Agent
func (pc *PipelineController) DeployPipelineToAgent(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	agentID := c.Param("agentId")
	if agentID == "" {
		pc.BadRequestResponse(c, "Agent ID不能为空")
		return
	}

	var req struct {
		PipelineID string `json:"pipeline_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 检查流水线是否存在
	_, err := pc.pipelineService.GetPipeline(req.PipelineID)
	if err != nil {
		pc.logger.Error("获取流水线配置失败",
			zap.String("pipelineId", req.PipelineID),
			zap.Error(err))
		pc.NotFoundResponse(c, "流水线不存在")
		return
	}

	// 部署流水线到Agent
	err = pc.pipelineService.StartPipelineOnAgent(agentID, req.PipelineID)
	if err != nil {
		pc.logger.Error("部署流水线到Agent失败",
			zap.String("agentId", agentID),
			zap.String("pipelineId", req.PipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线已成功部署到Agent", nil)
}

// GetPipelineTemplates 获取流水线模板
func (pc *PipelineController) GetPipelineTemplates(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	templateType := c.Query("type")
	templates, err := pc.pipelineService.GetPipelineTemplates(templateType)
	if err != nil {
		pc.logger.Error("获取流水线模板失败", zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, model.MsgQuerySuccess, templates)
}

// ValidatePipelineConfig 验证流水线配置
func (pc *PipelineController) ValidatePipelineConfig(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	var req pb.PipelineConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 调用服务验证配置
	validationResponse, err := pc.pipelineService.ValidatePipelineConfig(&req)
	if err != nil {
		pc.logger.Error("验证流水线配置失败", zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	// 根据验证结果返回相应状态
	if validationResponse.Valid {
		pc.SuccessResponse(c, validationResponse.Message, map[string]interface{}{
			"valid":    validationResponse.Valid,
			"warnings": validationResponse.Warnings,
		})
	} else {
		// 配置无效时返回400状态码，但不是服务器错误
		c.JSON(400, gin.H{
			"success": false,
			"message": validationResponse.Message,
			"data": map[string]interface{}{
				"valid":    validationResponse.Valid,
				"errors":   validationResponse.Errors,
				"warnings": validationResponse.Warnings,
			},
		})
	}
}

// ReloadPipeline 重新加载流水线配置
func (pc *PipelineController) ReloadPipeline(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	pipelineID := c.Param("id")
	if pipelineID == "" {
		pc.BadRequestResponse(c, "流水线ID不能为空")
		return
	}

	// 获取流水线配置
	pipeline, err := pc.pipelineService.GetPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("获取流水线配置失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.NotFoundResponse(c, "流水线不存在")
		return
	}

	// 先停止流水线
	if err := pc.pipelineService.StopPipeline(pipelineID); err != nil {
		pc.logger.Warn("停止流水线失败，继续重新加载操作",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
	}

	// 再启动流水线（重新加载配置）
	err = pc.pipelineService.StartPipeline(pipelineID)
	if err != nil {
		pc.logger.Error("重新加载流水线失败",
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线配置已重新加载", pipeline)
}

// GetPipelinesByAgent 获取指定Agent的流水线
func (pc *PipelineController) GetPipelinesByAgent(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	agentID := c.Param("agentId")
	if agentID == "" {
		pc.BadRequestResponse(c, "Agent ID不能为空")
		return
	}

	pipelines, err := pc.pipelineService.GetPipelinesByAgent(agentID)
	if err != nil {
		pc.logger.Error("获取Agent流水线失败",
			zap.String("agentId", agentID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, model.MsgQuerySuccess, pipelines)
}

// StartPipelineOnAgent 在指定Agent上启动流水线
func (pc *PipelineController) StartPipelineOnAgent(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	agentID := c.Param("agentId")
	pipelineID := c.Param("pipelineId")

	if agentID == "" || pipelineID == "" {
		pc.BadRequestResponse(c, "Agent ID和流水线ID不能为空")
		return
	}

	err := pc.pipelineService.StartPipelineOnAgent(agentID, pipelineID)
	if err != nil {
		pc.logger.Error("在Agent上启动流水线失败",
			zap.String("agentId", agentID),
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线启动命令已发送到Agent", nil)
}

// StopPipelineOnAgent 在指定Agent上停止流水线
func (pc *PipelineController) StopPipelineOnAgent(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	agentID := c.Param("agentId")
	pipelineID := c.Param("pipelineId")

	if agentID == "" || pipelineID == "" {
		pc.BadRequestResponse(c, "Agent ID和流水线ID不能为空")
		return
	}

	err := pc.pipelineService.StopPipelineOnAgent(agentID, pipelineID)
	if err != nil {
		pc.logger.Error("在Agent上停止流水线失败",
			zap.String("agentId", agentID),
			zap.String("pipelineId", pipelineID),
			zap.Error(err))
		pc.InternalErrorResponse(c, err)
		return
	}

	pc.SuccessResponse(c, "流水线停止命令已发送到Agent", nil)
}

// BatchStartPipelines 批量启动流水线
func (pc *PipelineController) BatchStartPipelines(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	var req struct {
		PipelineIDs []string `json:"pipeline_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	if len(req.PipelineIDs) == 0 {
		pc.BadRequestResponse(c, "流水线ID列表不能为空")
		return
	}

	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0

	for _, pipelineID := range req.PipelineIDs {
		err := pc.pipelineService.StartPipeline(pipelineID)
		if err != nil {
			pc.logger.Error("启动流水线失败",
				zap.String("pipelineId", pipelineID),
				zap.Error(err))
			results[pipelineID] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
			failedCount++
		} else {
			results[pipelineID] = map[string]interface{}{
				"success": true,
				"message": "启动成功",
			}
			successCount++
		}
	}

	response := map[string]interface{}{
		"total":   len(req.PipelineIDs),
		"success": successCount,
		"failed":  failedCount,
		"results": results,
	}

	if failedCount > 0 {
		pc.logger.Warn("部分流水线启动失败",
			zap.Int("total", len(req.PipelineIDs)),
			zap.Int("success", successCount),
			zap.Int("failed", failedCount))
	}

	pc.SuccessResponse(c, "批量启动操作完成", response)
}

// BatchStopPipelines 批量停止流水线
func (pc *PipelineController) BatchStopPipelines(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	var req struct {
		PipelineIDs []string `json:"pipeline_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	if len(req.PipelineIDs) == 0 {
		pc.BadRequestResponse(c, "流水线ID列表不能为空")
		return
	}

	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0

	for _, pipelineID := range req.PipelineIDs {
		err := pc.pipelineService.StopPipeline(pipelineID)
		if err != nil {
			pc.logger.Error("停止流水线失败",
				zap.String("pipelineId", pipelineID),
				zap.Error(err))
			results[pipelineID] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
			failedCount++
		} else {
			results[pipelineID] = map[string]interface{}{
				"success": true,
				"message": "停止成功",
			}
			successCount++
		}
	}

	response := map[string]interface{}{
		"total":   len(req.PipelineIDs),
		"success": successCount,
		"failed":  failedCount,
		"results": results,
	}

	if failedCount > 0 {
		pc.logger.Warn("部分流水线停止失败",
			zap.Int("total", len(req.PipelineIDs)),
			zap.Int("success", successCount),
			zap.Int("failed", failedCount))
	}

	pc.SuccessResponse(c, "批量停止操作完成", response)
}

// BatchDeployPipelines 批量部署流水线
func (pc *PipelineController) BatchDeployPipelines(c *gin.Context) {
	if pc.pipelineService == nil {
		pc.ServiceUnavailableResponse(c, "流水线服务未启用")
		return
	}

	var req struct {
		Deployments []struct {
			AgentID    string `json:"agent_id" binding:"required"`
			PipelineID string `json:"pipeline_id" binding:"required"`
		} `json:"deployments" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		pc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	if len(req.Deployments) == 0 {
		pc.BadRequestResponse(c, "部署列表不能为空")
		return
	}

	results := make([]map[string]interface{}, 0, len(req.Deployments))
	successCount := 0
	failedCount := 0

	for _, deployment := range req.Deployments {
		result := map[string]interface{}{
			"agent_id":    deployment.AgentID,
			"pipeline_id": deployment.PipelineID,
		}

		// 检查流水线是否存在
		_, err := pc.pipelineService.GetPipeline(deployment.PipelineID)
		if err != nil {
			pc.logger.Error("获取流水线配置失败",
				zap.String("pipelineId", deployment.PipelineID),
				zap.Error(err))
			result["success"] = false
			result["error"] = "流水线不存在"
			failedCount++
		} else {
			// 部署流水线到Agent
			err = pc.pipelineService.StartPipelineOnAgent(deployment.AgentID, deployment.PipelineID)
			if err != nil {
				pc.logger.Error("部署流水线到Agent失败",
					zap.String("agentId", deployment.AgentID),
					zap.String("pipelineId", deployment.PipelineID),
					zap.Error(err))
				result["success"] = false
				result["error"] = err.Error()
				failedCount++
			} else {
				result["success"] = true
				result["message"] = "部署成功"
				successCount++
			}
		}

		results = append(results, result)
	}

	response := map[string]interface{}{
		"total":   len(req.Deployments),
		"success": successCount,
		"failed":  failedCount,
		"results": results,
	}

	if failedCount > 0 {
		pc.logger.Warn("部分流水线部署失败",
			zap.Int("total", len(req.Deployments)),
			zap.Int("success", successCount),
			zap.Int("failed", failedCount))
	}

	pc.SuccessResponse(c, "批量部署操作完成", response)
}
