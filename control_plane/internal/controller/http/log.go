package http

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
)

// LogController 日志控制器
type LogController struct {
	*BaseController
	logService service.LogService
}

// NewLogController 创建日志控制器
func NewLogController(logService service.LogService, logger *log.Logger) *LogController {
	return &LogController{
		BaseController: NewBaseController(logger),
		logService:     logService,
	}
}

// GetDeviceLogs 获取设备日志
func (lc *LogController) GetDeviceLogs(c *gin.Context) {
	deviceID := c.Param("deviceId")
	if deviceID == "" {
		lc.BadRequestResponse(c, "设备ID不能为空")
		return
	}

	page, pageSize := lc.GetPaginationParams(c)

	// TODO: 实现获取设备日志的逻辑，支持分页
	_ = page
	_ = pageSize

	lc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// GetLogs 获取日志列表
func (lc *LogController) GetLogs(c *gin.Context) {
	page, pageSize := lc.GetPaginationParams(c)

	// 获取查询参数
	logLevel := c.Query("level")
	source := c.Query("source")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")

	// TODO: 实现获取日志列表的逻辑，支持过滤和分页
	_ = page
	_ = pageSize
	_ = logLevel
	_ = source
	_ = startTime
	_ = endTime

	lc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// GetLogSources 获取日志来源列表
func (lc *LogController) GetLogSources(c *gin.Context) {
	// TODO: 实现获取日志来源列表的逻辑
	lc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// CleanupOldLogs 清理旧日志（仅管理员）
func (lc *LogController) CleanupOldLogs(c *gin.Context) {
	if !lc.IsAdmin(c) {
		lc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// 获取保留天数参数
	retentionDays := c.Query("retention_days")
	if retentionDays == "" {
		retentionDays = "30" // 默认保留30天
	}

	// TODO: 实现清理旧日志的逻辑
	lc.SuccessResponse(c, "日志清理完成", nil)
}

// CleanupLogs 清理日志（仅管理员）
func (lc *LogController) CleanupLogs(c *gin.Context) {
	if !lc.IsAdmin(c) {
		lc.ForbiddenResponse(c, model.MsgForbidden)
		return
	}

	// 获取保留天数参数
	retentionDays := c.Query("retention_days")
	if retentionDays == "" {
		retentionDays = "30" // 默认保留30天
	}

	// TODO: 实现清理日志的逻辑
	lc.SuccessResponse(c, "日志清理完成", nil)
}
