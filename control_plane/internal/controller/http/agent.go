package http

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AgentController Agent控制器
type AgentController struct {
	*BaseController
	agentService *service.AgentService
}

// NewAgentController 创建Agent控制器
func NewAgentController(agentService *service.AgentService, logger *log.Logger) *AgentController {
	return &AgentController{
		BaseController: NewBaseController(logger),
		agentService:   agentService,
	}
}

// GetAllAgents 获取所有Agent
func (ac *AgentController) GetAllAgents(c *gin.Context) {
	agents, err := ac.agentService.GetAllAgents()
	if err != nil {
		ac.logger.Error("获取Agent列表失败", zap.Error(err))
		ac.InternalErrorResponse(c, err)
		return
	}

	ac.SuccessResponse(c, model.MsgQuerySuccess, agents)
}

// GetAgent 获取指定Agent
func (ac *AgentController) GetAgent(c *gin.Context) {
	agentID := c.Param("id")
	if agentID == "" {
		ac.BadRequestResponse(c, "Agent ID不能为空")
		return
	}

	agent, err := ac.agentService.GetAgent(agentID)
	if err != nil {
		ac.logger.Error("获取Agent失败", zap.String("agentID", agentID), zap.Error(err))
		ac.NotFoundResponse(c, model.MsgAgentNotFound)
		return
	}

	ac.SuccessResponse(c, model.MsgQuerySuccess, agent)
}

// DeleteAgent 删除Agent
func (ac *AgentController) DeleteAgent(c *gin.Context) {
	agentID := c.Param("id")
	if agentID == "" {
		ac.BadRequestResponse(c, "Agent ID不能为空")
		return
	}

	err := ac.agentService.DeleteAgent(agentID)
	if err != nil {
		ac.logger.Error("删除Agent失败", zap.String("agentID", agentID), zap.Error(err))
		ac.InternalErrorResponse(c, err)
		return
	}

	ac.SuccessResponse(c, model.MsgDeleteSuccess, nil)
}

// UpdateAgentStatus 更新Agent状态
func (ac *AgentController) UpdateAgentStatus(c *gin.Context) {
	agentID := c.Param("id")
	if agentID == "" {
		ac.BadRequestResponse(c, "Agent ID不能为空")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		ac.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// TODO: 实现更新Agent状态的逻辑
	ac.logger.Info("更新Agent状态", zap.String("agentID", agentID), zap.String("status", req.Status))

	ac.SuccessResponse(c, "Agent状态更新成功", nil)
}
