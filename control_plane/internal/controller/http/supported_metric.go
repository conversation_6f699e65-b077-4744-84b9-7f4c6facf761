package http

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
)

// SupportedMetricController 支持的指标控制器
type SupportedMetricController struct {
	*BaseController
	supportedMetricService service.SupportedMetricService
}

// NewSupportedMetricController 创建支持的指标控制器
func NewSupportedMetricController(supportedMetricService service.SupportedMetricService, logger *log.Logger) *SupportedMetricController {
	return &SupportedMetricController{
		BaseController:         NewBaseController(logger),
		supportedMetricService: supportedMetricService,
	}
}

// GetAllSupportedMetrics 获取所有支持的指标
func (smc *SupportedMetricController) GetAllSupportedMetrics(c *gin.Context) {
	page, pageSize := smc.GetPaginationParams(c)

	// TODO: 实现获取所有支持的指标的逻辑，支持分页
	_ = page
	_ = pageSize

	smc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// CreateSupportedMetric 创建支持的指标
func (smc *SupportedMetricController) CreateSupportedMetric(c *gin.Context) {
	// TODO: 实现创建支持的指标的逻辑
	smc.CreatedResponse(c, model.MsgCreateSuccess, nil)
}

// GetSupportedMetric 获取指定支持的指标
func (smc *SupportedMetricController) GetSupportedMetric(c *gin.Context) {
	metricID := c.Param("id")
	if metricID == "" {
		smc.BadRequestResponse(c, "指标ID不能为空")
		return
	}

	// TODO: 实现获取指定支持的指标的逻辑
	smc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}

// UpdateSupportedMetric 更新支持的指标
func (smc *SupportedMetricController) UpdateSupportedMetric(c *gin.Context) {
	metricID := c.Param("id")
	if metricID == "" {
		smc.BadRequestResponse(c, "指标ID不能为空")
		return
	}

	// TODO: 实现更新支持的指标的逻辑
	smc.SuccessResponse(c, model.MsgUpdateSuccess, nil)
}

// DeleteSupportedMetric 删除支持的指标
func (smc *SupportedMetricController) DeleteSupportedMetric(c *gin.Context) {
	metricID := c.Param("id")
	if metricID == "" {
		smc.BadRequestResponse(c, "指标ID不能为空")
		return
	}

	// TODO: 实现删除支持的指标的逻辑
	smc.SuccessResponse(c, model.MsgDeleteSuccess, nil)
}

// GetSupportedMetricsByCollectorType 根据采集器类型获取支持的指标
func (smc *SupportedMetricController) GetSupportedMetricsByCollectorType(c *gin.Context) {
	collectorType := c.Param("type")
	if collectorType == "" {
		smc.BadRequestResponse(c, "采集器类型不能为空")
		return
	}

	// TODO: 实现根据采集器类型获取支持的指标的逻辑
	smc.SuccessResponse(c, model.MsgQuerySuccess, nil)
}
