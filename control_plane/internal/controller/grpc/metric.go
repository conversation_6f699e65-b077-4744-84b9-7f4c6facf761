package grpc

import (
	"encoding/json"
	"fmt"
	"io"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

// MetricController gRPC指标控制器
type MetricController struct {
	*BaseController
	metricService *service.MetricService
}

// NewMetricController 创建gRPC指标控制器
func NewMetricController(metricService *service.MetricService, logger *log.Logger) *MetricController {
	return &MetricController{
		BaseController: NewBaseController(logger),
		metricService:  metricService,
	}
} // StreamMetricData 流式接收指标数据
func (mc *MetricController) StreamMetricData(stream pb.AgentService_StreamMetricDataServer) error {
	deviceID := ""
	receivedCount := int32(0)

	for {
		metricData, err := stream.Recv()
		if err == io.EOF {
			// 客户端关闭流
			mc.LogInfo("StreamMetricData completed",
				zap.String("deviceID", deviceID),
				zap.Int32("receivedCount", receivedCount))
			break
		}
		if err != nil {
			mc.LogError("StreamMetricData receive failed", err,
				zap.String("deviceID", deviceID))
			return err
		}

		// 第一次接收时获取 Device ID
		if deviceID == "" {
			deviceID = metricData.DeviceId
			mc.LogInfo("StreamMetricData started",
				zap.String("deviceID", deviceID))
		}

		// 转换并存储指标数据
		modelMetric := mc.convertPbMetricToModel(metricData)
		err = mc.metricService.SaveMetrics([]*model.MetricData{modelMetric})
		if err != nil {
			mc.LogError("SaveMetrics failed", err,
				zap.String("deviceID", deviceID),
				zap.String("metricKey", metricData.MetricKey))
			continue // 继续处理其他数据，不中断流
		}

		receivedCount++
	}

	// 发送响应
	return stream.SendAndClose(&pb.StreamMetricDataResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功接收 %d 条指标数据", receivedCount),
		ReceivedCount: receivedCount,
	})
}

// convertPbMetricToModel 转换protobuf指标数据到模型
func (mc *MetricController) convertPbMetricToModel(pbMetric *pb.MetricData) *model.MetricData {
	// 将 protobuf 格式的 labels 转换为 JSON 字符串
	labels := ""
	if len(pbMetric.Labels) > 0 {
		if labelsJSON, err := json.Marshal(pbMetric.Labels); err == nil {
			labels = string(labelsJSON)
		} else {
			labels = "{}" // Default to empty JSON object on error
		}
	}

	metric := &model.MetricData{
		DeviceID:  pbMetric.DeviceId,
		MetricKey: pbMetric.MetricKey,
		Timestamp: pbMetric.Timestamp,
		Labels:    labels,
		JSONData:  pbMetric.JsonData,
	}

	// 根据 protobuf 中的 oneof 值类型设置相应的字段
	switch value := pbMetric.ValueType.(type) {
	case *pb.MetricData_NumericValue:
		metric.NumericValue = &value.NumericValue
	case *pb.MetricData_StringValue:
		metric.StringValue = &value.StringValue
	case *pb.MetricData_BooleanValue:
		metric.BooleanValue = &value.BooleanValue
	}

	return metric
}
