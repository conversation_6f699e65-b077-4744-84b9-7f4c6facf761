package grpc

import (
	"context"
	"fmt"
	"time"

	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// AgentController gRPC Agent控制器
type AgentController struct {
	*BaseController
	agentService *service.AgentService
}

// NewAgentController 创建gRPC Agent控制器
func NewAgentController(agentService *service.AgentService, logger *log.Logger) *AgentController {
	return &AgentController{
		BaseController: NewBaseController(logger),
		agentService:   agentService,
	}
}

// RegisterAgent 注册Agent
func (ac *AgentController) RegisterAgent(ctx context.Context, req *pb.RegisterAgentRequest) (*pb.RegisterAgentResponse, error) {
	ac.LogInfo("RegisterAgent",
		zap.String("agentID", req.AgentId),
		zap.String("agentIP", req.AgentIp))

	success, err := ac.agentService.RegisterAgent(ctx, req.AgentId, req.AgentIp, req.SupportedCollectorTypes)
	if err != nil {
		ac.LogError("RegisterAgent", err,
			zap.String("agentID", req.AgentId))
		return &pb.RegisterAgentResponse{
			Success: false,
			Message: fmt.Sprintf("注册失败: %v", err),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.RegisterAgentResponse{
		Success: success,
		Message: "注册成功",
	}, nil
}

// Heartbeat 处理Agent心跳请求
func (ac *AgentController) Heartbeat(ctx context.Context, req *pb.HeartbeatRequest) (*pb.HeartbeatResponse, error) {
	ac.LogInfo("Heartbeat", zap.String("agentID", req.AgentId))

	err := ac.agentService.Heartbeat(ctx, req.AgentId)
	if err != nil {
		ac.LogError("Heartbeat", err, zap.String("agentID", req.AgentId))
		return &pb.HeartbeatResponse{
			Success:         false,
			Message:         fmt.Sprintf("心跳处理失败: %v", err),
			ServerTimestamp: time.Now().Unix(),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.HeartbeatResponse{
		Success:         true,
		Message:         "心跳成功",
		ServerTimestamp: time.Now().Unix(),
	}, nil
}
