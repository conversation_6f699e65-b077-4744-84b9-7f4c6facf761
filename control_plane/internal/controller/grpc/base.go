package grpc

import (
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// BaseController gRPC基础控制器，提供通用方法
type BaseController struct {
	logger *log.Logger
}

// NewBaseController 创建gRPC基础控制器
func NewBaseController(logger *log.Logger) *BaseController {
	return &BaseController{
		logger: logger,
	}
}

// LogError 记录错误日志
func (bc *BaseController) LogError(operation string, err error, fields ...zap.Field) {
	allFields := []zap.Field{zap.String("operation", operation), zap.Error(err)}
	allFields = append(allFields, fields...)
	bc.logger.Error("gRPC operation failed", allFields...)
}

// LogInfo 记录信息日志
func (bc *BaseController) LogInfo(operation string, fields ...zap.Field) {
	allFields := []zap.Field{zap.String("operation", operation)}
	allFields = append(allFields, fields...)
	bc.logger.Info("gRPC operation", allFields...)
}
