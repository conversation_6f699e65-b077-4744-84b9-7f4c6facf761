package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"sync"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// PipelineController gRPC流水线控制器
type PipelineController struct {
	*BaseController
	pipelineService *service.PipelineService
	agentService    *service.AgentService
}

// NewPipelineController 创建gRPC流水线控制器
func NewPipelineController(pipelineService *service.PipelineService, agentService *service.AgentService, logger *log.Logger) *PipelineController {
	return &PipelineController{
		BaseController:  NewBaseController(logger),
		pipelineService: pipelineService,
		agentService:    agentService,
	}
}

// StreamPipelines 处理流水线管理命令和状态的双向流
func (pc *PipelineController) StreamPipelines(stream pb.AgentService_StreamPipelinesServer) error {
	// 等待第一条消息以获取 AgentID
	pipelineStatus, err := stream.Recv()
	if err != nil {
		pc.LogError("接收首条流水线状态消息", err)
		return status.Errorf(codes.Internal, "接收首条消息失败: %v", err)
	}

	agentID := pipelineStatus.AgentId
	pc.LogInfo("Agent连接到流水线流", zap.String("agentID", agentID))

	// 注册 Agent 的命令队列
	var commandQueue chan *pb.PipelineCommand
	if pc.pipelineService != nil {
		commandQueue = pc.pipelineService.RegisterAgentCommandQueue(agentID)
		pc.LogInfo("注册Agent命令队列", zap.String("agentID", agentID))
	}

	// 确保在函数退出时注销命令队列和通知代理断连
	defer func() {
		if pc.pipelineService != nil {
			pc.pipelineService.UnregisterAgentCommandQueue(agentID)
			pc.LogInfo("注销Agent命令队列", zap.String("agentID", agentID))
		}

		// 通知AgentService代理已断连
		if pc.agentService != nil {
			pc.agentService.OnAgentDisconnected(agentID)
			pc.LogInfo("通知Agent断连", zap.String("agentID", agentID))
		}
	}()

	// 处理首条消息
	if pc.pipelineService != nil {
		err = pc.pipelineService.UpdatePipelineStatus(pipelineStatus)
		if err != nil {
			pc.LogError("处理流水线状态", err)
		}
	}

	// 创建 WaitGroup 来等待两个 goroutine 完成
	var wg sync.WaitGroup
	wg.Add(2)

	// 错误通道，用于传递错误
	errCh := make(chan error, 2)

	// 从 Agent 接收流水线状态
	go func() {
		defer wg.Done()

		for {
			pipelineStatus, err := stream.Recv()
			if err == io.EOF {
				pc.LogInfo("Agent关闭了流水线状态流", zap.String("agentID", agentID))
				return
			}
			if err != nil {
				pc.LogError("接收流水线状态", err, zap.String("agentID", agentID))
				errCh <- err
				return
			}

			// 处理流水线状态
			if pc.pipelineService != nil {
				err = pc.pipelineService.UpdatePipelineStatus(pipelineStatus)
				if err != nil {
					pc.LogError("处理流水线状态", err)
				}
			}
		}
	}()

	// 向 Agent 发送流水线管理命令
	go func() {
		defer wg.Done()

		pc.LogInfo("流水线管理命令发送器已启动", zap.String("agentID", agentID))

		for {
			select {
			case command, ok := <-commandQueue:
				if !ok {
					pc.LogInfo("命令队列已关闭", zap.String("agentID", agentID))
					return
				}

				// 发送命令到 Agent
				if err := stream.Send(command); err != nil {
					pc.LogError("发送流水线命令", err,
						zap.String("agentID", agentID),
						zap.String("command", command.CommandType.String()))
					errCh <- err
					return
				}

				pc.LogInfo("发送流水线命令成功",
					zap.String("agentID", agentID),
					zap.String("command", command.CommandType.String()),
					zap.String("pipelineID", command.PipelineId))

			case <-stream.Context().Done():
				pc.LogInfo("流水线stream context已完成", zap.String("agentID", agentID))
				return
			}
		}
	}()

	// 等待任一 goroutine 出错或全部完成
	go func() {
		wg.Wait()
		close(errCh)
	}()

	// 等待错误或全部完成
	err = <-errCh
	return err
}

// GetPipelineTemplates 获取流水线模板
func (pc *PipelineController) GetPipelineTemplates(ctx context.Context, req *pb.PipelineTemplateRequest) (*pb.PipelineTemplateResponse, error) {
	if pc.pipelineService == nil {
		return &pb.PipelineTemplateResponse{
			Success: false,
			Message: "流水线服务未启用",
		}, nil
	}

	templates, err := pc.pipelineService.GetPipelineTemplates(req.TemplateType)
	if err != nil {
		pc.LogError("获取流水线模板", err, zap.String("templateType", req.TemplateType))
		return &pb.PipelineTemplateResponse{
			Success: false,
			Message: fmt.Sprintf("获取模板失败: %v", err),
		}, nil
	}

	pbTemplates := make([]*pb.PipelineTemplate, 0, len(templates))
	for _, template := range templates {
		pbTemplate := pc.convertModelTemplateToPb(template)
		pbTemplates = append(pbTemplates, pbTemplate)
	}

	pc.LogInfo("获取流水线模板成功",
		zap.String("templateType", req.TemplateType),
		zap.Int("count", len(pbTemplates)))

	return &pb.PipelineTemplateResponse{
		Success:   true,
		Message:   "获取模板成功",
		Templates: pbTemplates,
	}, nil
}

// ValidatePipelineConfig 验证流水线配置
func (pc *PipelineController) ValidatePipelineConfig(ctx context.Context, req *pb.PipelineConfigValidationRequest) (*pb.PipelineConfigValidationResponse, error) {
	if pc.pipelineService == nil {
		return &pb.PipelineConfigValidationResponse{
			Valid:   false,
			Message: "流水线服务未启用",
		}, nil
	}

	pc.LogInfo("验证流水线配置", zap.String("pipelineID", req.Config.PipelineId))

	response, err := pc.pipelineService.ValidatePipelineConfig(req.Config)
	if err != nil {
		pc.LogError("验证流水线配置", err, zap.String("pipelineID", req.Config.PipelineId))
		return response, err
	}

	pc.LogInfo("流水线配置验证完成",
		zap.String("pipelineID", req.Config.PipelineId),
		zap.Bool("valid", response.Valid))

	return response, nil
}

// convertModelTemplateToPb 转换数据库模板到protobuf
func (pc *PipelineController) convertModelTemplateToPb(template *model.PipelineTemplate) *pb.PipelineTemplate {
	pbTemplate := &pb.PipelineTemplate{
		Id:          template.TemplateID,
		Name:        template.Name,
		Description: template.Description,
		Category:    template.Category,
	}

	// 反序列化JSON字段
	if template.Tags != "" {
		var tags []string
		if err := json.Unmarshal([]byte(template.Tags), &tags); err == nil {
			pbTemplate.Tags = tags
		}
	}

	if template.ConfigTemplate != "" {
		var configTemplate pb.PipelineConfig
		if err := json.Unmarshal([]byte(template.ConfigTemplate), &configTemplate); err == nil {
			pbTemplate.ConfigTemplate = &configTemplate
		}
	}

	if template.Parameters != "" {
		var parameters []*pb.TemplateParameter
		if err := json.Unmarshal([]byte(template.Parameters), &parameters); err == nil {
			pbTemplate.Parameters = parameters
		}
	}

	if template.DefaultValues != "" {
		var defaultValues map[string]string
		if err := json.Unmarshal([]byte(template.DefaultValues), &defaultValues); err == nil {
			pbTemplate.DefaultValues = defaultValues
		}
	}

	return pbTemplate
}
