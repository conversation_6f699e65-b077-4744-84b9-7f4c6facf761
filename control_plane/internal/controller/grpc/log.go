package grpc

import (
	"encoding/json"
	"fmt"
	"io"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

// LogController gRPC日志控制器
type LogController struct {
	*BaseController
	logService service.LogService
}

// NewLogController 创建gRPC日志控制器
func NewLogController(logService service.LogService, logger *log.Logger) *LogController {
	return &LogController{
		BaseController: NewBaseController(logger),
		logService:     logService,
	}
}

// StreamLogData 流式接收日志数据
func (lc *LogController) StreamLogData(stream pb.AgentService_StreamLogDataServer) error {
	deviceID := ""
	receivedCount := int32(0)
	var logEntries []*model.LogEntry

	for {
		logData, err := stream.Recv()
		if err == io.EOF {
			// 客户端关闭流
			lc.LogInfo("StreamLogData completed",
				zap.String("deviceID", deviceID),
				zap.Int32("receivedCount", receivedCount))
			break
		}
		if err != nil {
			lc.LogError("StreamLogData receive failed", err,
				zap.String("deviceID", deviceID))
			return err
		}

		// 第一次接收时获取 Device ID
		if deviceID == "" {
			deviceID = logData.DeviceId
			lc.LogInfo("StreamLogData started",
				zap.String("deviceID", deviceID))
		}

		// 转换并暂存日志数据
		modelLog := lc.convertPbLogToModel(logData)
		logEntries = append(logEntries, modelLog)
		receivedCount++

		// 批量保存，当达到一定数量时保存一次
		if len(logEntries) >= 100 {
			err = lc.logService.SaveLogs(logEntries)
			if err != nil {
				lc.LogError("SaveLogs failed", err,
					zap.String("deviceID", deviceID),
					zap.Int("batchSize", len(logEntries)))
				// 继续处理其他数据，不中断流
			}
			logEntries = logEntries[:0] // 清空切片
		}
	}

	// 保存剩余的日志数据
	if len(logEntries) > 0 {
		err := lc.logService.SaveLogs(logEntries)
		if err != nil {
			lc.LogError("SaveLogs final batch failed", err,
				zap.String("deviceID", deviceID),
				zap.Int("batchSize", len(logEntries)))
		}
	}

	// 发送响应
	return stream.SendAndClose(&pb.StreamLogDataResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功接收 %d 条日志数据", receivedCount),
		ReceivedCount: receivedCount,
	})
}

// convertPbLogToModel 转换protobuf日志数据到模型
func (lc *LogController) convertPbLogToModel(pbLog *pb.LogEntry) *model.LogEntry {
	// 将 protobuf 格式的 fields 转换为 JSON 字符串
	fields := ""
	if len(pbLog.Fields) > 0 {
		if fieldsJSON, err := json.Marshal(pbLog.Fields); err == nil {
			fields = string(fieldsJSON)
		} else {
			fields = "{}" // Default to empty JSON object on error
		}
	}

	return &model.LogEntry{
		DeviceID:  pbLog.DeviceId,
		LogLevel:  pbLog.LogLevel,
		Message:   pbLog.Message,
		Timestamp: pbLog.Timestamp,
		Source:    pbLog.Source,
		Fields:    fields,
	}
}
