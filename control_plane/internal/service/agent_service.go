package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// AgentService 提供 Agent 管理相关的业务逻辑
type AgentService struct {
	agentRepo    repository.AgentRepository
	deviceRepo   repository.DeviceRepository
	taskRepo     repository.CollectorTaskRepository
	logger       *log.Logger
	agentMutex   sync.RWMutex
	agentStreams map[string]AgentStream // 存储每个 Agent 的 gRPC 流

	// 心跳监控相关
	heartbeatTimeout time.Duration // Agent心跳超时时间
	cleanupInterval  time.Duration // 清理检查间隔
	cleanupStop      chan struct{} // 停止清理任务的信号
	cleanupDone      chan struct{} // 清理任务完成信号
	isRunning        bool          // 服务是否运行中
	runningMutex     sync.RWMutex  // 保护isRunning字段
}

// AgentStream 包含与 Agent 通信的流
type AgentStream struct {
	TaskStatusCh chan model.TaskStatus
	TaskConfigCh chan model.CollectorTaskConfig
	ConnectedAt  time.Time // 连接时间
}

// NewAgentService 创建一个新的 AgentService 实例
func NewAgentService(
	agentRepo repository.AgentRepository,
	deviceRepo repository.DeviceRepository,
	taskRepo repository.CollectorTaskRepository,
	logger *log.Logger,
) *AgentService {
	service := &AgentService{
		agentRepo:        agentRepo,
		deviceRepo:       deviceRepo,
		taskRepo:         taskRepo,
		logger:           logger,
		agentStreams:     make(map[string]AgentStream),
		agentMutex:       sync.RWMutex{},
		heartbeatTimeout: 5 * time.Minute, // 5分钟心跳超时
		cleanupInterval:  1 * time.Minute, // 1分钟检查一次
		cleanupStop:      make(chan struct{}),
		cleanupDone:      make(chan struct{}),
		isRunning:        false,
	}

	// 启动心跳监控
	service.StartHeartbeatMonitor()

	return service
}

// StartHeartbeatMonitor 启动心跳监控
func (s *AgentService) StartHeartbeatMonitor() {
	s.runningMutex.Lock()
	if s.isRunning {
		s.runningMutex.Unlock()
		return
	}
	s.isRunning = true
	s.runningMutex.Unlock()

	go s.heartbeatMonitorLoop()
	s.logger.Info("Agent心跳监控已启动",
		zap.Duration("heartbeat_timeout", s.heartbeatTimeout),
		zap.Duration("cleanup_interval", s.cleanupInterval))
}

// StopHeartbeatMonitor 停止心跳监控
func (s *AgentService) StopHeartbeatMonitor() {
	s.runningMutex.Lock()
	if !s.isRunning {
		s.runningMutex.Unlock()
		return
	}
	s.isRunning = false
	s.runningMutex.Unlock()

	close(s.cleanupStop)
	<-s.cleanupDone // 等待清理任务完成
	s.logger.Info("Agent心跳监控已停止")
}

// heartbeatMonitorLoop 心跳监控循环
func (s *AgentService) heartbeatMonitorLoop() {
	defer close(s.cleanupDone)

	ticker := time.NewTicker(s.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.cleanupStop:
			s.logger.Info("接收到停止信号，退出心跳监控循环")
			return
		case <-ticker.C:
			s.checkAndCleanupOfflineAgents()
		}
	}
}

// checkAndCleanupOfflineAgents 检查并清理离线Agent
func (s *AgentService) checkAndCleanupOfflineAgents() {
	// 获取所有Agent
	agents, err := s.agentRepo.GetAllAgents()
	if err != nil {
		s.logger.Error("获取Agent列表失败", zap.Error(err))
		return
	}

	now := time.Now()
	offlineCount := 0
	cleanedCount := 0

	for _, agent := range agents {
		// 检查心跳超时
		timeSinceLastHeartbeat := now.Sub(agent.LastHeartbeat)

		if timeSinceLastHeartbeat > s.heartbeatTimeout {
			// Agent已超时
			if agent.Status == "online" {
				// 标记为离线
				agent.Status = "offline"
				if err := s.agentRepo.UpdateAgent(agent); err != nil {
					s.logger.Error("更新Agent状态为离线失败",
						zap.String("agentID", agent.AgentID),
						zap.Error(err))
					continue
				}

				s.logger.Info("Agent因心跳超时被标记为离线",
					zap.String("agentID", agent.AgentID),
					zap.Duration("timeout", timeSinceLastHeartbeat))
				offlineCount++

				// 关闭相关流连接
				s.closeAgentStream(agent.AgentID)
			}

			// 如果离线时间超过更长的阈值，考虑自动删除（可配置）
			if timeSinceLastHeartbeat > s.heartbeatTimeout*3 { // 15分钟后自动删除
				// 检查是否有依赖，如果没有依赖则自动删除
				if s.canAutoDeleteAgent(agent.AgentID) {
					if err := s.autoDeleteAgent(agent.AgentID); err != nil {
						s.logger.Error("自动删除离线Agent失败",
							zap.String("agentID", agent.AgentID),
							zap.Error(err))
					} else {
						s.logger.Info("自动删除长时间离线的Agent",
							zap.String("agentID", agent.AgentID),
							zap.Duration("offline_duration", timeSinceLastHeartbeat))
						cleanedCount++
					}
				}
			}
		} else if agent.Status == "offline" && timeSinceLastHeartbeat <= s.heartbeatTimeout {
			// 这种情况下，Agent的LastHeartbeat时间在心跳超时范围内，但状态是offline
			// 这通常意味着Agent最近有心跳活动，应该检查是否真的重新上线

			// 检查Agent最近的心跳是否是"新鲜的"（最近更新的）
			// 只有当LastHeartbeat在最近的检查间隔内被更新，才认为Agent重新上线
			timeSinceOffline := time.Since(agent.LastHeartbeat)

			// 关键修复：只有当心跳时间非常接近当前时间（小于检查间隔）时，才认为是重新上线
			// 这表明Agent最近确实发送了心跳
			if timeSinceLastHeartbeat < s.cleanupInterval {
				if timeSinceOffline > s.cleanupInterval*2 {
					// Agent确实离线了一段时间，现在重新上线
					agent.Status = "online"
					if err := s.agentRepo.UpdateAgent(agent); err != nil {
						s.logger.Error("更新Agent状态为在线失败",
							zap.String("agentID", agent.AgentID),
							zap.Error(err))
					} else {
						s.logger.Info("Agent重新上线",
							zap.String("agentID", agent.AgentID),
							zap.Duration("offline_duration", timeSinceOffline))
					}
				} else {
					// Agent可能只是短暂断开连接，直接恢复在线状态
					agent.Status = "online"
					if err := s.agentRepo.UpdateAgent(agent); err != nil {
						s.logger.Error("更新Agent状态为在线失败",
							zap.String("agentID", agent.AgentID),
							zap.Error(err))
					} else {
						s.logger.Debug("Agent连接恢复",
							zap.String("agentID", agent.AgentID),
							zap.Duration("disconnect_duration", timeSinceOffline))
					}
				}
			} else {
				// 心跳时间不够新鲜，可能是时间计算误差导致的误判，保持离线状态
				s.logger.Debug("Agent仍处于离线状态",
					zap.String("agentID", agent.AgentID),
					zap.Duration("timeSinceLastHeartbeat", timeSinceLastHeartbeat),
					zap.Duration("cleanupInterval", s.cleanupInterval))
			}
		}
	}

	if offlineCount > 0 || cleanedCount > 0 {
		s.logger.Info("Agent状态检查完成",
			zap.Int("offline_marked", offlineCount),
			zap.Int("auto_deleted", cleanedCount),
			zap.Int("total_checked", len(agents)))
	}
}

// canAutoDeleteAgent 检查Agent是否可以自动删除（没有依赖的设备或任务）
func (s *AgentService) canAutoDeleteAgent(agentID string) bool {
	// 检查是否有关联的设备
	devices, err := s.deviceRepo.GetDevicesByAgentID(agentID)
	if err != nil {
		s.logger.Error("检查Agent关联设备失败",
			zap.String("agentID", agentID),
			zap.Error(err))
		return false
	}

	if len(devices) > 0 {
		s.logger.Debug("Agent有关联设备，不能自动删除",
			zap.String("agentID", agentID),
			zap.Int("device_count", len(devices)))
		return false
	}

	// 可以添加更多检查，比如正在运行的流水线等
	return true
}

// autoDeleteAgent 自动删除Agent
func (s *AgentService) autoDeleteAgent(agentID string) error {
	// 关闭流连接
	s.closeAgentStream(agentID)

	// 删除Agent记录
	if err := s.agentRepo.DeleteAgent(agentID); err != nil {
		return fmt.Errorf("删除Agent失败: %w", err)
	}

	s.logger.Info("自动删除Agent成功", zap.String("agentID", agentID))
	return nil
}

// Agent 心跳注册
func (s *AgentService) RegisterAgent(ctx context.Context, agentID, agentIP string, supportedCollectorTypes []string) (bool, error) {
	// 查询 Agent 是否已存在
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return false, fmt.Errorf("查询 Agent 失败: %w", err)
	}

	now := time.Now()

	if agent == nil {
		// 创建新 Agent - 这是真正的注册
		s.logger.Info("注册新 Agent", zap.String("AgentID", agentID), zap.String("IP", agentIP),
			zap.Strings("支持的采集器类型", supportedCollectorTypes))

		agent = &model.Agent{
			AgentID:                 agentID,
			AgentIP:                 agentIP,
			SupportedCollectorTypes: formatCollectorTypes(supportedCollectorTypes),
			Status:                  "online",
			LastHeartbeat:           now,
		}
		if err := s.agentRepo.CreateAgent(agent); err != nil {
			return false, fmt.Errorf("创建 Agent 失败: %w", err)
		}
		s.logger.Info("新 Agent 注册成功", zap.String("agentID", agentID))
		return true, nil
	}

	// 对于已存在的 Agent，检查是否需要更新
	needsUpdate := false

	// 检查关键信息是否变化
	if agent.AgentIP != agentIP {
		agent.AgentIP = agentIP
		needsUpdate = true
		s.logger.Info("Agent IP 变化", zap.String("agentID", agentID),
			zap.String("oldIP", agent.AgentIP), zap.String("newIP", agentIP))
	}

	newSupportedTypes := formatCollectorTypes(supportedCollectorTypes)
	if agent.SupportedCollectorTypes != newSupportedTypes {
		agent.SupportedCollectorTypes = newSupportedTypes
		needsUpdate = true
		s.logger.Info("Agent 支持的采集器类型变化", zap.String("agentID", agentID))
	}

	// 检查状态是否需要更新（从离线变为在线）
	if agent.Status != "online" {
		agent.Status = "online"
		needsUpdate = true
		s.logger.Info("Agent 状态变为在线", zap.String("agentID", agentID))
	}

	// 检查心跳时间间隔，只有超过一定时间才更新（避免频繁的数据库写入）
	heartbeatInterval := now.Sub(agent.LastHeartbeat)
	if heartbeatInterval > 30*time.Second { // 30秒以上才更新心跳时间
		agent.LastHeartbeat = now
		needsUpdate = true
		s.logger.Debug("更新 Agent 心跳时间", zap.String("agentID", agentID),
			zap.Duration("interval", heartbeatInterval))
	} else {
		// 频繁的心跳，只记录 Debug 级别日志
		s.logger.Debug("Agent 心跳", zap.String("agentID", agentID),
			zap.Duration("sinceLastHeartbeat", heartbeatInterval))
	}

	// 只有在真正需要时才更新数据库
	if needsUpdate {
		if err := s.agentRepo.UpdateAgent(agent); err != nil {
			return false, fmt.Errorf("更新 Agent 失败: %w", err)
		}
		s.logger.Debug("Agent 信息已更新", zap.String("agentID", agentID))
	}

	return true, nil
}

// Heartbeat 处理Agent心跳请求（专门的心跳方法，避免频繁数据库更新）
func (s *AgentService) Heartbeat(ctx context.Context, agentID string) error {
	// 检查 Agent 是否存在
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return fmt.Errorf("查询 Agent 失败: %w", err)
	}
	if agent == nil {
		return fmt.Errorf("Agent 不存在: %s", agentID)
	}

	now := time.Now()

	// 检查上次心跳时间，只有超过30秒才更新数据库
	heartbeatInterval := now.Sub(agent.LastHeartbeat)
	if heartbeatInterval > 30*time.Second {
		// 更新心跳时间和状态
		if agent.Status != "online" {
			// 检查是否是真正的重新上线（离线时间超过2分钟）
			if heartbeatInterval > s.cleanupInterval*2 {
				agent.Status = "online"
				s.logger.Info("Agent重新上线",
					zap.String("agentID", agentID),
					zap.Duration("offline_duration", heartbeatInterval))
			} else {
				// 短暂断开连接，恢复在线状态
				agent.Status = "online"
				s.logger.Debug("Agent连接恢复",
					zap.String("agentID", agentID),
					zap.Duration("disconnect_duration", heartbeatInterval))
			}
		}

		agent.LastHeartbeat = now
		if err := s.agentRepo.UpdateAgent(agent); err != nil {
			return fmt.Errorf("更新 Agent 心跳失败: %w", err)
		}

		s.logger.Debug("Agent 心跳更新", zap.String("agentID", agentID),
			zap.Duration("interval", heartbeatInterval))
	} else {
		// 频繁心跳，不更新数据库，只记录 Debug 日志
		s.logger.Debug("Agent 心跳接收", zap.String("agentID", agentID),
			zap.Duration("sinceLastUpdate", heartbeatInterval))
	}

	return nil
}

// GetAgent 获取 Agent 信息
func (s *AgentService) GetAgent(agentID string) (*model.Agent, error) {
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return nil, fmt.Errorf("获取 Agent 信息失败: %w", err)
	}
	return agent, nil
}

// GetAllAgents 获取所有 Agent
func (s *AgentService) GetAllAgents() ([]*model.Agent, error) {
	agents, err := s.agentRepo.GetAllAgents()
	if err != nil {
		return nil, fmt.Errorf("获取所有 Agent 失败: %w", err)
	}
	return agents, nil
}

// DeleteAgent 删除 Agent
func (s *AgentService) DeleteAgent(agentID string) error {
	// 检查是否存在依赖该 Agent 的设备或任务
	devices, err := s.deviceRepo.GetDevicesByAgentID(agentID)
	if err != nil {
		return fmt.Errorf("查询 Agent 关联设备失败: %w", err)
	}
	if len(devices) > 0 {
		return fmt.Errorf("无法删除 Agent，还有 %d 个设备与其关联", len(devices))
	}

	// 删除 Agent
	if err := s.agentRepo.DeleteAgent(agentID); err != nil {
		return fmt.Errorf("删除 Agent 失败: %w", err)
	}

	// 关闭流 (如果存在)
	s.closeAgentStream(agentID)

	s.logger.Info("删除 Agent 成功", zap.String("agentID", agentID))
	return nil
}

// RegisterAgentStream 注册 Agent 的流通道
func (s *AgentService) RegisterAgentStream(agentID string) (AgentStream, error) {
	// 检查 Agent 是否存在
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return AgentStream{}, fmt.Errorf("查询 Agent 失败: %w", err)
	}
	if agent == nil {
		return AgentStream{}, fmt.Errorf("Agent 不存在: %s", agentID)
	}

	// 创建新的流
	stream := AgentStream{
		TaskStatusCh: make(chan model.TaskStatus, 100),
		TaskConfigCh: make(chan model.CollectorTaskConfig, 100),
	}

	// 存储流
	s.agentMutex.Lock()
	defer s.agentMutex.Unlock()

	// 关闭旧的流 (如果存在)
	if oldStream, exists := s.agentStreams[agentID]; exists {
		close(oldStream.TaskStatusCh)
		close(oldStream.TaskConfigCh)
	}

	s.agentStreams[agentID] = stream
	s.logger.Info("Agent 流注册成功", zap.String("agentID", agentID))

	// 查询 Agent 关联的任务，并发送到新流
	tasks, err := s.taskRepo.GetTasksByAgentID(agentID)
	if err != nil {
		s.logger.Error("查询 Agent 任务失败", zap.String("agentID", agentID), zap.Error(err))
	} else {
		// 在后台发送任务配置
		go func() {
			for _, task := range tasks {
				if !task.IsEnabled {
					continue
				}

				// 转换为配置
				config := s.convertTaskToConfig(*task)

				// 尝试发送，但不阻塞太久
				select {
				case stream.TaskConfigCh <- config:
					s.logger.Info("向 Agent 发送任务配置", zap.String("agentID", agentID), zap.String("taskID", task.TaskID))
				case <-time.After(1 * time.Second):
					s.logger.Error("向 Agent 发送任务配置超时", zap.String("agentID", agentID), zap.String("taskID", task.TaskID))
				}
			}
		}()
	}

	return stream, nil
}

// UpdateTaskStatus 更新任务状态
func (s *AgentService) UpdateTaskStatus(agentID string, taskStatus model.TaskStatus) error {
	// 更新数据库中的任务状态
	// 将 time.Time 转换为 Unix 时间戳 (int64)
	lastCollectTimestamp := taskStatus.LastCollectTimestamp.Unix()

	err := s.taskRepo.UpdateTaskStatus(
		taskStatus.TaskID,
		taskStatus.Status,
		taskStatus.ErrorMessage,
		lastCollectTimestamp,
	)
	if err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	s.logger.Info("更新任务状态",
		zap.String("agentID", agentID),
		zap.String("taskID", taskStatus.TaskID),
		zap.String("状态", taskStatus.Status))

	return nil
}

// GetAgentStream 获取 Agent 的流通道
func (s *AgentService) GetAgentStream(agentID string) (AgentStream, bool) {
	s.agentMutex.RLock()
	defer s.agentMutex.RUnlock()

	stream, exists := s.agentStreams[agentID]
	return stream, exists
}

// closeAgentStream 关闭并删除 Agent 的流通道
func (s *AgentService) closeAgentStream(agentID string) {
	s.agentMutex.Lock()
	defer s.agentMutex.Unlock()

	if stream, exists := s.agentStreams[agentID]; exists {
		close(stream.TaskStatusCh)
		close(stream.TaskConfigCh)
		delete(s.agentStreams, agentID)
		s.logger.Info("关闭 Agent 流", zap.String("agentID", agentID))
	}
}

// UpdateTaskConfiguration 更新任务配置并发送到 Agent
func (s *AgentService) UpdateTaskConfiguration(task *model.CollectorTask) error {
	// 保存到数据库
	if err := s.taskRepo.UpdateTask(task); err != nil {
		return fmt.Errorf("更新任务配置失败: %w", err)
	}

	// 如果任务已启用，发送到相关 Agent
	if task.IsEnabled {
		agentID := task.AgentID
		s.agentMutex.RLock()
		stream, exists := s.agentStreams[agentID]
		s.agentMutex.RUnlock()

		if exists {
			// 转换为配置并发送
			config := s.convertTaskToConfig(*task)

			// 以非阻塞方式发送
			select {
			case stream.TaskConfigCh <- config:
				s.logger.Info("发送任务配置更新", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
			case <-time.After(1 * time.Second):
				s.logger.Error("发送任务配置超时", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
			}
		} else {
			s.logger.Info("Agent 当前未连接，配置将在重连时发送", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
		}
	}

	return nil
}

// formatCollectorTypes 将收集器类型数组格式化为字符串
func formatCollectorTypes(types []string) string {
	// 实际中应该使用 JSON 封装
	return fmt.Sprintf("%v", types)
}

// convertTaskToConfig 将任务模型转换为配置消息
func (s *AgentService) convertTaskToConfig(task model.CollectorTask) model.CollectorTaskConfig {
	// 查询设备信息
	device, err := s.deviceRepo.GetDeviceByID(task.DeviceID)
	if err != nil {
		s.logger.Error("查询设备失败", zap.Error(err))
	}
	if device == nil {
		s.logger.Error("设备不存在", zap.Uint("deviceID", task.DeviceID))
		device = &model.Device{} // 使用空设备避免空指针
	}

	// 创建任务配置
	config := model.CollectorTaskConfig{
		TaskID:       task.TaskID,
		DeviceID:     fmt.Sprintf("%d", task.DeviceID),
		DeviceName:   device.Name,
		DeviceType:   device.Type,
		Host:         device.Host,
		Port:         device.Port,
		Username:     device.Username,
		Password:     device.Password,
		CollectItems: task.CollectItems,
		IsEnabled:    task.IsEnabled,
		Frequency:    task.Frequency,
	}

	return config
}

// OnAgentDisconnected 当Agent断开连接时调用
func (s *AgentService) OnAgentDisconnected(agentID string) {
	s.logger.Info("Agent断开连接", zap.String("agentID", agentID))

	// 关闭流连接
	s.closeAgentStream(agentID)

	// 更新Agent状态为离线
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		s.logger.Error("获取Agent信息失败",
			zap.String("agentID", agentID),
			zap.Error(err))
		return
	}

	if agent != nil && agent.Status == "online" {
		agent.Status = "offline"
		if err := s.agentRepo.UpdateAgent(agent); err != nil {
			s.logger.Error("更新Agent状态为离线失败",
				zap.String("agentID", agentID),
				zap.Error(err))
		} else {
			s.logger.Info("Agent状态已更新为离线",
				zap.String("agentID", agentID))
		}
	}
}
