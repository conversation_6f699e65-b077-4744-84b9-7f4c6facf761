package model

// 统一的API响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   *ErrorInfo  `json:"error,omitempty"`
}

// 错误信息结构
type ErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// 分页响应结构
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// 分页信息
type Pagination struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// 常用响应消息
const (
	// 成功消息
	MsgSuccess       = "操作成功"
	MsgCreateSuccess = "创建成功"
	MsgUpdateSuccess = "更新成功"
	MsgDeleteSuccess = "删除成功"
	MsgQuerySuccess  = "查询成功"

	// 错误消息
	MsgInternalError      = "内部服务器错误"
	MsgInvalidParams      = "请求参数无效"
	MsgNotFound           = "资源不存在"
	MsgUnauthorized       = "未授权访问"
	MsgForbidden          = "权限不足"
	MsgServiceUnavailable = "服务不可用"

	// 业务错误消息
	MsgAgentNotFound    = "Agent不存在"
	MsgDeviceNotFound   = "设备不存在"
	MsgTaskNotFound     = "任务不存在"
	MsgPipelineNotFound = "流水线不存在"
	MsgUserNotFound     = "用户不存在"
)

// 错误代码
const (
	ErrCodeInternal           = "INTERNAL_ERROR"
	ErrCodeInvalidParam       = "INVALID_PARAM"
	ErrCodeNotFound           = "NOT_FOUND"
	ErrCodeUnauthorized       = "UNAUTHORIZED"
	ErrCodeForbidden          = "FORBIDDEN"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
)

// 创建成功响应
func NewSuccessResponse(message string, data interface{}) *APIResponse {
	return &APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// 创建错误响应
func NewErrorResponse(message string, code string, details string) *APIResponse {
	return &APIResponse{
		Success: false,
		Message: message,
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
			Details: details,
		},
	}
}

// 创建分页响应
func NewPaginatedResponse(message string, data interface{}, pagination *Pagination) *PaginatedResponse {
	return &PaginatedResponse{
		Success:    true,
		Message:    message,
		Data:       data,
		Pagination: pagination,
	}
}
