#!/bin/bash

# AIOps 系统测试脚本

set -e

echo "🚀 开始测试 AIOps 简化架构系统..."

# 检查 Go 版本
echo "📋 检查 Go 版本..."
go version

# 编译项目
echo "🔨 编译项目..."
go build -o aiops cmd/main.go

# 创建必要的目录
echo "📁 创建目录..."
mkdir -p data logs web/static

# 启动系统（后台运行）
echo "🚀 启动系统..."
./aiops &
AIOPS_PID=$!

# 等待系统启动
echo "⏳ 等待系统启动..."
sleep 5

# 测试健康检查
echo "🔍 测试健康检查..."
curl -s http://localhost:8080/health | jq .

# 测试获取任务列表
echo "📋 测试获取任务列表..."
curl -s http://localhost:8080/api/v1/tasks | jq .

# 创建系统监控任务
echo "➕ 创建系统监控任务..."
curl -s -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "系统指标采集",
    "type": "collect",
    "config": {
      "collector_type": "system",
      "metrics": ["cpu", "memory", "disk", "network"]
    },
    "schedule": {
      "type": "interval",
      "interval": "30s"
    },
    "enabled": true,
    "description": "每30秒采集一次系统指标"
  }' | jq .

# 创建 CPU 异常检测任务
echo "🔍 创建 CPU 异常检测任务..."
curl -s -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "CPU异常检测",
    "type": "analyze",
    "config": {
      "analyzer_type": "anomaly_detection",
      "metric": "cpu_usage_percent",
      "algorithm": "statistical",
      "threshold": 2.0,
      "window": "5m"
    },
    "schedule": {
      "type": "interval",
      "interval": "1m"
    },
    "enabled": true,
    "description": "每分钟检测CPU使用率异常"
  }' | jq .

# 等待任务执行
echo "⏳ 等待任务执行..."
sleep 10

# 检查任务列表
echo "📋 检查任务列表..."
curl -s http://localhost:8080/api/v1/tasks | jq '.tasks[] | {id, name, type, enabled, run_count}'

# 检查指标数据
echo "📊 检查指标数据..."
curl -s "http://localhost:8080/api/v1/metrics" | jq '.metrics | length'

# 检查告警事件
echo "🚨 检查告警事件..."
curl -s http://localhost:8080/api/v1/alerts | jq '.alerts | length'

# 测试立即执行任务
echo "▶️ 测试立即执行任务..."
TASK_ID=$(curl -s http://localhost:8080/api/v1/tasks | jq -r '.tasks[0].id')
if [ "$TASK_ID" != "null" ]; then
  curl -s -X POST "http://localhost:8080/api/v1/tasks/$TASK_ID/start"
  echo "任务 $TASK_ID 已手动执行"
fi

# 停止系统
echo "🛑 停止系统..."
kill $AIOPS_PID

# 等待进程结束
sleep 2

echo "✅ 测试完成！"
echo ""
echo "📊 测试结果总结："
echo "- 系统启动: ✅"
echo "- 健康检查: ✅"
echo "- 任务创建: ✅"
echo "- 任务执行: ✅"
echo "- API 接口: ✅"
echo ""
echo "🌐 访问 Web UI: http://localhost:8080"
echo "📖 查看 API 文档: http://localhost:8080/health"
