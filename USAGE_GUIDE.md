# 改进后的插件管理系统使用指南

## 快速开始

### 1. 系统架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Control Plane │────│      Agent      │────│     Plugins     │
│                 │    │                 │    │                 │
│ - 流水线配置    │    │ - 插件管理器    │    │ - 采集器插件    │
│ - 任务调度      │    │ - 流水线引擎    │    │ - 处理器插件    │
│ - 状态监控      │    │ - 数据转换器    │    │ - 告警器插件    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 插件管理器使用

#### 初始化插件管理器
```go
import "aiops/agent/internal/pipeline"

// 创建插件管理器
pluginManager := pipeline.NewPluginManager("plugins/build", logger)

// 初始化（自动扫描和加载插件）
if err := pluginManager.Initialize(); err != nil {
    log.Fatal("插件管理器初始化失败:", err)
}
```

#### 检查插件可用性
```go
// 检查特定插件是否可用
available := pluginManager.IsPluginAvailable("system_collector", pipeline.CollectorType)
if available {
    fmt.Println("system_collector 插件可用")
}

// 获取所有已加载的插件
loadedPlugins := pluginManager.GetLoadedPlugins()
for _, plugin := range loadedPlugins {
    fmt.Printf("插件: %s:%s (%s)\n", plugin.Name, plugin.Version, plugin.Type)
}
```

#### 创建插件实例
```go
// 创建采集器插件
collectorConfig := &pb.CollectorPluginConfig{
    Name:            "system_collector",
    Type:            "collector",
    Enabled:         true,
    IntervalSeconds: 30,
    Config: map[string]string{
        "enable_cpu":    "true",
        "enable_memory": "true",
        "enable_disk":   "true",
    },
}

collector, err := pluginManager.CreateCollectorPlugin(collectorConfig)
if err != nil {
    log.Fatal("创建采集器失败:", err)
}

// 创建处理器插件
processorConfig := &pb.ProcessorPluginConfig{
    Name:           "threshold_processor",
    Type:           "processor",
    Enabled:        true,
    Concurrency:    2,
    TimeoutSeconds: 30,
    Order:          1,
    Config: map[string]string{
        "cpu_threshold":    "80.0",
        "memory_threshold": "90.0",
    },
}

processor, err := pluginManager.CreateProcessorPlugin(processorConfig)
if err != nil {
    log.Fatal("创建处理器失败:", err)
}
```

### 3. 数据转换器使用

#### 初始化数据转换器
```go
import "aiops/agent/internal/pipeline"

dataTransformer := pipeline.NewDataTransformer(logger)
```

#### 转换不同类型的数据
```go
// 转换指标数据
metrics := []*pb.MetricData{
    {
        DeviceId:  "server_001",
        Timestamp: time.Now().Unix(),
        Name:      "cpu_usage",
        Value:     75.5,
        Unit:      "percent",
    },
}

pipelineData := dataTransformer.TransformMetricData(metrics, "system_collector")

// 验证数据完整性
if err := dataTransformer.ValidatePipelineData(pipelineData); err != nil {
    log.Error("数据验证失败:", err)
}

// 增强数据（添加上下文信息）
enrichments := map[string]interface{}{
    "datacenter": "dc1",
    "environment": "production",
}
dataTransformer.EnrichPipelineData(pipelineData, enrichments)
```

### 4. Control Plane 客户端使用

#### 初始化客户端
```go
client := pipeline.NewControlPlaneClient("localhost:50051", "agent_001", logger)

// 连接到 Control Plane
if err := client.Connect(); err != nil {
    log.Fatal("连接 Control Plane 失败:", err)
}

// 启动心跳
client.StartHeartbeat()
```

#### 接收流水线配置
```go
// 启动流水线配置接收
pipelineConfigs := make(chan *pb.PipelineConfig, 10)
go client.ReceivePipelineConfigs(pipelineConfigs)

// 处理接收到的配置
for config := range pipelineConfigs {
    fmt.Printf("接收到流水线配置: %s\n", config.PipelineId)
    // 创建和启动流水线
    // ...
}
```

### 5. 插件开发指南

#### 插件接口实现
```go
// 采集器插件示例
type MyCollectorPlugin struct {
    name   string
    config map[string]interface{}
}

func (p *MyCollectorPlugin) GetName() string {
    return p.name
}

func (p *MyCollectorPlugin) GetType() pipeline.PluginType {
    return pipeline.CollectorType
}

func (p *MyCollectorPlugin) Initialize(config map[string]interface{}) error {
    p.config = config
    // 初始化逻辑
    return nil
}

func (p *MyCollectorPlugin) Start(ctx context.Context) error {
    // 启动采集逻辑
    return nil
}

func (p *MyCollectorPlugin) Stop() error {
    // 停止采集逻辑
    return nil
}

func (p *MyCollectorPlugin) Collect(ctx context.Context) ([]*pb.MetricData, error) {
    // 实际采集逻辑
    return metrics, nil
}
```

#### 插件工厂实现
```go
type MyCollectorFactory struct{}

func (f *MyCollectorFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
    if pluginType != pipeline.CollectorType {
        return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
    }
    
    plugin := &MyCollectorPlugin{
        name: "my_collector",
    }
    
    if err := plugin.Initialize(config); err != nil {
        return nil, err
    }
    
    return plugin, nil
}

func (f *MyCollectorFactory) GetSupportedTypes() []pipeline.PluginType {
    return []pipeline.PluginType{pipeline.CollectorType}
}

// 插件入口点
func GetPluginFactory() pipeline.PluginFactory {
    return &MyCollectorFactory{}
}

func GetPluginInfo() *pipeline.PluginInfo {
    return &pipeline.PluginInfo{
        Name:        "my_collector",
        Version:     "1.0.0",
        Type:        pipeline.CollectorType,
        Description: "我的自定义采集器",
    }
}
```

### 6. 构建和部署

#### 构建插件
```bash
# 构建插件为共享库
go build -buildmode=plugin -o plugins/build/my_collector-1.0.0.so plugins/my_collector/main.go

# 使用构建脚本（自动添加版本后缀）
./build.sh
```

#### 部署配置
```yaml
# agent_config.yaml
plugins:
  directory: "plugins/build"
  auto_reload: false
  validation_enabled: true

control_plane:
  server_address: "control-plane:50051"
  heartbeat_interval_seconds: 30
```

### 7. 监控和调试

#### 查看插件状态
```go
// 获取插件统计信息
stats := pluginManager.GetPluginStats()
fmt.Printf("总插件数: %v\n", stats["total_plugins"])
fmt.Printf("按类型分布: %v\n", stats["by_type"])

// 获取可用插件列表
availablePlugins := pluginManager.GetAvailablePlugins()
for _, plugin := range availablePlugins {
    fmt.Printf("可用插件: %s (%s)\n", plugin.Name, plugin.Type)
}
```

#### 日志和错误处理
```go
// 插件管理器会自动记录详细日志
// 2025-05-27 10:17:11.764029000	info	Loading plugin	{"path": "plugins/build/system_collector-1.0.0.so"}
// 2025-05-27 10:17:12.059043000	info	Plugin loaded successfully	{"name": "system_collector", "version": "1.0.0", "type": "collector"}

// 检查插件验证
if err := pluginManager.ValidatePlugin("plugins/build/my_plugin.so"); err != nil {
    log.Error("插件验证失败:", err)
}
```

### 8. 最佳实践

#### 插件版本管理
- 使用语义化版本号（x.y.z）
- 插件文件命名格式：`plugin_name-version.so`
- 保持向后兼容性

#### 错误处理
- 插件加载失败不应影响其他插件
- 实现适当的超时和重试机制
- 提供详细的错误日志

#### 性能优化
- 合理设置并发数和缓冲区大小
- 避免在插件中执行阻塞操作
- 定期监控资源使用情况

#### 安全考虑
- 验证插件来源和完整性
- 限制插件的资源使用
- 隔离插件运行环境

## 故障排除

### 常见问题

1. **插件重复加载错误**
   - 检查插件目录中是否有同名插件的多个版本
   - 系统会自动选择最高版本，删除不需要的旧版本文件

2. **插件加载失败**
   - 检查插件文件是否存在且可读
   - 验证插件是否实现了必要的接口
   - 查看详细的错误日志

3. **Control Plane 连接问题**
   - 检查网络连接和防火墙设置
   - 验证服务器地址和端口
   - 查看重连日志

4. **数据转换错误**
   - 验证输入数据格式
   - 检查必要字段是否存在
   - 查看数据验证错误信息

通过遵循本指南，您可以有效地使用改进后的插件管理系统，构建稳定可靠的监控流水线。
