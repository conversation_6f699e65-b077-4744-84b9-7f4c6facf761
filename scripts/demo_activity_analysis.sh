#!/bin/bash

# AIOps数据分析插件系统演示脚本
# 演示活动异常检测功能

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🚀 AIOps数据分析插件系统演示"
echo "=================================="
echo ""

# 检查依赖
check_dependencies() {
    echo "📋 检查依赖..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        echo "❌ Go 未安装，请先安装 Go 1.19+"
        exit 1
    fi
    
    go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    echo "✅ Go 版本: $go_version"
    
    # 检查项目结构
    if [ ! -f "$PROJECT_ROOT/go.mod" ]; then
        echo "❌ 未找到 go.mod 文件，请确保在项目根目录运行"
        exit 1
    fi
    
    echo "✅ 项目结构检查通过"
    echo ""
}

# 构建插件
build_plugins() {
    echo "🔨 构建数据分析插件..."
    
    cd "$PROJECT_ROOT"
    
    # 构建插件
    if [ -f "plugins/build.sh" ]; then
        chmod +x plugins/build.sh
        ./plugins/build.sh
    else
        echo "❌ 插件构建脚本不存在"
        exit 1
    fi
    
    echo ""
}

# 运行测试
run_tests() {
    echo "🧪 运行数据分析测试..."
    
    cd "$PROJECT_ROOT"
    
    # 运行活动分析测试
    echo "运行活动分析测试..."
    if go test -v ./test -run TestActivityAnalysis; then
        echo "✅ 活动分析测试通过"
    else
        echo "⚠️  活动分析测试失败（可能是因为缺少依赖）"
    fi
    
    # 运行业务数据结构测试
    echo "运行业务数据结构测试..."
    if go test -v ./test -run TestBusinessDataStructures; then
        echo "✅ 业务数据结构测试通过"
    else
        echo "⚠️  业务数据结构测试失败"
    fi
    
    # 运行统计函数测试
    echo "运行统计函数测试..."
    if go test -v ./test -run TestStatisticalFunctions; then
        echo "✅ 统计函数测试通过"
    else
        echo "⚠️  统计函数测试失败"
    fi
    
    echo ""
}

# 演示配置
demo_configuration() {
    echo "⚙️  演示配置管理..."
    
    # 显示插件配置
    echo "📄 活动分析插件配置示例:"
    if [ -f "$PROJECT_ROOT/plugins/examples/activity_analyzer/config.example.json" ]; then
        cat "$PROJECT_ROOT/plugins/examples/activity_analyzer/config.example.json" | head -20
        echo "..."
    else
        echo "⚠️  配置示例文件不存在"
    fi
    
    echo ""
    
    # 显示流水线配置
    echo "📄 活动分析流水线配置示例:"
    if [ -f "$PROJECT_ROOT/config/pipeline_examples/activity_analysis_pipeline.yaml" ]; then
        head -30 "$PROJECT_ROOT/config/pipeline_examples/activity_analysis_pipeline.yaml"
        echo "..."
    else
        echo "⚠️  流水线配置示例文件不存在"
    fi
    
    echo ""
}

# 演示异常检测场景
demo_scenarios() {
    echo "🎭 演示异常检测场景..."
    
    echo "场景1: 正常活动"
    echo "  - 参与人数: 500"
    echo "  - 创建到开始时间: 2小时"
    echo "  - 预期结果: 无异常"
    echo ""
    
    echo "场景2: 大型活动立即开始"
    echo "  - 参与人数: 15,000"
    echo "  - 创建到开始时间: 2分钟"
    echo "  - 预期结果: 阈值异常检测"
    echo ""
    
    echo "场景3: 可疑的非工作时间模式"
    echo "  - 参与人数: 20,000"
    echo "  - 创建时间: 凌晨2点"
    echo "  - 创建到开始时间: 1分钟"
    echo "  - 预期结果: 阈值异常 + 模式匹配异常"
    echo ""
    
    echo "场景4: 统计异常"
    echo "  - 基于历史数据的Z-Score和IQR分析"
    echo "  - 需要积累足够的历史数据"
    echo "  - 预期结果: 统计异常检测"
    echo ""
}

# 显示架构信息
show_architecture() {
    echo "🏗️  系统架构概览..."
    
    echo "数据分析插件系统架构:"
    echo "┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐"
    echo "│  业务数据采集器  │───▶│   活动分析插件    │───▶│   告警处理器     │"
    echo "│ BusinessCollector│    │ ActivityAnalyzer │    │  AlertHandler   │"
    echo "└─────────────────┘    └──────────────────┘    └─────────────────┘"
    echo "         │                       │                       │"
    echo "         ▼                       ▼                       ▼"
    echo "   生成活动数据              检测异常模式              发送告警通知"
    echo ""
    
    echo "核心分析算法:"
    echo "• 阈值异常检测 - 检测大型活动立即开始"
    echo "• Z-Score分析 - 基于标准差的统计异常检测"
    echo "• IQR分析 - 基于四分位距的异常检测"
    echo "• 模式匹配 - 检测可疑的活动创建模式"
    echo ""
    
    echo "配置灵活性:"
    echo "• 动态配置更新"
    echo "• 选择性启用算法"
    echo "• 参数级别调整"
    echo "• 历史数据管理"
    echo ""
}

# 显示使用指南
show_usage_guide() {
    echo "📖 使用指南..."
    
    echo "1. 构建和部署:"
    echo "   ./plugins/build.sh"
    echo "   # 插件将构建到 plugins/build/ 目录"
    echo ""
    
    echo "2. 配置插件:"
    echo "   # 编辑 config/plugins.yaml"
    echo "   # 添加 activity-analyzer 和 business-collector 配置"
    echo ""
    
    echo "3. 创建流水线:"
    echo "   # 参考 config/pipeline_examples/activity_analysis_pipeline.yaml"
    echo "   # 配置采集器 -> 分析器 -> 告警器 的处理链路"
    echo ""
    
    echo "4. 启动系统:"
    echo "   ./bin/agent --config config/agent.yaml"
    echo ""
    
    echo "5. 监控和调试:"
    echo "   # 查看日志文件"
    echo "   # 检查插件指标"
    echo "   # 调整配置参数"
    echo ""
}

# 主函数
main() {
    check_dependencies
    build_plugins
    run_tests
    demo_configuration
    demo_scenarios
    show_architecture
    show_usage_guide
    
    echo "🎉 演示完成！"
    echo ""
    echo "📚 更多信息:"
    echo "• 插件文档: plugins/examples/activity_analyzer/README.md"
    echo "• 配置示例: config/pipeline_examples/activity_analysis_pipeline.yaml"
    echo "• 测试代码: test/activity_analysis_test.go"
    echo ""
    echo "🚀 下一步:"
    echo "1. 根据实际需求调整配置参数"
    echo "2. 集成到现有的AIOps流水线中"
    echo "3. 监控异常检测效果并优化算法"
    echo "4. 扩展支持更多业务数据类型"
}

# 运行演示
main "$@"
