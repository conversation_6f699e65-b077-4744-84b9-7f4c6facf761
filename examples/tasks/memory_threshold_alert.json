{"id": "memory-threshold-001", "name": "内存阈值分析", "type": "analyze", "config": {"analyzer_type": "threshold_analysis", "metric": "memory_usage_percent", "thresholds": [{"name": "warning", "value": 80, "operator": ">", "severity": "warning"}, {"name": "critical", "value": 90, "operator": ">", "severity": "critical"}]}, "schedule": {"type": "interval", "interval": "30s"}, "enabled": true, "description": "每30秒检查内存使用率，超过80%警告，超过90%严重告警"}