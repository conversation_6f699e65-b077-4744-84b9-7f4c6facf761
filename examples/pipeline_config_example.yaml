# 流水线配置示例
# 展示如何配置一个完整的监控流水线

pipeline_configs:
  # 系统监控流水线
  - pipeline_id: "system_monitoring_pipeline"
    name: "系统监控流水线"
    description: "监控系统CPU、内存、磁盘使用情况"
    version: "1.0.0"
    enabled: true
    
    # 基础配置
    buffer_size: 1000
    worker_count: 2
    timeout_seconds: 30
    retry_attempts: 3
    retry_delay_seconds: 5
    
    # 监控配置
    enable_metrics: true
    enable_tracing: false
    metrics_interval_seconds: 60
    
    # 采集器配置
    collector:
      name: "system_collector"
      type: "collector"
      enabled: true
      interval_seconds: 30
      config:
        enable_cpu: "true"
        enable_memory: "true"
        enable_disk: "true"
        enable_network: "false"
        cpu_threshold: "80.0"
        memory_threshold: "90.0"
        disk_threshold: "85.0"
    
    # 处理器配置
    processors:
      - name: "threshold_processor"
        type: "processor"
        enabled: true
        concurrency: 2
        timeout_seconds: 10
        order: 1
        config:
          cpu_threshold: "80.0"
          memory_threshold: "90.0"
          disk_threshold: "85.0"
          alert_on_threshold: "true"
          
      - name: "data_enricher"
        type: "enricher"
        enabled: false  # 暂时禁用
        concurrency: 1
        timeout_seconds: 5
        order: 2
        config:
          add_hostname: "true"
          add_timestamp: "true"
          add_location: "datacenter_1"
    
    # 错误处理配置
    error_handling:
      strategy: "retry"
      max_retries: 3
      retry_delay_seconds: 2
      circuit_breaker:
        enabled: true
        failure_threshold: 5
        recovery_timeout_seconds: 30
    
    # 资源限制配置
    resource_limits:
      max_memory_mb: 512
      max_cpu_percent: 50
      max_goroutines: 100

  # MySQL 数据库监控流水线
  - pipeline_id: "mysql_monitoring_pipeline"
    name: "MySQL监控流水线"
    description: "监控MySQL数据库性能指标"
    version: "1.0.0"
    enabled: true
    
    buffer_size: 500
    worker_count: 1
    timeout_seconds: 60
    retry_attempts: 2
    retry_delay_seconds: 10
    
    enable_metrics: true
    enable_tracing: true
    metrics_interval_seconds: 120
    
    # MySQL采集器配置
    collector:
      name: "mysql_collector"
      type: "collector"
      enabled: true
      interval_seconds: 60
      config:
        host: "localhost"
        port: "3306"
        username: "monitor_user"
        password: "monitor_password"
        database: "information_schema"
        connection_timeout: "10"
        query_timeout: "30"
        collect_slow_queries: "true"
        collect_connections: "true"
        collect_innodb_stats: "true"
    
    processors:
      - name: "threshold_processor"
        type: "processor"
        enabled: true
        concurrency: 1
        timeout_seconds: 15
        order: 1
        config:
          connection_threshold: "80"
          slow_query_threshold: "2.0"
          innodb_buffer_threshold: "90.0"
          alert_on_threshold: "true"
    
    error_handling:
      strategy: "circuit_breaker"
      max_retries: 2
      retry_delay_seconds: 5
      circuit_breaker:
        enabled: true
        failure_threshold: 3
        recovery_timeout_seconds: 60
    
    resource_limits:
      max_memory_mb: 256
      max_cpu_percent: 30
      max_goroutines: 50

  # 告警处理流水线
  - pipeline_id: "alert_processing_pipeline"
    name: "告警处理流水线"
    description: "处理和分发系统告警"
    version: "1.0.0"
    enabled: true
    
    buffer_size: 200
    worker_count: 3
    timeout_seconds: 20
    retry_attempts: 5
    retry_delay_seconds: 3
    
    enable_metrics: true
    enable_tracing: true
    metrics_interval_seconds: 30
    
    # 告警采集器（从其他流水线接收告警）
    collector:
      name: "alert_collector"
      type: "collector"
      enabled: true
      interval_seconds: 10
      config:
        source_pipelines: "system_monitoring_pipeline,mysql_monitoring_pipeline"
        alert_queue: "alert_queue"
        batch_size: "10"
    
    processors:
      - name: "alert_filter"
        type: "filter"
        enabled: true
        concurrency: 2
        timeout_seconds: 5
        order: 1
        config:
          min_severity: "warning"
          blacklist_sources: "test_source"
          rate_limit_per_minute: "60"
          
      - name: "alert_enricher"
        type: "enricher"
        enabled: true
        concurrency: 1
        timeout_seconds: 10
        order: 2
        config:
          add_runbook_links: "true"
          add_escalation_info: "true"
          lookup_asset_info: "true"
          
      - name: "email_alerter"
        type: "alerter"
        enabled: true
        concurrency: 2
        timeout_seconds: 15
        order: 3
        config:
          smtp_server: "smtp.company.com"
          smtp_port: "587"
          username: "<EMAIL>"
          password: "alert_password"
          recipients: "<EMAIL>,<EMAIL>"
          template: "default_alert_template"
    
    error_handling:
      strategy: "retry"
      max_retries: 3
      retry_delay_seconds: 5
      circuit_breaker:
        enabled: false
    
    resource_limits:
      max_memory_mb: 128
      max_cpu_percent: 20
      max_goroutines: 30

# 全局配置
global_config:
  # Agent配置
  agent:
    id: "agent_001"
    name: "Production Agent 1"
    location: "datacenter_1"
    tags:
      environment: "production"
      region: "us-west-1"
      
  # Control Plane连接配置
  control_plane:
    server_address: "control-plane.company.com:50051"
    heartbeat_interval_seconds: 30
    reconnect_interval_seconds: 5
    max_reconnect_attempts: 10
    
  # 插件配置
  plugins:
    directory: "plugins/build"
    auto_reload: false
    validation_enabled: true
    
  # 日志配置
  logging:
    level: "info"
    format: "json"
    output: "stdout"
    file_rotation: true
    max_file_size_mb: 100
    max_backup_files: 5
