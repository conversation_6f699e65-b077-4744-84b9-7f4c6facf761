package pipeline

import (
	pb "aiops/pkg/proto"
	"time"
)

// DataType 数据类型枚举
type DataType string

const (
	MetricDataType   DataType = "metric"
	LogDataType      DataType = "log"
	EventDataType    DataType = "event"
	AnomalyDataType  DataType = "anomaly"
	AlertDataType    DataType = "alert"
	MixedDataType    DataType = "mixed"
	BusinessDataType DataType = "business" // 业务数据类型
)

// PipelineData - 流水线统一数据格式
type PipelineData struct {
	// 基础信息
	ID        string    `json:"id"`        // 数据唯一标识
	Type      DataType  `json:"type"`      // 数据类型
	Source    string    `json:"source"`    // 数据源
	DeviceID  string    `json:"device_id"` // 设备ID
	Timestamp time.Time `json:"timestamp"` // 时间戳

	// 数据载荷
	Metrics      []*pb.MetricData  `json:"metrics,omitempty"`       // 指标数据
	Logs         []*pb.LogEntry    `json:"logs,omitempty"`          // 日志数据
	Events       []*Event          `json:"events,omitempty"`        // 事件数据
	Anomalies    []*Anomaly        `json:"anomalies,omitempty"`     // 异常信息
	Alerts       []*Alert          `json:"alerts,omitempty"`        // 告警信息
	BusinessData []*BusinessEntity `json:"business_data,omitempty"` // 业务数据

	// 扩展信息
	Metadata map[string]any    `json:"metadata"` // 元数据
	Context  map[string]any    `json:"context"`  // 上下文信息
	Tags     map[string]string `json:"tags"`     // 标签信息

	// 流水线信息
	PipelineID  string   `json:"pipeline_id"`  // 所属流水线
	Stage       string   `json:"stage"`        // 当前处理阶段
	ProcessedBy []string `json:"processed_by"` // 已处理的插件列表
}

// Event 事件数据结构
type Event struct {
	ID          string         `json:"id"`
	Type        string         `json:"type"`
	Source      string         `json:"source"`
	Timestamp   time.Time      `json:"timestamp"`
	Severity    string         `json:"severity"`
	Message     string         `json:"message"`
	Attributes  map[string]any `json:"attributes"`
	DeviceID    string         `json:"device_id"`
	ServiceName string         `json:"service_name"`
}

// Anomaly 异常数据结构
type Anomaly struct {
	ID          string         `json:"id"`
	Type        string         `json:"type"`
	Severity    string         `json:"severity"`
	Score       float64        `json:"score"`
	Threshold   float64        `json:"threshold"`
	Description string         `json:"description"`
	Timestamp   time.Time      `json:"timestamp"`
	DeviceID    string         `json:"device_id"`
	MetricName  string         `json:"metric_name"`
	Value       float64        `json:"value"`
	Expected    float64        `json:"expected"`
	Attributes  map[string]any `json:"attributes"`
}

// Alert 告警数据结构
type Alert struct {
	ID          string         `json:"id"`
	Type        string         `json:"type"`
	Severity    string         `json:"severity"`
	Status      string         `json:"status"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Timestamp   time.Time      `json:"timestamp"`
	DeviceID    string         `json:"device_id"`
	RuleID      string         `json:"rule_id"`
	Attributes  map[string]any `json:"attributes"`
	Actions     []AlertAction  `json:"actions"`
}

// AlertAction 告警动作
type AlertAction struct {
	Type       string         `json:"type"`
	Target     string         `json:"target"`
	Parameters map[string]any `json:"parameters"`
	Status     string         `json:"status"`
	Timestamp  time.Time      `json:"timestamp"`
}

// PluginType 插件类型
type PluginType string

const (
	CollectorType PluginType = "collector" // 采集器插件
	ProcessorType PluginType = "processor" // 处理器插件
	AnalyzerType  PluginType = "analyzer"  // 分析器插件
	AlerterType   PluginType = "alerter"   // 告警器插件
	EnricherType  PluginType = "enricher"  // 数据增强插件
	FilterType    PluginType = "filter"    // 过滤器插件
)

// PipelineState 流水线状态
type PipelineState string

const (
	StateIdle     PipelineState = "idle"
	StateStarting PipelineState = "starting"
	StateRunning  PipelineState = "running"
	StateStopping PipelineState = "stopping"
	StateStopped  PipelineState = "stopped"
	StateError    PipelineState = "error"
)

// Schema 数据模式定义
type Schema struct {
	Fields      map[string]FieldSchema `json:"fields"`
	Required    []string               `json:"required"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
}

// FieldSchema 字段模式
type FieldSchema struct {
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Default     any         `json:"default,omitempty"`
	Validation  *Validation `json:"validation,omitempty"`
}

// Validation 验证规则
type Validation struct {
	Min    *float64 `json:"min,omitempty"`
	Max    *float64 `json:"max,omitempty"`
	Regex  string   `json:"regex,omitempty"`
	Enum   []string `json:"enum,omitempty"`
	Length *int     `json:"length,omitempty"`
}

// PluginMetrics 插件指标
type PluginMetrics struct {
	Name            string         `json:"name"`
	ProcessedCount  int64          `json:"processed_count"`
	ErrorCount      int64          `json:"error_count"`
	AvgLatency      time.Duration  `json:"avg_latency"`
	LastProcessTime time.Time      `json:"last_process_time"`
	Status          string         `json:"status"`
	CustomMetrics   map[string]any `json:"custom_metrics"`
}

// BusinessEntity 业务实体数据结构
type BusinessEntity struct {
	ID         string            `json:"id"`         // 业务实体唯一标识
	Type       string            `json:"type"`       // 业务实体类型 (activity, user, order, etc.)
	Name       string            `json:"name"`       // 业务实体名称
	Status     string            `json:"status"`     // 业务实体状态
	Timestamp  time.Time         `json:"timestamp"`  // 业务实体时间戳
	Properties map[string]any    `json:"properties"` // 业务实体属性
	Metadata   map[string]any    `json:"metadata"`   // 业务实体元数据
	Tags       map[string]string `json:"tags"`       // 业务实体标签
}

// ActivityData 活动业务数据结构
type ActivityData struct {
	ActivityID           string         `json:"activity_id"`           // 活动ID
	ActivityName         string         `json:"activity_name"`         // 活动名称
	ActivityType         string         `json:"activity_type"`         // 活动类型
	CreatedTime          time.Time      `json:"created_time"`          // 创建时间
	StartTime            time.Time      `json:"start_time"`            // 开始时间
	EndTime              time.Time      `json:"end_time"`              // 结束时间
	ExpectedParticipants int64          `json:"expected_participants"` // 预期参与人数
	ActualParticipants   int64          `json:"actual_participants"`   // 实际参与人数
	Status               string         `json:"status"`                // 活动状态 (created, started, ended, cancelled)
	CreatorID            string         `json:"creator_id"`            // 创建者ID
	Description          string         `json:"description"`           // 活动描述
	Properties           map[string]any `json:"properties"`            // 活动属性
}

// UserData 用户业务数据结构
type UserData struct {
	UserID       string         `json:"user_id"`       // 用户ID
	Username     string         `json:"username"`      // 用户名
	UserType     string         `json:"user_type"`     // 用户类型
	Status       string         `json:"status"`        // 用户状态
	RegisterTime time.Time      `json:"register_time"` // 注册时间
	LastLogin    time.Time      `json:"last_login"`    // 最后登录时间
	Properties   map[string]any `json:"properties"`    // 用户属性
}

// BusinessEntityType 业务实体类型枚举
type BusinessEntityType string

const (
	ActivityEntityType BusinessEntityType = "activity" // 活动数据
	UserEntityType     BusinessEntityType = "user"     // 用户数据
	OrderEntityType    BusinessEntityType = "order"    // 订单数据
	CustomEntityType   BusinessEntityType = "custom"   // 自定义业务数据
)
