syntax = "proto3";

import "google/protobuf/empty.proto";

package devinsight;

option go_package = "aiops/pkg/proto";

// AgentService 定义了 Agent 和 Control Plane 之间的通信接口
service AgentService {
    // RegisterAgent 用于 Agent 向 Control Plane 注册和发送心跳
    rpc RegisterAgent(RegisterAgentRequest) returns (RegisterAgentResponse) {}

    // Heartbeat 专门用于心跳检测，避免频繁的注册更新
    rpc Heartbeat(HeartbeatRequest) returns (HeartbeatResponse) {}

    // StreamCollectorTasks 是双向流，用于下发采集任务配置和接收任务状态 (保留向后兼容)
    rpc StreamCollectorTasks(stream TaskStatus) returns (stream CollectorTaskConfig) {}

    // StreamPipelines 是双向流，用于下发流水线管理命令和接收流水线状态
    rpc StreamPipelines(stream PipelineStatus) returns (stream PipelineCommand) {}

    // StreamMetricData 是单向流，用于 Agent 上报采集到的指标数据
    rpc StreamMetricData(stream MetricData) returns (StreamMetricDataResponse) {}

    // StreamLogData 是单向流，用于 Agent 上报日志数据
    rpc StreamLogData(stream LogEntry) returns (StreamLogDataResponse) {}

    // RequestPlugin 用于 Agent 请求特定的插件
    rpc RequestPlugin(PluginRequest) returns (PluginResponse) {}

    // StreamPluginUpdates 是服务器流，用于 Control Plane 推送插件更新通知
    rpc StreamPluginUpdates(google.protobuf.Empty) returns (stream PluginUpdateNotification) {}

    // ReportPluginStatus 用于 Agent 上报插件状态
    rpc ReportPluginStatus(PluginStatusReport) returns (PluginStatusResponse) {}

    // GetPipelineTemplates 获取流水线模板
    rpc GetPipelineTemplates(PipelineTemplateRequest) returns (PipelineTemplateResponse) {}

    // ValidatePipelineConfig 验证流水线配置
    rpc ValidatePipelineConfig(PipelineConfigValidationRequest) returns (PipelineConfigValidationResponse) {}
}

// RegisterAgentRequest Agent 注册请求
message RegisterAgentRequest {
    string agent_id = 1;
    string agent_ip = 2;
    repeated string supported_collector_types = 3;
    DeviceConfig device_config = 4; // Agent设备配置信息
}

// DeviceConfig Agent设备配置信息
message DeviceConfig {
    int64 max_memory_mb = 1;
    int32 max_cpu_percent = 2;
    int64 max_disk_mb = 3;
    int32 max_concurrent_tasks = 4;
    map<string, string> capabilities = 5;
}

// RegisterAgentResponse Agent 注册响应
message RegisterAgentResponse {
    bool success = 1;
    string message = 2;
}

// HeartbeatRequest 心跳请求
message HeartbeatRequest {
    string agent_id = 1;
    int64 timestamp = 2;
}

// HeartbeatResponse 心跳响应
message HeartbeatResponse {
    bool success = 1;
    string message = 2;
    int64 server_timestamp = 3;
}

// CollectorTaskConfig 采集任务配置
message CollectorTaskConfig {
    string task_id = 1;
    string device_id = 2;
    string device_name = 3;
    string device_type = 4;
    string host = 5;
    int32 port = 6;
    string username = 7;
    string password = 8;
    map<string, string> connect_params = 9;
    int64 frequency_seconds = 10;
    repeated string collect_items = 11;
    bool is_enabled = 12;
}

// TaskStatus 任务状态上报
message TaskStatus {
    string task_id = 1;
    string status = 2; // running, failed, success
    string error_message = 3;
    int64 last_collect_timestamp = 4;
}

// MetricData 指标数据点 - 支持灵活的多维数据
message MetricData {
    string device_id = 1;
    string metric_key = 2;
    oneof value_type {
        double numeric_value = 3;
        string string_value = 4;
        bool boolean_value = 5;
    }
    int64 timestamp = 6;
    string json_data = 7; // 灵活的JSON格式数据，支持复杂的多维指标
    map<string, string> labels = 8;
}

// SupportedMetric 支持的指标定义
message SupportedMetric {
    string metric_key = 1;
    string metric_name = 2;
    string description = 3;
    string data_type = 4; // numeric, string, boolean, json
    string unit = 5;
    map<string, string> metadata = 6;
    bool is_active = 7;
    string collector_type = 8;
}

// LogEntry 日志条目
message LogEntry {
    string device_id = 1;
    string log_level = 2; // DEBUG, INFO, WARN, ERROR
    string message = 3;
    int64 timestamp = 4;
    string source = 5; // 日志来源：agent, collector, etc.
    map<string, string> fields = 6; // 额外的结构化字段
}

// StreamLogDataResponse 日志数据上报响应
message StreamLogDataResponse {
    bool success = 1;
    string message = 2;
    int32 received_count = 3;
}

// StreamMetricDataResponse 指标数据上报响应
message StreamMetricDataResponse {
    bool success = 1;
    string message = 2;
    int32 received_count = 3;
}

// Plugin Distribution Messages

// PluginRequest Agent 请求插件
message PluginRequest {
    string agent_id = 1;
    string plugin_name = 2;
    string plugin_version = 3; // 可选，如果为空则获取最新版本
    string device_type = 4; // 设备类型，用于确定插件兼容性
    string architecture = 5; // 系统架构：amd64, arm64, etc.
    string os = 6; // 操作系统：linux, windows, darwin
}

// PluginResponse Control Plane 响应插件请求
message PluginResponse {
    bool success = 1;
    string message = 2;
    PluginMetadata metadata = 3;
    string plugin_path = 4; // 插件在 plugins/build 目录下的相对路径
    string checksum = 5; // 插件文件校验和
    string absolute_path = 6; // 插件的绝对路径（可选）
}

// PluginMetadata 插件元数据
message PluginMetadata {
    string name = 1;
    string version = 2;
    string description = 3;
    repeated string supported_device_types = 4;
    repeated string supported_architectures = 5;
    repeated string supported_os = 6;
    map<string, string> configuration = 7;
    repeated string dependencies = 8;
    int64 size_bytes = 9;
    string author = 10;
    int64 created_at = 11;
    int64 updated_at = 12;
}

// PluginUpdateNotification 插件更新通知
message PluginUpdateNotification {
    string agent_id = 1;
    string plugin_name = 2;
    string new_version = 3;
    string old_version = 4;
    bool is_mandatory = 5; // 是否强制更新
    string reason = 6; // 更新原因
    PluginMetadata metadata = 7;
}

// PluginUpdateResponse 插件更新响应
message PluginUpdateResponse {
    bool success = 1;
    string message = 2;
    int32 notification_count = 3;
}

// PluginStatusReport Agent 上报插件状态
message PluginStatusReport {
    string agent_id = 1;
    repeated PluginStatus plugin_statuses = 2;
}

// PluginStatus 单个插件状态
message PluginStatus {
    string plugin_name = 1;
    string version = 2;
    string status = 3; // loaded, running, failed, unloaded
    string error_message = 4;
    int64 last_used_timestamp = 5;
    int64 load_timestamp = 6;
    map<string, string> metrics = 7; // 插件相关指标
}

// PluginStatusResponse 插件状态上报响应
message PluginStatusResponse {
    bool success = 1;
    string message = 2;
    repeated string actions = 3; // 建议的操作：reload, unload, update, etc.
}

// Pipeline Messages

// PipelineCommand 流水线管理命令
message PipelineCommand {
    string command_id = 1;
    string agent_id = 2;
    CommandType command_type = 3;
    string pipeline_id = 4;
    PipelineConfig config = 5; // 仅在CREATE和UPDATE命令时使用
    map<string, string> parameters = 6; // 额外参数
    int64 timestamp = 7;
}

// CommandType 命令类型枚举
enum CommandType {
    UNKNOWN = 0;
    CREATE_PIPELINE = 1;
    START_PIPELINE = 2;
    STOP_PIPELINE = 3;
    DELETE_PIPELINE = 4;
    UPDATE_PIPELINE = 5;
    RELOAD_PIPELINE = 6;
    GET_STATUS = 7;
}

// PipelineConfig 流水线配置
message PipelineConfig {
    string pipeline_id = 1;
    string name = 2;
    string description = 3;
    string version = 4;
    bool enabled = 5;

    // 基础配置
    int32 buffer_size = 6;
    int32 worker_count = 7;
    int64 timeout_seconds = 8;
    int32 retry_attempts = 9;
    int64 retry_delay_seconds = 10;

    // 采集器配置
    CollectorPluginConfig collector = 11;

    // 处理器配置
    repeated ProcessorPluginConfig processors = 12;

    // 监控配置
    bool enable_metrics = 13;
    bool enable_tracing = 14;
    int64 metrics_interval_seconds = 15;

    // 错误处理配置
    ErrorHandlingConfig error_handling = 16;

    // 资源限制
    ResourceLimitsConfig resource_limits = 17;

    // 创建和更新时间
    int64 created_at = 18;
    int64 updated_at = 19;
}

// CollectorPluginConfig 采集器插件配置
message CollectorPluginConfig {
    string name = 1;
    string type = 2;
    int64 interval_seconds = 3;
    map<string, string> config = 4;
    bool enabled = 5;
}

// ProcessorPluginConfig 处理器插件配置
message ProcessorPluginConfig {
    string name = 1;
    string type = 2;
    bool enabled = 3;
    map<string, string> config = 4;
    int32 concurrency = 5;
    int64 timeout_seconds = 6;
    int32 order = 7;
}

// ErrorHandlingConfig 错误处理配置
message ErrorHandlingConfig {
    string strategy = 1; // ignore, retry, circuit_breaker
    int32 max_retries = 2;
    int64 retry_delay_seconds = 3;
    CircuitBreakerConfig circuit_breaker = 4;
    DeadLetterConfig dead_letter = 5;
}

// CircuitBreakerConfig 熔断器配置
message CircuitBreakerConfig {
    bool enabled = 1;
    int32 failure_threshold = 2;
    int64 recovery_timeout_seconds = 3;
    int32 half_open_requests = 4;
}

// DeadLetterConfig 死信队列配置
message DeadLetterConfig {
    bool enabled = 1;
    int32 queue_size = 2;
    bool persistent = 3;
    string path = 4;
}

// ResourceLimitsConfig 资源限制配置
message ResourceLimitsConfig {
    int64 max_memory_mb = 1;
    int32 max_cpu_percent = 2;
    int32 max_goroutines = 3;
    int64 process_timeout_seconds = 4;
    int64 collect_timeout_seconds = 5;
}

// PipelineStatus 流水线状态
message PipelineStatus {
    string pipeline_id = 1;
    string agent_id = 2;
    string status = 3; // idle, starting, running, stopping, stopped, error
    string error_message = 4;
    int64 start_timestamp = 5;
    int64 last_update_timestamp = 6;
    PipelineMetrics metrics = 7;
}

// PipelineMetrics 流水线指标
message PipelineMetrics {
    int64 collected_count = 1;
    int64 processed_count = 2;
    int64 error_count = 3;
    int64 dropped_count = 4;
    int64 avg_latency_ms = 5;
    int64 p95_latency_ms = 6;
    int64 p99_latency_ms = 7;
    double throughput = 8;
    int64 memory_usage_mb = 9;
    double cpu_usage_percent = 10;
    int32 goroutine_count = 11;
    int32 channel_size = 12;
    map<string, PluginMetrics> plugin_metrics = 13;
    int64 last_update_timestamp = 14;
}

// PluginMetrics 插件指标
message PluginMetrics {
    string name = 1;
    int64 processed_count = 2;
    int64 error_count = 3;
    int64 avg_latency_ms = 4;
    int64 last_process_timestamp = 5;
    string status = 6;
    map<string, string> custom_metrics = 7;
}

// PipelineTemplateRequest 流水线模板请求
message PipelineTemplateRequest {
    string agent_id = 1;
    string template_type = 2; // mysql, redis, system, etc.
    string category = 3; // monitoring, alerting, analysis
}

// PipelineTemplateResponse 流水线模板响应
message PipelineTemplateResponse {
    bool success = 1;
    string message = 2;
    repeated PipelineTemplate templates = 3;
}

// PipelineTemplate 流水线模板
message PipelineTemplate {
    string id = 1;
    string name = 2;
    string description = 3;
    string category = 4;
    repeated string tags = 5;
    PipelineConfig config_template = 6;
    map<string, string> default_values = 7;
    repeated TemplateParameter parameters = 8;
}

// TemplateParameter 模板参数
message TemplateParameter {
    string name = 1;
    string type = 2; // string, int, bool, float
    string description = 3;
    bool required = 4;
    string default_value = 5;
    repeated string allowed_values = 6;
}

// PipelineConfigValidationRequest 流水线配置验证请求
message PipelineConfigValidationRequest {
    string agent_id = 1;
    PipelineConfig config = 2;
}

// PipelineConfigValidationResponse 流水线配置验证响应
message PipelineConfigValidationResponse {
    bool valid = 1;
    repeated ValidationError errors = 2;
    repeated ValidationWarning warnings = 3;
    string message = 4;
}

// ValidationError 验证错误
message ValidationError {
    string field = 1;
    string message = 2;
    string code = 3;
}

// ValidationWarning 验证警告
message ValidationWarning {
    string field = 1;
    string message = 2;
    string code = 3;
}
