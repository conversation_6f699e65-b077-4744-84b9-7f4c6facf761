// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pkg/proto/devinsight.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AgentService_RegisterAgent_FullMethodName          = "/devinsight.AgentService/RegisterAgent"
	AgentService_Heartbeat_FullMethodName              = "/devinsight.AgentService/Heartbeat"
	AgentService_StreamCollectorTasks_FullMethodName   = "/devinsight.AgentService/StreamCollectorTasks"
	AgentService_StreamPipelines_FullMethodName        = "/devinsight.AgentService/StreamPipelines"
	AgentService_StreamMetricData_FullMethodName       = "/devinsight.AgentService/StreamMetricData"
	AgentService_StreamLogData_FullMethodName          = "/devinsight.AgentService/StreamLogData"
	AgentService_RequestPlugin_FullMethodName          = "/devinsight.AgentService/RequestPlugin"
	AgentService_StreamPluginUpdates_FullMethodName    = "/devinsight.AgentService/StreamPluginUpdates"
	AgentService_ReportPluginStatus_FullMethodName     = "/devinsight.AgentService/ReportPluginStatus"
	AgentService_GetPipelineTemplates_FullMethodName   = "/devinsight.AgentService/GetPipelineTemplates"
	AgentService_ValidatePipelineConfig_FullMethodName = "/devinsight.AgentService/ValidatePipelineConfig"
)

// AgentServiceClient is the client API for AgentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AgentService 定义了 Agent 和 Control Plane 之间的通信接口
type AgentServiceClient interface {
	// RegisterAgent 用于 Agent 向 Control Plane 注册和发送心跳
	RegisterAgent(ctx context.Context, in *RegisterAgentRequest, opts ...grpc.CallOption) (*RegisterAgentResponse, error)
	// Heartbeat 专门用于心跳检测，避免频繁的注册更新
	Heartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*HeartbeatResponse, error)
	// StreamCollectorTasks 是双向流，用于下发采集任务配置和接收任务状态 (保留向后兼容)
	StreamCollectorTasks(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[TaskStatus, CollectorTaskConfig], error)
	// StreamPipelines 是双向流，用于下发流水线管理命令和接收流水线状态
	StreamPipelines(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[PipelineStatus, PipelineCommand], error)
	// StreamMetricData 是单向流，用于 Agent 上报采集到的指标数据
	StreamMetricData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[MetricData, StreamMetricDataResponse], error)
	// StreamLogData 是单向流，用于 Agent 上报日志数据
	StreamLogData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[LogEntry, StreamLogDataResponse], error)
	// RequestPlugin 用于 Agent 请求特定的插件
	RequestPlugin(ctx context.Context, in *PluginRequest, opts ...grpc.CallOption) (*PluginResponse, error)
	// StreamPluginUpdates 是服务器流，用于 Control Plane 推送插件更新通知
	StreamPluginUpdates(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[PluginUpdateNotification], error)
	// ReportPluginStatus 用于 Agent 上报插件状态
	ReportPluginStatus(ctx context.Context, in *PluginStatusReport, opts ...grpc.CallOption) (*PluginStatusResponse, error)
	// GetPipelineTemplates 获取流水线模板
	GetPipelineTemplates(ctx context.Context, in *PipelineTemplateRequest, opts ...grpc.CallOption) (*PipelineTemplateResponse, error)
	// ValidatePipelineConfig 验证流水线配置
	ValidatePipelineConfig(ctx context.Context, in *PipelineConfigValidationRequest, opts ...grpc.CallOption) (*PipelineConfigValidationResponse, error)
}

type agentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentServiceClient(cc grpc.ClientConnInterface) AgentServiceClient {
	return &agentServiceClient{cc}
}

func (c *agentServiceClient) RegisterAgent(ctx context.Context, in *RegisterAgentRequest, opts ...grpc.CallOption) (*RegisterAgentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterAgentResponse)
	err := c.cc.Invoke(ctx, AgentService_RegisterAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) Heartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*HeartbeatResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HeartbeatResponse)
	err := c.cc.Invoke(ctx, AgentService_Heartbeat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) StreamCollectorTasks(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[TaskStatus, CollectorTaskConfig], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[0], AgentService_StreamCollectorTasks_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[TaskStatus, CollectorTaskConfig]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamCollectorTasksClient = grpc.BidiStreamingClient[TaskStatus, CollectorTaskConfig]

func (c *agentServiceClient) StreamPipelines(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[PipelineStatus, PipelineCommand], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[1], AgentService_StreamPipelines_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[PipelineStatus, PipelineCommand]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamPipelinesClient = grpc.BidiStreamingClient[PipelineStatus, PipelineCommand]

func (c *agentServiceClient) StreamMetricData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[MetricData, StreamMetricDataResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[2], AgentService_StreamMetricData_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[MetricData, StreamMetricDataResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamMetricDataClient = grpc.ClientStreamingClient[MetricData, StreamMetricDataResponse]

func (c *agentServiceClient) StreamLogData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[LogEntry, StreamLogDataResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[3], AgentService_StreamLogData_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[LogEntry, StreamLogDataResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamLogDataClient = grpc.ClientStreamingClient[LogEntry, StreamLogDataResponse]

func (c *agentServiceClient) RequestPlugin(ctx context.Context, in *PluginRequest, opts ...grpc.CallOption) (*PluginResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PluginResponse)
	err := c.cc.Invoke(ctx, AgentService_RequestPlugin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) StreamPluginUpdates(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[PluginUpdateNotification], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[4], AgentService_StreamPluginUpdates_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[emptypb.Empty, PluginUpdateNotification]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamPluginUpdatesClient = grpc.ServerStreamingClient[PluginUpdateNotification]

func (c *agentServiceClient) ReportPluginStatus(ctx context.Context, in *PluginStatusReport, opts ...grpc.CallOption) (*PluginStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PluginStatusResponse)
	err := c.cc.Invoke(ctx, AgentService_ReportPluginStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) GetPipelineTemplates(ctx context.Context, in *PipelineTemplateRequest, opts ...grpc.CallOption) (*PipelineTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PipelineTemplateResponse)
	err := c.cc.Invoke(ctx, AgentService_GetPipelineTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) ValidatePipelineConfig(ctx context.Context, in *PipelineConfigValidationRequest, opts ...grpc.CallOption) (*PipelineConfigValidationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PipelineConfigValidationResponse)
	err := c.cc.Invoke(ctx, AgentService_ValidatePipelineConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentServiceServer is the server API for AgentService service.
// All implementations must embed UnimplementedAgentServiceServer
// for forward compatibility.
//
// AgentService 定义了 Agent 和 Control Plane 之间的通信接口
type AgentServiceServer interface {
	// RegisterAgent 用于 Agent 向 Control Plane 注册和发送心跳
	RegisterAgent(context.Context, *RegisterAgentRequest) (*RegisterAgentResponse, error)
	// Heartbeat 专门用于心跳检测，避免频繁的注册更新
	Heartbeat(context.Context, *HeartbeatRequest) (*HeartbeatResponse, error)
	// StreamCollectorTasks 是双向流，用于下发采集任务配置和接收任务状态 (保留向后兼容)
	StreamCollectorTasks(grpc.BidiStreamingServer[TaskStatus, CollectorTaskConfig]) error
	// StreamPipelines 是双向流，用于下发流水线管理命令和接收流水线状态
	StreamPipelines(grpc.BidiStreamingServer[PipelineStatus, PipelineCommand]) error
	// StreamMetricData 是单向流，用于 Agent 上报采集到的指标数据
	StreamMetricData(grpc.ClientStreamingServer[MetricData, StreamMetricDataResponse]) error
	// StreamLogData 是单向流，用于 Agent 上报日志数据
	StreamLogData(grpc.ClientStreamingServer[LogEntry, StreamLogDataResponse]) error
	// RequestPlugin 用于 Agent 请求特定的插件
	RequestPlugin(context.Context, *PluginRequest) (*PluginResponse, error)
	// StreamPluginUpdates 是服务器流，用于 Control Plane 推送插件更新通知
	StreamPluginUpdates(*emptypb.Empty, grpc.ServerStreamingServer[PluginUpdateNotification]) error
	// ReportPluginStatus 用于 Agent 上报插件状态
	ReportPluginStatus(context.Context, *PluginStatusReport) (*PluginStatusResponse, error)
	// GetPipelineTemplates 获取流水线模板
	GetPipelineTemplates(context.Context, *PipelineTemplateRequest) (*PipelineTemplateResponse, error)
	// ValidatePipelineConfig 验证流水线配置
	ValidatePipelineConfig(context.Context, *PipelineConfigValidationRequest) (*PipelineConfigValidationResponse, error)
	mustEmbedUnimplementedAgentServiceServer()
}

// UnimplementedAgentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAgentServiceServer struct{}

func (UnimplementedAgentServiceServer) RegisterAgent(context.Context, *RegisterAgentRequest) (*RegisterAgentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterAgent not implemented")
}
func (UnimplementedAgentServiceServer) Heartbeat(context.Context, *HeartbeatRequest) (*HeartbeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Heartbeat not implemented")
}
func (UnimplementedAgentServiceServer) StreamCollectorTasks(grpc.BidiStreamingServer[TaskStatus, CollectorTaskConfig]) error {
	return status.Errorf(codes.Unimplemented, "method StreamCollectorTasks not implemented")
}
func (UnimplementedAgentServiceServer) StreamPipelines(grpc.BidiStreamingServer[PipelineStatus, PipelineCommand]) error {
	return status.Errorf(codes.Unimplemented, "method StreamPipelines not implemented")
}
func (UnimplementedAgentServiceServer) StreamMetricData(grpc.ClientStreamingServer[MetricData, StreamMetricDataResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamMetricData not implemented")
}
func (UnimplementedAgentServiceServer) StreamLogData(grpc.ClientStreamingServer[LogEntry, StreamLogDataResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamLogData not implemented")
}
func (UnimplementedAgentServiceServer) RequestPlugin(context.Context, *PluginRequest) (*PluginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestPlugin not implemented")
}
func (UnimplementedAgentServiceServer) StreamPluginUpdates(*emptypb.Empty, grpc.ServerStreamingServer[PluginUpdateNotification]) error {
	return status.Errorf(codes.Unimplemented, "method StreamPluginUpdates not implemented")
}
func (UnimplementedAgentServiceServer) ReportPluginStatus(context.Context, *PluginStatusReport) (*PluginStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportPluginStatus not implemented")
}
func (UnimplementedAgentServiceServer) GetPipelineTemplates(context.Context, *PipelineTemplateRequest) (*PipelineTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineTemplates not implemented")
}
func (UnimplementedAgentServiceServer) ValidatePipelineConfig(context.Context, *PipelineConfigValidationRequest) (*PipelineConfigValidationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePipelineConfig not implemented")
}
func (UnimplementedAgentServiceServer) mustEmbedUnimplementedAgentServiceServer() {}
func (UnimplementedAgentServiceServer) testEmbeddedByValue()                      {}

// UnsafeAgentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentServiceServer will
// result in compilation errors.
type UnsafeAgentServiceServer interface {
	mustEmbedUnimplementedAgentServiceServer()
}

func RegisterAgentServiceServer(s grpc.ServiceRegistrar, srv AgentServiceServer) {
	// If the following call pancis, it indicates UnimplementedAgentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AgentService_ServiceDesc, srv)
}

func _AgentService_RegisterAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).RegisterAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_RegisterAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).RegisterAgent(ctx, req.(*RegisterAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_Heartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).Heartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_Heartbeat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).Heartbeat(ctx, req.(*HeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_StreamCollectorTasks_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AgentServiceServer).StreamCollectorTasks(&grpc.GenericServerStream[TaskStatus, CollectorTaskConfig]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamCollectorTasksServer = grpc.BidiStreamingServer[TaskStatus, CollectorTaskConfig]

func _AgentService_StreamPipelines_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AgentServiceServer).StreamPipelines(&grpc.GenericServerStream[PipelineStatus, PipelineCommand]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamPipelinesServer = grpc.BidiStreamingServer[PipelineStatus, PipelineCommand]

func _AgentService_StreamMetricData_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AgentServiceServer).StreamMetricData(&grpc.GenericServerStream[MetricData, StreamMetricDataResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamMetricDataServer = grpc.ClientStreamingServer[MetricData, StreamMetricDataResponse]

func _AgentService_StreamLogData_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AgentServiceServer).StreamLogData(&grpc.GenericServerStream[LogEntry, StreamLogDataResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamLogDataServer = grpc.ClientStreamingServer[LogEntry, StreamLogDataResponse]

func _AgentService_RequestPlugin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PluginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).RequestPlugin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_RequestPlugin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).RequestPlugin(ctx, req.(*PluginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_StreamPluginUpdates_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(emptypb.Empty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(AgentServiceServer).StreamPluginUpdates(m, &grpc.GenericServerStream[emptypb.Empty, PluginUpdateNotification]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type AgentService_StreamPluginUpdatesServer = grpc.ServerStreamingServer[PluginUpdateNotification]

func _AgentService_ReportPluginStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PluginStatusReport)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).ReportPluginStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_ReportPluginStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).ReportPluginStatus(ctx, req.(*PluginStatusReport))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_GetPipelineTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).GetPipelineTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_GetPipelineTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).GetPipelineTemplates(ctx, req.(*PipelineTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_ValidatePipelineConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineConfigValidationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).ValidatePipelineConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_ValidatePipelineConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).ValidatePipelineConfig(ctx, req.(*PipelineConfigValidationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AgentService_ServiceDesc is the grpc.ServiceDesc for AgentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "devinsight.AgentService",
	HandlerType: (*AgentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterAgent",
			Handler:    _AgentService_RegisterAgent_Handler,
		},
		{
			MethodName: "Heartbeat",
			Handler:    _AgentService_Heartbeat_Handler,
		},
		{
			MethodName: "RequestPlugin",
			Handler:    _AgentService_RequestPlugin_Handler,
		},
		{
			MethodName: "ReportPluginStatus",
			Handler:    _AgentService_ReportPluginStatus_Handler,
		},
		{
			MethodName: "GetPipelineTemplates",
			Handler:    _AgentService_GetPipelineTemplates_Handler,
		},
		{
			MethodName: "ValidatePipelineConfig",
			Handler:    _AgentService_ValidatePipelineConfig_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamCollectorTasks",
			Handler:       _AgentService_StreamCollectorTasks_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamPipelines",
			Handler:       _AgentService_StreamPipelines_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamMetricData",
			Handler:       _AgentService_StreamMetricData_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamLogData",
			Handler:       _AgentService_StreamLogData_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamPluginUpdates",
			Handler:       _AgentService_StreamPluginUpdates_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "pkg/proto/devinsight.proto",
}
