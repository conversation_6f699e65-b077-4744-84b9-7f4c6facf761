package api

import (
	"net/http"
	"strconv"
	"time"

	"aiops/internal/task"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// createTask 创建任务
func (s *Server) createTask(c *gin.Context) {
	var req task.Task
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 生成任务 ID
	if req.ID == "" {
		req.ID = uuid.New().String()
	}

	// 设置默认值
	req.Enabled = true
	req.Status = task.TaskStatusPending

	// 保存任务
	if err := s.storage.CreateTask(&req); err != nil {
		s.logger.Error("创建任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建任务失败"})
		return
	}

	// 如果任务启用，则调度任务
	if req.Enabled {
		if err := s.scheduler.ScheduleTask(&req); err != nil {
			s.logger.Error("调度任务失败", zap.Error(err))
			// 不返回错误，任务已创建成功
		}
	}

	c.JSON(http.StatusCreated, req)
}

// getTasks 获取任务列表
func (s *Server) getTasks(c *gin.Context) {
	taskType := c.Query("type")

	var tasks []*task.Task
	var err error

	if taskType != "" {
		tasks, err = s.storage.GetTasksByType(task.TaskType(taskType))
	} else {
		tasks, err = s.storage.GetTasks()
	}

	if err != nil {
		s.logger.Error("获取任务列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"tasks": tasks})
}

// getTask 获取单个任务
func (s *Server) getTask(c *gin.Context) {
	id := c.Param("id")

	task, err := s.storage.GetTask(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	c.JSON(http.StatusOK, task)
}

// updateTask 更新任务
func (s *Server) updateTask(c *gin.Context) {
	id := c.Param("id")

	// 获取现有任务
	existingTask, err := s.storage.GetTask(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	// 绑定更新数据
	var req task.Task
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 保持 ID 不变
	req.ID = id
	req.Model = existingTask.Model

	// 更新任务
	if err := s.storage.UpdateTask(&req); err != nil {
		s.logger.Error("更新任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新任务失败"})
		return
	}

	// 重新调度任务
	if req.Enabled {
		if err := s.scheduler.ScheduleTask(&req); err != nil {
			s.logger.Error("重新调度任务失败", zap.Error(err))
		}
	} else {
		if err := s.scheduler.UnscheduleTask(id); err != nil {
			s.logger.Error("取消调度任务失败", zap.Error(err))
		}
	}

	c.JSON(http.StatusOK, req)
}

// deleteTask 删除任务
func (s *Server) deleteTask(c *gin.Context) {
	id := c.Param("id")

	// 取消调度
	if err := s.scheduler.UnscheduleTask(id); err != nil {
		s.logger.Error("取消调度任务失败", zap.Error(err))
	}

	// 删除任务
	if err := s.storage.DeleteTask(id); err != nil {
		s.logger.Error("删除任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务删除成功"})
}

// startTask 启动任务
func (s *Server) startTask(c *gin.Context) {
	id := c.Param("id")

	if err := s.scheduler.ExecuteTaskNow(id); err != nil {
		s.logger.Error("启动任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "启动任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务启动成功"})
}

// stopTask 停止任务
func (s *Server) stopTask(c *gin.Context) {
	id := c.Param("id")

	if err := s.scheduler.UnscheduleTask(id); err != nil {
		s.logger.Error("停止任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "停止任务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务停止成功"})
}

// getTaskExecutions 获取任务执行记录
func (s *Server) getTaskExecutions(c *gin.Context) {
	id := c.Param("id")
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	executions, err := s.storage.GetTaskExecutions(id, limit)
	if err != nil {
		s.logger.Error("获取任务执行记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取执行记录失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"executions": executions})
}

// getMetrics 获取指标数据
func (s *Server) getMetrics(c *gin.Context) {
	taskID := c.Query("task_id")
	metricName := c.Query("metric_name")
	startStr := c.Query("start")
	endStr := c.Query("end")

	var start, end time.Time
	var err error

	if startStr != "" {
		start, err = time.Parse(time.RFC3339, startStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的开始时间格式"})
			return
		}
	} else {
		start = time.Now().Add(-24 * time.Hour) // 默认最近24小时
	}

	if endStr != "" {
		end, err = time.Parse(time.RFC3339, endStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的结束时间格式"})
			return
		}
	} else {
		end = time.Now()
	}

	metrics, err := s.storage.GetMetricData(taskID, metricName, start, end)
	if err != nil {
		s.logger.Error("获取指标数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取指标数据失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

// getMetricData 获取特定指标数据
func (s *Server) getMetricData(c *gin.Context) {
	taskID := c.Param("task_id")
	metricName := c.Param("metric_name")

	data, err := s.storage.GetLatestMetricData(taskID, metricName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "指标数据不存在"})
		return
	}

	c.JSON(http.StatusOK, data)
}

// getLogs 获取日志
func (s *Server) getLogs(c *gin.Context) {
	taskID := c.Query("task_id")
	level := c.Query("level")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}

	logs, err := s.storage.GetLogEntries(taskID, level, limit)
	if err != nil {
		s.logger.Error("获取日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取日志失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

// getTaskLogs 获取任务日志
func (s *Server) getTaskLogs(c *gin.Context) {
	taskID := c.Param("task_id")
	level := c.Query("level")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}

	logs, err := s.storage.GetLogEntries(taskID, level, limit)
	if err != nil {
		s.logger.Error("获取任务日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务日志失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

// getAlerts 获取告警事件
func (s *Server) getAlerts(c *gin.Context) {
	taskID := c.Query("task_id")
	limitStr := c.DefaultQuery("limit", "50")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 50
	}

	alerts, err := s.storage.GetAlertEvents(taskID, limit)
	if err != nil {
		s.logger.Error("获取告警事件失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取告警事件失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"alerts": alerts})
}

// getUnresolvedAlerts 获取未解决的告警
func (s *Server) getUnresolvedAlerts(c *gin.Context) {
	alerts, err := s.storage.GetUnresolvedAlerts()
	if err != nil {
		s.logger.Error("获取未解决告警失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取未解决告警失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"alerts": alerts})
}

// resolveAlert 解决告警
func (s *Server) resolveAlert(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的告警 ID"})
		return
	}

	if err := s.storage.ResolveAlert(uint(id)); err != nil {
		s.logger.Error("解决告警失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解决告警失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "告警已解决"})
}
