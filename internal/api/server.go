package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"aiops/internal/config"
	"aiops/internal/scheduler"
	"aiops/internal/storage"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server HTTP API 服务器
type Server struct {
	config    config.ServerConfig
	storage   storage.Storage
	scheduler scheduler.Scheduler
	logger    *zap.Logger
	server    *http.Server
	router    *gin.Engine
}

// NewServer 创建 API 服务器实例
func NewServer(cfg config.ServerConfig, storage storage.Storage, scheduler scheduler.Scheduler, logger *zap.Logger) *Server {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger))

	server := &Server{
		config:    cfg,
		storage:   storage,
		scheduler: scheduler,
		logger:    logger,
		router:    router,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// Start 启动服务器
func (s *Server) Start() error {
	s.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", s.config.Port),
		Handler: s.router,
	}

	go func() {
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP 服务器启动失败", zap.Error(err))
		}
	}()

	s.logger.Info("HTTP API 服务器启动成功", zap.Int("port", s.config.Port))
	return nil
}

// Stop 停止服务器
func (s *Server) Stop() error {
	if s.server == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return s.server.Shutdown(ctx)
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	api := s.router.Group("/api/v1")

	// 任务管理 API
	tasks := api.Group("/tasks")
	{
		tasks.POST("", s.createTask)
		tasks.GET("", s.getTasks)
		tasks.GET("/:id", s.getTask)
		tasks.PUT("/:id", s.updateTask)
		tasks.DELETE("/:id", s.deleteTask)
		tasks.POST("/:id/start", s.startTask)
		tasks.POST("/:id/stop", s.stopTask)
		tasks.GET("/:id/executions", s.getTaskExecutions)
	}

	// 监控数据 API
	metrics := api.Group("/metrics")
	{
		metrics.GET("", s.getMetrics)
		metrics.GET("/:task_id/:metric_name", s.getMetricData)
	}

	// 日志 API
	logs := api.Group("/logs")
	{
		logs.GET("", s.getLogs)
		logs.GET("/:task_id", s.getTaskLogs)
	}

	// 告警 API
	alerts := api.Group("/alerts")
	{
		alerts.GET("", s.getAlerts)
		alerts.GET("/unresolved", s.getUnresolvedAlerts)
		alerts.POST("/:id/resolve", s.resolveAlert)
	}

	// 健康检查
	s.router.GET("/health", s.healthCheck)

	// 静态文件服务 (如果有 Web UI)
	s.router.Static("/static", "./web/static")
	s.router.StaticFile("/", "./web/index.html")
}

// corsMiddleware CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		logger.Info("HTTP Request",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", statusCode),
			zap.String("ip", clientIP),
			zap.Duration("latency", latency),
		)
	}
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now(),
		"version":   "3.0.0-simplified",
	})
}
