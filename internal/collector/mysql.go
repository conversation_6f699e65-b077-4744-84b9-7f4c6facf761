package collector

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"aiops/internal/task"

	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

// MySQLCollector MySQL 指标采集器
type MySQLCollector struct {
	logger *zap.Logger
}

// NewMySQLCollector 创建 MySQL 采集器
func NewMySQLCollector(logger *zap.Logger) *MySQLCollector {
	return &MySQLCollector{
		logger: logger,
	}
}

// Collect 采集 MySQL 指标
func (c *MySQLCollector) Collect(ctx context.Context, taskID string, config map[string]interface{}) ([]*task.MetricData, error) {
	// 获取连接配置
	dsn, ok := config["dsn"].(string)
	if !ok {
		return nil, fmt.Errorf("MySQL DSN 未配置")
	}

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接 MySQL 失败: %w", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("MySQL 连接测试失败: %w", err)
	}

	var metrics []*task.MetricData
	timestamp := time.Now()

	// 获取配置中的指标类型
	metricsToCollect, ok := config["metrics"].([]interface{})
	if !ok {
		metricsToCollect = []interface{}{"status", "connections", "queries", "innodb"}
	}

	for _, metricType := range metricsToCollect {
		switch metricType.(string) {
		case "status":
			statusMetrics, err := c.collectStatusMetrics(ctx, db, taskID, timestamp)
			if err != nil {
				c.logger.Error("采集 MySQL 状态指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, statusMetrics...)

		case "connections":
			connMetrics, err := c.collectConnectionMetrics(ctx, db, taskID, timestamp)
			if err != nil {
				c.logger.Error("采集 MySQL 连接指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, connMetrics...)

		case "queries":
			queryMetrics, err := c.collectQueryMetrics(ctx, db, taskID, timestamp)
			if err != nil {
				c.logger.Error("采集 MySQL 查询指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, queryMetrics...)

		case "innodb":
			innodbMetrics, err := c.collectInnoDBMetrics(ctx, db, taskID, timestamp)
			if err != nil {
				c.logger.Error("采集 MySQL InnoDB 指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, innodbMetrics...)
		}
	}

	c.logger.Info("MySQL 指标采集完成", zap.String("task_id", taskID), zap.Int("metrics_count", len(metrics)))
	return metrics, nil
}

// collectStatusMetrics 采集状态指标
func (c *MySQLCollector) collectStatusMetrics(ctx context.Context, db *sql.DB, taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	// 查询全局状态
	rows, err := db.QueryContext(ctx, "SHOW GLOBAL STATUS")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	statusMap := make(map[string]string)
	for rows.Next() {
		var name, value string
		if err := rows.Scan(&name, &value); err != nil {
			continue
		}
		statusMap[name] = value
	}

	// 提取关键指标
	keyMetrics := []string{
		"Uptime",
		"Threads_connected",
		"Threads_running",
		"Questions",
		"Slow_queries",
		"Aborted_connects",
		"Aborted_clients",
	}

	for _, metricName := range keyMetrics {
		if valueStr, exists := statusMap[metricName]; exists {
			if value, err := strconv.ParseFloat(valueStr, 64); err == nil {
				metrics = append(metrics, &task.MetricData{
					TaskID:     taskID,
					MetricName: fmt.Sprintf("mysql_%s", metricName),
					Value:      value,
					Unit:       "count",
					Timestamp:  timestamp,
					Source:     "mysql_collector",
				})
			}
		}
	}

	return metrics, nil
}

// collectConnectionMetrics 采集连接指标
func (c *MySQLCollector) collectConnectionMetrics(ctx context.Context, db *sql.DB, taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	// 查询连接数
	var maxConnections, threadsConnected int
	err := db.QueryRowContext(ctx, "SHOW VARIABLES LIKE 'max_connections'").Scan(nil, &maxConnections)
	if err != nil {
		return nil, err
	}

	err = db.QueryRowContext(ctx, "SHOW STATUS LIKE 'Threads_connected'").Scan(nil, &threadsConnected)
	if err != nil {
		return nil, err
	}

	metrics = append(metrics, []*task.MetricData{
		{
			TaskID:     taskID,
			MetricName: "mysql_max_connections",
			Value:      float64(maxConnections),
			Unit:       "count",
			Timestamp:  timestamp,
			Source:     "mysql_collector",
		},
		{
			TaskID:     taskID,
			MetricName: "mysql_current_connections",
			Value:      float64(threadsConnected),
			Unit:       "count",
			Timestamp:  timestamp,
			Source:     "mysql_collector",
		},
		{
			TaskID:     taskID,
			MetricName: "mysql_connection_usage_percent",
			Value:      float64(threadsConnected) / float64(maxConnections) * 100,
			Unit:       "percent",
			Timestamp:  timestamp,
			Source:     "mysql_collector",
		},
	}...)

	return metrics, nil
}

// collectQueryMetrics 采集查询指标
func (c *MySQLCollector) collectQueryMetrics(ctx context.Context, db *sql.DB, taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	// 查询 QPS 相关指标
	queryMetrics := map[string]string{
		"Com_select":  "mysql_select_queries",
		"Com_insert":  "mysql_insert_queries",
		"Com_update":  "mysql_update_queries",
		"Com_delete":  "mysql_delete_queries",
		"Questions":   "mysql_total_queries",
		"Slow_queries": "mysql_slow_queries",
	}

	for statusName, metricName := range queryMetrics {
		var value int
		err := db.QueryRowContext(ctx, fmt.Sprintf("SHOW STATUS LIKE '%s'", statusName)).Scan(nil, &value)
		if err != nil {
			continue
		}

		metrics = append(metrics, &task.MetricData{
			TaskID:     taskID,
			MetricName: metricName,
			Value:      float64(value),
			Unit:       "count",
			Timestamp:  timestamp,
			Source:     "mysql_collector",
		})
	}

	return metrics, nil
}

// collectInnoDBMetrics 采集 InnoDB 指标
func (c *MySQLCollector) collectInnoDBMetrics(ctx context.Context, db *sql.DB, taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	// InnoDB 相关指标
	innodbMetrics := map[string]string{
		"Innodb_buffer_pool_size":         "mysql_innodb_buffer_pool_size",
		"Innodb_buffer_pool_pages_total":  "mysql_innodb_buffer_pool_pages_total",
		"Innodb_buffer_pool_pages_free":   "mysql_innodb_buffer_pool_pages_free",
		"Innodb_buffer_pool_pages_dirty":  "mysql_innodb_buffer_pool_pages_dirty",
		"Innodb_buffer_pool_read_requests": "mysql_innodb_buffer_pool_read_requests",
		"Innodb_buffer_pool_reads":        "mysql_innodb_buffer_pool_reads",
	}

	for statusName, metricName := range innodbMetrics {
		var value int64
		err := db.QueryRowContext(ctx, fmt.Sprintf("SHOW STATUS LIKE '%s'", statusName)).Scan(nil, &value)
		if err != nil {
			continue
		}

		metrics = append(metrics, &task.MetricData{
			TaskID:     taskID,
			MetricName: metricName,
			Value:      float64(value),
			Unit:       "count",
			Timestamp:  timestamp,
			Source:     "mysql_collector",
		})
	}

	return metrics, nil
}
