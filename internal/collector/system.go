package collector

import (
	"context"
	"fmt"
	"time"

	"aiops/internal/task"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
	"go.uber.org/zap"
)

// SystemCollector 系统指标采集器
type SystemCollector struct {
	logger *zap.Logger
}

// NewSystemCollector 创建系统采集器
func NewSystemCollector(logger *zap.Logger) *SystemCollector {
	return &SystemCollector{
		logger: logger,
	}
}

// Collect 采集系统指标
func (c *SystemCollector) Collect(ctx context.Context, taskID string, config map[string]interface{}) ([]*task.MetricData, error) {
	var metrics []*task.MetricData
	timestamp := time.Now()

	// 获取配置中的指标类型
	metricsToCollect, ok := config["metrics"].([]interface{})
	if !ok {
		metricsToCollect = []interface{}{"cpu", "memory", "disk", "network"}
	}

	for _, metricType := range metricsToCollect {
		switch metricType.(string) {
		case "cpu":
			cpuMetrics, err := c.collectCPUMetrics(taskID, timestamp)
			if err != nil {
				c.logger.Error("采集 CPU 指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, cpuMetrics...)

		case "memory":
			memMetrics, err := c.collectMemoryMetrics(taskID, timestamp)
			if err != nil {
				c.logger.Error("采集内存指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, memMetrics...)

		case "disk":
			diskMetrics, err := c.collectDiskMetrics(taskID, timestamp)
			if err != nil {
				c.logger.Error("采集磁盘指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, diskMetrics...)

		case "network":
			netMetrics, err := c.collectNetworkMetrics(taskID, timestamp)
			if err != nil {
				c.logger.Error("采集网络指标失败", zap.Error(err))
				continue
			}
			metrics = append(metrics, netMetrics...)
		}
	}

	c.logger.Info("系统指标采集完成", zap.String("task_id", taskID), zap.Int("metrics_count", len(metrics)))
	return metrics, nil
}

// collectCPUMetrics 采集 CPU 指标
func (c *SystemCollector) collectCPUMetrics(taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	// CPU 使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return nil, fmt.Errorf("获取 CPU 使用率失败: %w", err)
	}

	if len(cpuPercent) > 0 {
		metrics = append(metrics, &task.MetricData{
			TaskID:     taskID,
			MetricName: "cpu_usage_percent",
			Value:      cpuPercent[0],
			Unit:       "percent",
			Timestamp:  timestamp,
			Source:     "system_collector",
		})
	}

	// CPU 核心数
	cpuCount, err := cpu.Counts(true)
	if err == nil {
		metrics = append(metrics, &task.MetricData{
			TaskID:     taskID,
			MetricName: "cpu_cores",
			Value:      float64(cpuCount),
			Unit:       "count",
			Timestamp:  timestamp,
			Source:     "system_collector",
		})
	}

	return metrics, nil
}

// collectMemoryMetrics 采集内存指标
func (c *SystemCollector) collectMemoryMetrics(taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	vmStat, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("获取内存信息失败: %w", err)
	}

	metrics = append(metrics, []*task.MetricData{
		{
			TaskID:     taskID,
			MetricName: "memory_total",
			Value:      float64(vmStat.Total),
			Unit:       "bytes",
			Timestamp:  timestamp,
			Source:     "system_collector",
		},
		{
			TaskID:     taskID,
			MetricName: "memory_used",
			Value:      float64(vmStat.Used),
			Unit:       "bytes",
			Timestamp:  timestamp,
			Source:     "system_collector",
		},
		{
			TaskID:     taskID,
			MetricName: "memory_usage_percent",
			Value:      vmStat.UsedPercent,
			Unit:       "percent",
			Timestamp:  timestamp,
			Source:     "system_collector",
		},
		{
			TaskID:     taskID,
			MetricName: "memory_available",
			Value:      float64(vmStat.Available),
			Unit:       "bytes",
			Timestamp:  timestamp,
			Source:     "system_collector",
		},
	}...)

	return metrics, nil
}

// collectDiskMetrics 采集磁盘指标
func (c *SystemCollector) collectDiskMetrics(taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	diskStat, err := disk.Usage("/")
	if err != nil {
		return nil, fmt.Errorf("获取磁盘信息失败: %w", err)
	}

	metrics = append(metrics, []*task.MetricData{
		{
			TaskID:     taskID,
			MetricName: "disk_total",
			Value:      float64(diskStat.Total),
			Unit:       "bytes",
			Timestamp:  timestamp,
			Source:     "system_collector",
			Tags:       map[string]interface{}{"path": "/"},
		},
		{
			TaskID:     taskID,
			MetricName: "disk_used",
			Value:      float64(diskStat.Used),
			Unit:       "bytes",
			Timestamp:  timestamp,
			Source:     "system_collector",
			Tags:       map[string]interface{}{"path": "/"},
		},
		{
			TaskID:     taskID,
			MetricName: "disk_usage_percent",
			Value:      diskStat.UsedPercent,
			Unit:       "percent",
			Timestamp:  timestamp,
			Source:     "system_collector",
			Tags:       map[string]interface{}{"path": "/"},
		},
	}...)

	return metrics, nil
}

// collectNetworkMetrics 采集网络指标
func (c *SystemCollector) collectNetworkMetrics(taskID string, timestamp time.Time) ([]*task.MetricData, error) {
	var metrics []*task.MetricData

	netStats, err := net.IOCounters(false)
	if err != nil {
		return nil, fmt.Errorf("获取网络信息失败: %w", err)
	}

	if len(netStats) > 0 {
		stat := netStats[0]
		metrics = append(metrics, []*task.MetricData{
			{
				TaskID:     taskID,
				MetricName: "network_bytes_sent",
				Value:      float64(stat.BytesSent),
				Unit:       "bytes",
				Timestamp:  timestamp,
				Source:     "system_collector",
			},
			{
				TaskID:     taskID,
				MetricName: "network_bytes_recv",
				Value:      float64(stat.BytesRecv),
				Unit:       "bytes",
				Timestamp:  timestamp,
				Source:     "system_collector",
			},
			{
				TaskID:     taskID,
				MetricName: "network_packets_sent",
				Value:      float64(stat.PacketsSent),
				Unit:       "count",
				Timestamp:  timestamp,
				Source:     "system_collector",
			},
			{
				TaskID:     taskID,
				MetricName: "network_packets_recv",
				Value:      float64(stat.PacketsRecv),
				Unit:       "count",
				Timestamp:  timestamp,
				Source:     "system_collector",
			},
		}...)
	}

	return metrics, nil
}
