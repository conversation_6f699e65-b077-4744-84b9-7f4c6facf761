package collector

import (
	"context"
	"fmt"

	"aiops/internal/task"

	"go.uber.org/zap"
)

// Collector 采集器接口
type Collector interface {
	Collect(ctx context.Context, taskID string, config map[string]interface{}) ([]*task.MetricData, error)
}

// Manager 采集器管理器
type Manager struct {
	collectors map[string]Collector
	logger     *zap.Logger
}

// NewManager 创建采集器管理器
func NewManager(logger *zap.Logger) *Manager {
	manager := &Manager{
		collectors: make(map[string]Collector),
		logger:     logger,
	}

	// 注册内置采集器
	manager.registerBuiltinCollectors()

	return manager
}

// registerBuiltinCollectors 注册内置采集器
func (m *Manager) registerBuiltinCollectors() {
	m.collectors["system"] = NewSystemCollector(m.logger)
	m.collectors["mysql"] = NewMySQLCollector(m.logger)
	// 可以在这里添加更多内置采集器
	// m.collectors["redis"] = NewRedisCollector(m.logger)
	// m.collectors["http"] = NewHTTPCollector(m.logger)
	// m.collectors["log"] = NewLogCollector(m.logger)

	m.logger.Info("内置采集器注册完成", zap.Int("count", len(m.collectors)))
}

// RegisterCollector 注册自定义采集器
func (m *Manager) RegisterCollector(name string, collector Collector) {
	m.collectors[name] = collector
	m.logger.Info("注册自定义采集器", zap.String("name", name))
}

// GetCollector 获取采集器
func (m *Manager) GetCollector(name string) (Collector, error) {
	collector, exists := m.collectors[name]
	if !exists {
		return nil, fmt.Errorf("采集器 '%s' 不存在", name)
	}
	return collector, nil
}

// ListCollectors 列出所有可用的采集器
func (m *Manager) ListCollectors() []string {
	var names []string
	for name := range m.collectors {
		names = append(names, name)
	}
	return names
}

// Collect 执行采集
func (m *Manager) Collect(ctx context.Context, taskID string, collectorType string, config map[string]interface{}) ([]*task.MetricData, error) {
	collector, err := m.GetCollector(collectorType)
	if err != nil {
		return nil, err
	}

	m.logger.Info("开始采集指标", 
		zap.String("task_id", taskID), 
		zap.String("collector_type", collectorType))

	metrics, err := collector.Collect(ctx, taskID, config)
	if err != nil {
		m.logger.Error("采集指标失败", 
			zap.String("task_id", taskID), 
			zap.String("collector_type", collectorType), 
			zap.Error(err))
		return nil, err
	}

	m.logger.Info("采集指标完成", 
		zap.String("task_id", taskID), 
		zap.String("collector_type", collectorType), 
		zap.Int("metrics_count", len(metrics)))

	return metrics, nil
}
