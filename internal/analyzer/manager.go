package analyzer

import (
	"context"
	"fmt"

	"aiops/internal/storage"

	"go.uber.org/zap"
)

// Manager 分析器管理器
type Manager struct {
	analyzers map[string]Analyzer
	storage   storage.Storage
	logger    *zap.Logger
}

// NewManager 创建分析器管理器
func NewManager(storage storage.Storage, logger *zap.Logger) *Manager {
	manager := &Manager{
		analyzers: make(map[string]Analyzer),
		storage:   storage,
		logger:    logger,
	}

	// 注册内置分析器
	manager.registerBuiltinAnalyzers()

	return manager
}

// registerBuiltinAnalyzers 注册内置分析器
func (m *Manager) registerBuiltinAnalyzers() {
	m.analyzers["anomaly_detection"] = NewAnomalyDetector(m.storage, m.logger)
	m.analyzers["threshold_analysis"] = NewThresholdAnalyzer(m.storage, m.logger)
	// 可以在这里添加更多内置分析器
	// m.analyzers["trend_analysis"] = NewTrendAnalyzer(m.storage, m.logger)
	// m.analyzers["correlation_analysis"] = NewCorrelationAnalyzer(m.storage, m.logger)

	m.logger.Info("内置分析器注册完成", zap.Int("count", len(m.analyzers)))
}

// RegisterAnalyzer 注册自定义分析器
func (m *Manager) RegisterAnalyzer(name string, analyzer Analyzer) {
	m.analyzers[name] = analyzer
	m.logger.Info("注册自定义分析器", zap.String("name", name))
}

// GetAnalyzer 获取分析器
func (m *Manager) GetAnalyzer(name string) (Analyzer, error) {
	analyzer, exists := m.analyzers[name]
	if !exists {
		return nil, fmt.Errorf("分析器 '%s' 不存在", name)
	}
	return analyzer, nil
}

// ListAnalyzers 列出所有可用的分析器
func (m *Manager) ListAnalyzers() []string {
	var names []string
	for name := range m.analyzers {
		names = append(names, name)
	}
	return names
}

// Analyze 执行分析
func (m *Manager) Analyze(ctx context.Context, taskID string, analyzerType string, config map[string]interface{}) (*AnalysisResult, error) {
	analyzer, err := m.GetAnalyzer(analyzerType)
	if err != nil {
		return nil, err
	}

	m.logger.Info("开始执行分析", 
		zap.String("task_id", taskID), 
		zap.String("analyzer_type", analyzerType))

	result, err := analyzer.Analyze(ctx, taskID, config)
	if err != nil {
		m.logger.Error("分析执行失败", 
			zap.String("task_id", taskID), 
			zap.String("analyzer_type", analyzerType), 
			zap.Error(err))
		return nil, err
	}

	m.logger.Info("分析执行完成", 
		zap.String("task_id", taskID), 
		zap.String("analyzer_type", analyzerType), 
		zap.String("status", result.Status))

	return result, nil
}
