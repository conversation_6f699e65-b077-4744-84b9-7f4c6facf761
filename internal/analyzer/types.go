package analyzer

import (
	"context"
	"time"
)

// Analyzer 分析器接口
type Analyzer interface {
	Analyze(ctx context.Context, taskID string, config map[string]interface{}) (*AnalysisResult, error)
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	TaskID    string                 `json:"task_id"`
	Type      string                 `json:"type"`      // 分析类型: anomaly_detection, trend_analysis, correlation_analysis
	Algorithm string                 `json:"algorithm"` // 使用的算法
	Status    string                 `json:"status"`    // 分析状态: normal, anomaly_detected, warning, error
	Message   string                 `json:"message"`   // 分析结果描述
	Severity  string                 `json:"severity"`  // 严重程度: info, warning, error, critical
	Details   map[string]interface{} `json:"details"`   // 详细分析数据
	Timestamp time.Time              `json:"timestamp"` // 分析时间
}
