package analyzer

import (
	"context"
	"fmt"
	"time"

	"aiops/internal/storage"

	"go.uber.org/zap"
)

// ThresholdAnalyzer 阈值分析器
type ThresholdAnalyzer struct {
	storage storage.Storage
	logger  *zap.Logger
}

// NewThresholdAnalyzer 创建阈值分析器
func NewThresholdAnalyzer(storage storage.Storage, logger *zap.Logger) *ThresholdAnalyzer {
	return &ThresholdAnalyzer{
		storage: storage,
		logger:  logger,
	}
}

// Analyze 执行阈值分析
func (t *ThresholdAnalyzer) Analyze(ctx context.Context, taskID string, config map[string]interface{}) (*AnalysisResult, error) {
	// 获取配置参数
	metricName, ok := config["metric"].(string)
	if !ok {
		return nil, fmt.Errorf("指标名称未配置")
	}

	// 获取最新的指标数据
	latestMetric, err := t.storage.GetLatestMetricData(taskID, metricName)
	if err != nil {
		return nil, fmt.<PERSON>rro<PERSON>("获取最新指标数据失败: %w", err)
	}

	currentValue := latestMetric.Value

	// 解析阈值配置
	thresholds, err := t.parseThresholds(config)
	if err != nil {
		return nil, err
	}

	// 执行阈值检查
	result := &AnalysisResult{
		TaskID:    taskID,
		Type:      "threshold_analysis",
		Algorithm: "threshold_check",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"metric_name":    metricName,
			"current_value":  currentValue,
			"thresholds":     thresholds,
		},
	}

	// 检查各个阈值
	violatedThresholds := []map[string]interface{}{}
	maxSeverity := "info"

	for _, threshold := range thresholds {
		violated, severity := t.checkThreshold(currentValue, threshold)
		if violated {
			violatedThresholds = append(violatedThresholds, threshold)
			if t.compareSeverity(severity, maxSeverity) > 0 {
				maxSeverity = severity
			}
		}
	}

	// 设置分析结果
	if len(violatedThresholds) > 0 {
		result.Status = "threshold_violated"
		result.Severity = maxSeverity
		result.Message = fmt.Sprintf("检测到 %d 个阈值违规", len(violatedThresholds))
		result.Details["violated_thresholds"] = violatedThresholds
	} else {
		result.Status = "normal"
		result.Severity = "info"
		result.Message = "所有阈值检查正常"
	}

	t.logger.Info("阈值分析完成", 
		zap.String("task_id", taskID), 
		zap.String("metric", metricName),
		zap.Float64("current_value", currentValue),
		zap.String("status", result.Status),
		zap.Int("violated_count", len(violatedThresholds)))

	return result, nil
}

// parseThresholds 解析阈值配置
func (t *ThresholdAnalyzer) parseThresholds(config map[string]interface{}) ([]map[string]interface{}, error) {
	var thresholds []map[string]interface{}

	// 支持单个阈值配置
	if threshold, ok := config["threshold"].(float64); ok {
		operator, ok := config["operator"].(string)
		if !ok {
			operator = ">" // 默认大于操作符
		}
		severity, ok := config["severity"].(string)
		if !ok {
			severity = "warning" // 默认警告级别
		}

		thresholds = append(thresholds, map[string]interface{}{
			"value":    threshold,
			"operator": operator,
			"severity": severity,
			"name":     "default",
		})
	}

	// 支持多个阈值配置
	if thresholdList, ok := config["thresholds"].([]interface{}); ok {
		for i, item := range thresholdList {
			if thresholdMap, ok := item.(map[string]interface{}); ok {
				// 验证必需字段
				if _, hasValue := thresholdMap["value"]; !hasValue {
					return nil, fmt.Errorf("阈值 %d 缺少 value 字段", i)
				}
				if _, hasOperator := thresholdMap["operator"]; !hasOperator {
					thresholdMap["operator"] = ">" // 默认操作符
				}
				if _, hasSeverity := thresholdMap["severity"]; !hasSeverity {
					thresholdMap["severity"] = "warning" // 默认严重程度
				}
				if _, hasName := thresholdMap["name"]; !hasName {
					thresholdMap["name"] = fmt.Sprintf("threshold_%d", i)
				}

				thresholds = append(thresholds, thresholdMap)
			}
		}
	}

	if len(thresholds) == 0 {
		return nil, fmt.Errorf("未配置阈值")
	}

	return thresholds, nil
}

// checkThreshold 检查单个阈值
func (t *ThresholdAnalyzer) checkThreshold(value float64, threshold map[string]interface{}) (bool, string) {
	thresholdValue, ok := threshold["value"].(float64)
	if !ok {
		return false, "info"
	}

	operator, ok := threshold["operator"].(string)
	if !ok {
		operator = ">"
	}

	severity, ok := threshold["severity"].(string)
	if !ok {
		severity = "warning"
	}

	var violated bool
	switch operator {
	case ">":
		violated = value > thresholdValue
	case ">=":
		violated = value >= thresholdValue
	case "<":
		violated = value < thresholdValue
	case "<=":
		violated = value <= thresholdValue
	case "==":
		violated = value == thresholdValue
	case "!=":
		violated = value != thresholdValue
	default:
		t.logger.Warn("不支持的操作符", zap.String("operator", operator))
		return false, "info"
	}

	return violated, severity
}

// compareSeverity 比较严重程度
func (t *ThresholdAnalyzer) compareSeverity(s1, s2 string) int {
	severityLevels := map[string]int{
		"info":     1,
		"warning":  2,
		"error":    3,
		"critical": 4,
	}

	level1, ok1 := severityLevels[s1]
	level2, ok2 := severityLevels[s2]

	if !ok1 {
		level1 = 1
	}
	if !ok2 {
		level2 = 1
	}

	if level1 > level2 {
		return 1
	} else if level1 < level2 {
		return -1
	}
	return 0
}
