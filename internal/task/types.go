package task

import (
	"time"

	"gorm.io/gorm"
)

// TaskType 任务类型
type TaskType string

const (
	TaskTypeCollect TaskType = "collect" // 采集任务
	TaskTypeAnalyze TaskType = "analyze" // 分析任务
	TaskTypeAlert   TaskType = "alert"   // 告警任务
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending  TaskStatus = "pending"  // 等待中
	TaskStatusRunning  TaskStatus = "running"  // 运行中
	TaskStatusSuccess  TaskStatus = "success"  // 成功
	TaskStatusFailed   TaskStatus = "failed"   // 失败
	TaskStatusDisabled TaskStatus = "disabled" // 已禁用
)

// ScheduleType 调度类型
type ScheduleType string

const (
	ScheduleTypeInterval    ScheduleType = "interval"     // 间隔调度
	ScheduleTypeCron        ScheduleType = "cron"         // Cron 表达式
	ScheduleTypeEventDriven ScheduleType = "event_driven" // 事件驱动
)

// Task 任务模型
type Task struct {
	gorm.Model
	ID          string                 `json:"id" gorm:"uniqueIndex"`
	Name        string                 `json:"name"`
	Type        TaskType               `json:"type"`
	Config      map[string]interface{} `json:"config" gorm:"serializer:json"`
	Schedule    ScheduleConfig         `json:"schedule" gorm:"embedded"`
	Enabled     bool                   `json:"enabled"`
	Status      TaskStatus             `json:"status"`
	LastRunAt   *time.Time             `json:"last_run_at"`
	NextRunAt   *time.Time             `json:"next_run_at"`
	RunCount    int                    `json:"run_count"`
	FailCount   int                    `json:"fail_count"`
	LastError   string                 `json:"last_error"`
	Description string                 `json:"description"`
}

// ScheduleConfig 调度配置
type ScheduleConfig struct {
	Type     ScheduleType `json:"type"`
	Interval string       `json:"interval"`    // "30s", "5m", "1h"
	CronExpr string       `json:"cron_expr"`   // "0 */5 * * * *"
}

// TaskExecution 任务执行记录
type TaskExecution struct {
	gorm.Model
	TaskID      string        `json:"task_id" gorm:"index"`
	Status      TaskStatus    `json:"status"`
	StartedAt   time.Time     `json:"started_at"`
	CompletedAt *time.Time    `json:"completed_at"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error"`
	Output      string        `json:"output" gorm:"type:text"`
}

// MetricData 指标数据
type MetricData struct {
	gorm.Model
	TaskID      string                 `json:"task_id" gorm:"index"`
	MetricName  string                 `json:"metric_name" gorm:"index"`
	Value       float64                `json:"value"`
	Unit        string                 `json:"unit"`
	Tags        map[string]interface{} `json:"tags" gorm:"serializer:json"`
	Timestamp   time.Time              `json:"timestamp" gorm:"index"`
	Source      string                 `json:"source"`
}

// AlertEvent 告警事件
type AlertEvent struct {
	gorm.Model
	TaskID      string                 `json:"task_id" gorm:"index"`
	AlertName   string                 `json:"alert_name"`
	Level       string                 `json:"level"` // info, warning, error, critical
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details" gorm:"serializer:json"`
	Timestamp   time.Time              `json:"timestamp" gorm:"index"`
	Resolved    bool                   `json:"resolved"`
	ResolvedAt  *time.Time             `json:"resolved_at"`
}

// LogEntry 日志条目
type LogEntry struct {
	gorm.Model
	TaskID    string    `json:"task_id" gorm:"index"`
	Level     string    `json:"level" gorm:"index"`
	Message   string    `json:"message"`
	Source    string    `json:"source"`
	Timestamp time.Time `json:"timestamp" gorm:"index"`
	Context   string    `json:"context" gorm:"type:text"`
}
