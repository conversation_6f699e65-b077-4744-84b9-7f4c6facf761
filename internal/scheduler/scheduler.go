package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/internal/storage"
	"aiops/internal/task"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// Scheduler 任务调度器
type Scheduler interface {
	Start(ctx context.Context) error
	Stop() error
	ScheduleTask(task *task.Task) error
	UnscheduleTask(taskID string) error
	ExecuteTaskNow(taskID string) error
}

// scheduler 调度器实现
type scheduler struct {
	storage   storage.Storage
	logger    *zap.Logger
	cron      *cron.Cron
	tasks     map[string]*task.Task
	taskMutex sync.RWMutex
	workers   chan struct{} // 工作池
	ctx       context.Context
	cancel    context.CancelFunc
}

// New 创建调度器实例
func New(storage storage.Storage, logger *zap.Logger) Scheduler {
	return &scheduler{
		storage: storage,
		logger:  logger,
		cron:    cron.New(cron.WithSeconds()),
		tasks:   make(map[string]*task.Task),
		workers: make(chan struct{}, 10), // 最多10个并发任务
	}
}

// Start 启动调度器
func (s *scheduler) Start(ctx context.Context) error {
	s.ctx, s.cancel = context.WithCancel(ctx)

	// 启动 cron 调度器
	s.cron.Start()

	// 加载现有任务
	if err := s.loadTasks(); err != nil {
		return fmt.Errorf("加载任务失败: %w", err)
	}

	// 启动定期检查
	go s.periodicCheck()

	s.logger.Info("任务调度器启动成功")
	return nil
}

// Stop 停止调度器
func (s *scheduler) Stop() error {
	if s.cancel != nil {
		s.cancel()
	}
	s.cron.Stop()
	s.logger.Info("任务调度器已停止")
	return nil
}

// loadTasks 加载所有启用的任务
func (s *scheduler) loadTasks() error {
	tasks, err := s.storage.GetEnabledTasks()
	if err != nil {
		return err
	}

	for _, t := range tasks {
		if err := s.scheduleTaskInternal(t); err != nil {
			s.logger.Error("调度任务失败", zap.String("task_id", t.ID), zap.Error(err))
		}
	}

	s.logger.Info("加载任务完成", zap.Int("count", len(tasks)))
	return nil
}

// ScheduleTask 调度任务
func (s *scheduler) ScheduleTask(t *task.Task) error {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	return s.scheduleTaskInternal(t)
}

// scheduleTaskInternal 内部调度任务方法
func (s *scheduler) scheduleTaskInternal(t *task.Task) error {
	// 如果任务已存在，先移除
	if _, exists := s.tasks[t.ID]; exists {
		s.cron.Remove(cron.EntryID(0)) // 简化处理，实际应该记录 EntryID
	}

	// 根据调度类型添加任务
	switch t.Schedule.Type {
	case task.ScheduleTypeInterval:
		interval, err := time.ParseDuration(t.Schedule.Interval)
		if err != nil {
			return fmt.Errorf("解析间隔时间失败: %w", err)
		}

		// 使用 cron 表达式表示间隔
		cronExpr := fmt.Sprintf("@every %s", interval.String())
		_, err = s.cron.AddFunc(cronExpr, func() {
			s.executeTask(t.ID)
		})
		if err != nil {
			return fmt.Errorf("添加间隔任务失败: %w", err)
		}

	case task.ScheduleTypeCron:
		_, err := s.cron.AddFunc(t.Schedule.CronExpr, func() {
			s.executeTask(t.ID)
		})
		if err != nil {
			return fmt.Errorf("添加 cron 任务失败: %w", err)
		}

	case task.ScheduleTypeEventDriven:
		// 事件驱动任务不需要定时调度
		break

	default:
		return fmt.Errorf("不支持的调度类型: %s", t.Schedule.Type)
	}

	s.tasks[t.ID] = t
	s.logger.Info("任务调度成功", zap.String("task_id", t.ID), zap.String("type", string(t.Type)))
	return nil
}

// UnscheduleTask 取消调度任务
func (s *scheduler) UnscheduleTask(taskID string) error {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	delete(s.tasks, taskID)
	// 简化处理，实际应该移除对应的 cron 条目
	s.logger.Info("任务取消调度", zap.String("task_id", taskID))
	return nil
}

// ExecuteTaskNow 立即执行任务
func (s *scheduler) ExecuteTaskNow(taskID string) error {
	go s.executeTask(taskID)
	return nil
}

// executeTask 执行任务
func (s *scheduler) executeTask(taskID string) {
	// 获取工作池令牌
	s.workers <- struct{}{}
	defer func() { <-s.workers }()

	// 获取任务
	t, err := s.storage.GetTask(taskID)
	if err != nil {
		s.logger.Error("获取任务失败", zap.String("task_id", taskID), zap.Error(err))
		return
	}

	// 创建执行记录
	execution := &task.TaskExecution{
		TaskID:    taskID,
		Status:    task.TaskStatusRunning,
		StartedAt: time.Now(),
	}

	if err := s.storage.CreateTaskExecution(execution); err != nil {
		s.logger.Error("创建执行记录失败", zap.String("task_id", taskID), zap.Error(err))
		return
	}

	s.logger.Info("开始执行任务", zap.String("task_id", taskID), zap.String("type", string(t.Type)))

	// 执行任务
	var execErr error
	switch t.Type {
	case task.TaskTypeCollect:
		execErr = s.executeCollectTask(t)
	case task.TaskTypeAnalyze:
		execErr = s.executeAnalyzeTask(t)
	case task.TaskTypeAlert:
		execErr = s.executeAlertTask(t)
	default:
		execErr = fmt.Errorf("不支持的任务类型: %s", t.Type)
	}

	// 更新执行记录
	now := time.Now()
	execution.CompletedAt = &now
	execution.Duration = now.Sub(execution.StartedAt)

	if execErr != nil {
		execution.Status = task.TaskStatusFailed
		execution.Error = execErr.Error()
		t.FailCount++
		t.LastError = execErr.Error()
		s.logger.Error("任务执行失败", zap.String("task_id", taskID), zap.Error(execErr))
	} else {
		execution.Status = task.TaskStatusSuccess
		t.FailCount = 0 // 重置失败计数
		t.LastError = ""
		s.logger.Info("任务执行成功", zap.String("task_id", taskID))
	}

	// 更新任务状态
	t.LastRunAt = &execution.StartedAt
	t.RunCount++
	if err := s.storage.UpdateTask(t); err != nil {
		s.logger.Error("更新任务状态失败", zap.String("task_id", taskID), zap.Error(err))
	}

	// 更新执行记录
	if err := s.storage.UpdateTaskExecution(execution); err != nil {
		s.logger.Error("更新执行记录失败", zap.String("task_id", taskID), zap.Error(err))
	}
}

// executeCollectTask 执行采集任务
func (s *scheduler) executeCollectTask(t *task.Task) error {
	// 这里将实现具体的采集逻辑
	// 根据配置中的 collector_type 调用相应的采集器
	collectorType, ok := t.Config["collector_type"].(string)
	if !ok {
		return fmt.Errorf("采集器类型未配置")
	}

	s.logger.Info("执行采集任务", zap.String("collector_type", collectorType))

	// 模拟采集数据
	metricData := &task.MetricData{
		TaskID:     t.ID,
		MetricName: "sample_metric",
		Value:      100.0,
		Unit:       "count",
		Timestamp:  time.Now(),
		Source:     collectorType,
	}

	return s.storage.SaveMetricData(metricData)
}

// executeAnalyzeTask 执行分析任务
func (s *scheduler) executeAnalyzeTask(t *task.Task) error {
	// 这里将实现具体的分析逻辑
	analyzerType, ok := t.Config["analyzer_type"].(string)
	if !ok {
		return fmt.Errorf("分析器类型未配置")
	}

	s.logger.Info("执行分析任务", zap.String("analyzer_type", analyzerType))

	// 模拟分析结果
	// 实际实现中会根据分析类型执行相应的分析算法

	return nil
}

// executeAlertTask 执行告警任务
func (s *scheduler) executeAlertTask(t *task.Task) error {
	// 这里将实现具体的告警逻辑
	alertType, ok := t.Config["alert_type"].(string)
	if !ok {
		return fmt.Errorf("告警类型未配置")
	}

	s.logger.Info("执行告警任务", zap.String("alert_type", alertType))

	// 模拟告警事件
	alertEvent := &task.AlertEvent{
		TaskID:    t.ID,
		AlertName: "sample_alert",
		Level:     "warning",
		Message:   "这是一个示例告警",
		Timestamp: time.Now(),
		Resolved:  false,
	}

	return s.storage.CreateAlertEvent(alertEvent)
}

// periodicCheck 定期检查任务状态
func (s *scheduler) periodicCheck() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			// 检查是否有新的任务需要调度
			// 这里可以添加更多的定期检查逻辑
		}
	}
}
