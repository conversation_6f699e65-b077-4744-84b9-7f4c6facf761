package database

import (
	"fmt"
	"os"
	"path/filepath"

	"aiops/internal/config"
	"aiops/internal/task"

	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize 初始化数据库
func Initialize(cfg config.DatabaseConfig, log *zap.Logger) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// 确保数据目录存在
	if cfg.Driver == "sqlite" {
		dir := filepath.Dir(cfg.DSN)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, fmt.Errorf("创建数据目录失败: %w", err)
		}
	}

	// 配置 GORM 日志
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	}

	// 根据驱动类型初始化数据库
	switch cfg.Driver {
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(cfg.DSN), gormConfig)
	default:
		return nil, fmt.Errorf("不支持的数据库驱动: %s", cfg.Driver)
	}

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 自动迁移数据库表
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}

	log.Info("数据库初始化成功", zap.String("driver", cfg.Driver))
	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&task.Task{},
		&task.TaskExecution{},
		&task.MetricData{},
		&task.AlertEvent{},
		&task.LogEntry{},
	)
}
