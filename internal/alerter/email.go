package alerter

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"aiops/internal/config"

	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
)

// EmailAlerter 邮件告警器
type EmailAlerter struct {
	config config.EmailConfig
	logger *zap.Logger
}

// NewEmailAlerter 创建邮件告警器
func NewEmailAlerter(config config.EmailConfig, logger *zap.Logger) *EmailAlerter {
	return &EmailAlerter{
		config: config,
		logger: logger,
	}
}

// SendAlert 发送告警
func (e *EmailAlerter) SendAlert(ctx context.Context, taskID string, config map[string]interface{}) error {
	// 获取收件人列表
	recipients, ok := config["recipients"].([]interface{})
	if !ok {
		return fmt.Errorf("收件人列表未配置")
	}

	// 转换收件人列表
	var emailList []string
	for _, recipient := range recipients {
		if email, ok := recipient.(string); ok {
			emailList = append(emailList, email)
		}
	}

	if len(emailList) == 0 {
		return fmt.Errorf("没有有效的收件人邮箱")
	}

	// 获取告警条件
	conditions, ok := config["conditions"].([]interface{})
	if !ok {
		return fmt.Errorf("告警条件未配置")
	}

	// 检查告警条件
	triggered, alertDetails, err := e.checkAlertConditions(taskID, conditions)
	if err != nil {
		return fmt.Errorf("检查告警条件失败: %w", err)
	}

	if !triggered {
		e.logger.Info("告警条件未触发", zap.String("task_id", taskID))
		return nil
	}

	// 构建邮件内容
	subject := fmt.Sprintf("[AIOps 告警] 任务 %s 触发告警", taskID)
	body := e.buildEmailBody(taskID, alertDetails)

	// 发送邮件
	if err := e.sendEmail(emailList, subject, body); err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	e.logger.Info("邮件告警发送成功",
		zap.String("task_id", taskID),
		zap.Strings("recipients", emailList))

	return nil
}

// checkAlertConditions 检查告警条件
func (e *EmailAlerter) checkAlertConditions(taskID string, conditions []interface{}) (bool, []map[string]interface{}, error) {
	var alertDetails []map[string]interface{}

	for _, condition := range conditions {
		conditionMap, ok := condition.(map[string]interface{})
		if !ok {
			continue
		}

		metric, ok := conditionMap["metric"].(string)
		if !ok {
			continue
		}

		operator, ok := conditionMap["operator"].(string)
		if !ok {
			continue
		}

		value, ok := conditionMap["value"].(float64)
		if !ok {
			// 尝试从字符串转换
			if valueStr, ok := conditionMap["value"].(string); ok {
				if v, err := strconv.ParseFloat(valueStr, 64); err == nil {
					value = v
				} else {
					continue
				}
			} else {
				continue
			}
		}

		// 这里应该从存储中获取最新的指标数据进行比较
		// 为了简化，这里模拟条件检查
		triggered := e.evaluateCondition(metric, operator, value)

		if triggered {
			alertDetails = append(alertDetails, map[string]interface{}{
				"metric":   metric,
				"operator": operator,
				"value":    value,
				"current":  value + 10, // 模拟当前值
			})
		}
	}

	return len(alertDetails) > 0, alertDetails, nil
}

// evaluateCondition 评估条件（简化实现）
func (e *EmailAlerter) evaluateCondition(metric, operator string, value float64) bool {
	// 这里应该从存储中获取实际的指标数据进行比较
	// 为了演示，这里返回随机结果
	return time.Now().Unix()%2 == 0
}

// buildEmailBody 构建邮件正文
func (e *EmailAlerter) buildEmailBody(taskID string, alertDetails []map[string]interface{}) string {
	body := fmt.Sprintf(`
AIOps 监控系统告警通知

任务ID: %s
告警时间: %s

触发的告警条件:
`, taskID, time.Now().Format("2006-01-02 15:04:05"))

	for i, detail := range alertDetails {
		body += fmt.Sprintf(`
%d. 指标: %s
   条件: %s %v
   当前值: %v
`, i+1, detail["metric"], detail["operator"], detail["value"], detail["current"])
	}

	body += `

请及时处理相关问题。

---
AIOps 监控系统
`

	return body
}

// sendEmail 发送邮件
func (e *EmailAlerter) sendEmail(recipients []string, subject, body string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", e.config.FromEmail)
	m.SetHeader("To", recipients...)
	m.SetHeader("Subject", subject)
	m.SetBody("text/plain", body)

	d := gomail.NewDialer(e.config.SMTPHost, e.config.SMTPPort, e.config.SMTPUsername, e.config.SMTPPassword)

	if err := d.DialAndSend(m); err != nil {
		return err
	}

	return nil
}
