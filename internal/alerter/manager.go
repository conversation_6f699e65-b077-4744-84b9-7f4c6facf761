package alerter

import (
	"context"
	"fmt"

	"aiops/internal/config"

	"go.uber.org/zap"
)

// Alerter 告警器接口
type Alerter interface {
	SendAlert(ctx context.Context, taskID string, config map[string]interface{}) error
}

// Manager 告警器管理器
type Manager struct {
	alerters map[string]Alerter
	logger   *zap.Logger
}

// NewManager 创建告警器管理器
func NewManager(emailConfig config.EmailConfig, logger *zap.Logger) *Manager {
	manager := &Manager{
		alerters: make(map[string]Alerter),
		logger:   logger,
	}

	// 注册内置告警器
	manager.registerBuiltinAlerters(emailConfig)

	return manager
}

// registerBuiltinAlerters 注册内置告警器
func (m *Manager) registerBuiltinAlerters(emailConfig config.EmailConfig) {
	m.alerters["email"] = NewEmailAlerter(emailConfig, m.logger)
	// 可以在这里添加更多内置告警器
	// m.alerters["sms"] = NewSMSAlerter(smsConfig, m.logger)
	// m.alerters["webhook"] = NewWebhookAlerter(m.logger)
	// m.alerters["slack"] = NewSlackAlerter(slackConfig, m.logger)

	m.logger.Info("内置告警器注册完成", zap.Int("count", len(m.alerters)))
}

// RegisterAlerter 注册自定义告警器
func (m *Manager) RegisterAlerter(name string, alerter Alerter) {
	m.alerters[name] = alerter
	m.logger.Info("注册自定义告警器", zap.String("name", name))
}

// GetAlerter 获取告警器
func (m *Manager) GetAlerter(name string) (Alerter, error) {
	alerter, exists := m.alerters[name]
	if !exists {
		return nil, fmt.Errorf("告警器 '%s' 不存在", name)
	}
	return alerter, nil
}

// ListAlerters 列出所有可用的告警器
func (m *Manager) ListAlerters() []string {
	var names []string
	for name := range m.alerters {
		names = append(names, name)
	}
	return names
}

// SendAlert 发送告警
func (m *Manager) SendAlert(ctx context.Context, taskID string, alerterType string, config map[string]interface{}) error {
	alerter, err := m.GetAlerter(alerterType)
	if err != nil {
		return err
	}

	m.logger.Info("开始发送告警", 
		zap.String("task_id", taskID), 
		zap.String("alerter_type", alerterType))

	if err := alerter.SendAlert(ctx, taskID, config); err != nil {
		m.logger.Error("发送告警失败", 
			zap.String("task_id", taskID), 
			zap.String("alerter_type", alerterType), 
			zap.Error(err))
		return err
	}

	m.logger.Info("告警发送完成", 
		zap.String("task_id", taskID), 
		zap.String("alerter_type", alerterType))

	return nil
}
