package storage

import (
	"time"

	"aiops/internal/task"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Storage 存储层接口
type Storage interface {
	// 任务管理
	CreateTask(task *task.Task) error
	GetTask(id string) (*task.Task, error)
	GetTasks() ([]*task.Task, error)
	GetTasksByType(taskType task.TaskType) ([]*task.Task, error)
	GetEnabledTasks() ([]*task.Task, error)
	UpdateTask(task *task.Task) error
	DeleteTask(id string) error

	// 任务执行记录
	CreateTaskExecution(execution *task.TaskExecution) error
	GetTaskExecutions(taskID string, limit int) ([]*task.TaskExecution, error)
	UpdateTaskExecution(execution *task.TaskExecution) error

	// 指标数据
	SaveMetricData(data *task.MetricData) error
	GetMetricData(taskID, metricName string, start, end time.Time) ([]*task.MetricData, error)
	GetLatestMetricData(taskID, metricName string) (*task.MetricData, error)

	// 告警事件
	CreateAlertEvent(event *task.AlertEvent) error
	GetAlertEvents(taskID string, limit int) ([]*task.AlertEvent, error)
	GetUnresolvedAlerts() ([]*task.AlertEvent, error)
	ResolveAlert(id uint) error

	// 日志
	SaveLogEntry(entry *task.LogEntry) error
	GetLogEntries(taskID string, level string, limit int) ([]*task.LogEntry, error)
}

// storage 存储层实现
type storage struct {
	db     *gorm.DB
	logger *zap.Logger
}

// New 创建存储层实例
func New(db *gorm.DB, logger *zap.Logger) Storage {
	return &storage{
		db:     db,
		logger: logger,
	}
}

// CreateTask 创建任务
func (s *storage) CreateTask(t *task.Task) error {
	return s.db.Create(t).Error
}

// GetTask 获取任务
func (s *storage) GetTask(id string) (*task.Task, error) {
	var t task.Task
	err := s.db.Where("id = ?", id).First(&t).Error
	if err != nil {
		return nil, err
	}
	return &t, nil
}

// GetTasks 获取所有任务
func (s *storage) GetTasks() ([]*task.Task, error) {
	var tasks []*task.Task
	err := s.db.Find(&tasks).Error
	return tasks, err
}

// GetTasksByType 根据类型获取任务
func (s *storage) GetTasksByType(taskType task.TaskType) ([]*task.Task, error) {
	var tasks []*task.Task
	err := s.db.Where("type = ?", taskType).Find(&tasks).Error
	return tasks, err
}

// GetEnabledTasks 获取启用的任务
func (s *storage) GetEnabledTasks() ([]*task.Task, error) {
	var tasks []*task.Task
	err := s.db.Where("enabled = ?", true).Find(&tasks).Error
	return tasks, err
}

// UpdateTask 更新任务
func (s *storage) UpdateTask(t *task.Task) error {
	return s.db.Save(t).Error
}

// DeleteTask 删除任务
func (s *storage) DeleteTask(id string) error {
	return s.db.Where("id = ?", id).Delete(&task.Task{}).Error
}

// CreateTaskExecution 创建任务执行记录
func (s *storage) CreateTaskExecution(execution *task.TaskExecution) error {
	return s.db.Create(execution).Error
}

// GetTaskExecutions 获取任务执行记录
func (s *storage) GetTaskExecutions(taskID string, limit int) ([]*task.TaskExecution, error) {
	var executions []*task.TaskExecution
	query := s.db.Where("task_id = ?", taskID).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&executions).Error
	return executions, err
}

// UpdateTaskExecution 更新任务执行记录
func (s *storage) UpdateTaskExecution(execution *task.TaskExecution) error {
	return s.db.Save(execution).Error
}

// SaveMetricData 保存指标数据
func (s *storage) SaveMetricData(data *task.MetricData) error {
	return s.db.Create(data).Error
}

// GetMetricData 获取指标数据
func (s *storage) GetMetricData(taskID, metricName string, start, end time.Time) ([]*task.MetricData, error) {
	var data []*task.MetricData
	err := s.db.Where("task_id = ? AND metric_name = ? AND timestamp BETWEEN ? AND ?",
		taskID, metricName, start, end).Order("timestamp ASC").Find(&data).Error
	return data, err
}

// GetLatestMetricData 获取最新指标数据
func (s *storage) GetLatestMetricData(taskID, metricName string) (*task.MetricData, error) {
	var data task.MetricData
	err := s.db.Where("task_id = ? AND metric_name = ?", taskID, metricName).
		Order("timestamp DESC").First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// CreateAlertEvent 创建告警事件
func (s *storage) CreateAlertEvent(event *task.AlertEvent) error {
	return s.db.Create(event).Error
}

// GetAlertEvents 获取告警事件
func (s *storage) GetAlertEvents(taskID string, limit int) ([]*task.AlertEvent, error) {
	var events []*task.AlertEvent
	query := s.db.Where("task_id = ?", taskID).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&events).Error
	return events, err
}

// GetUnresolvedAlerts 获取未解决的告警
func (s *storage) GetUnresolvedAlerts() ([]*task.AlertEvent, error) {
	var events []*task.AlertEvent
	err := s.db.Where("resolved = ?", false).Order("created_at DESC").Find(&events).Error
	return events, err
}

// ResolveAlert 解决告警
func (s *storage) ResolveAlert(id uint) error {
	now := time.Now()
	return s.db.Model(&task.AlertEvent{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"resolved":    true,
			"resolved_at": &now,
		}).Error
}

// SaveLogEntry 保存日志条目
func (s *storage) SaveLogEntry(entry *task.LogEntry) error {
	return s.db.Create(entry).Error
}

// GetLogEntries 获取日志条目
func (s *storage) GetLogEntries(taskID string, level string, limit int) ([]*task.LogEntry, error) {
	var entries []*task.LogEntry
	query := s.db.Where("task_id = ?", taskID)
	if level != "" {
		query = query.Where("level = ?", level)
	}
	query = query.Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&entries).Error
	return entries, err
}
