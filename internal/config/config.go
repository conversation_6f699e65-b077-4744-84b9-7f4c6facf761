package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Tasks    TasksConfig    `mapstructure:"tasks"`
	Email    EmailConfig    `mapstructure:"email"`
}

// ServerConfig HTTP 服务器配置
type ServerConfig struct {
	Port int `mapstructure:"port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver string `mapstructure:"driver"`
	DSN    string `mapstructure:"dsn"`
}

// TasksConfig 任务配置
type TasksConfig struct {
	DefaultInterval time.Duration `mapstructure:"default_interval"`
	MaxConcurrent   int           `mapstructure:"max_concurrent"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTPHost     string `mapstructure:"smtp_host"`
	SMTPPort     int    `mapstructure:"smtp_port"`
	SMTPUsername string `mapstructure:"smtp_username"`
	SMTPPassword string `mapstructure:"smtp_password"`
	FromEmail    string `mapstructure:"from_email"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/aiops.db")
	viper.SetDefault("tasks.default_interval", "30s")
	viper.SetDefault("tasks.max_concurrent", 10)
	viper.SetDefault("email.smtp_port", 587)
}
