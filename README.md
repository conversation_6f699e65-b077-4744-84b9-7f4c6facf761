# AIOps 监控系统 - 简化架构版本

AIOps 监控系统是一个现代化的 IT 基础设施监控解决方案，采用简化的单体架构设计，基于任务管理模式，为中小型企业提供高效、可靠的监控服务。

## 🚀 核心特性

### 简化架构
- **单体应用**：一个二进制文件包含所有功能
- **任务导向**：采集、分析、告警三种任务类型
- **本地存储**：SQLite 数据库，无外部依赖
- **统一配置**：单一 YAML 配置文件

### 监控能力
- **系统监控**：CPU、内存、磁盘、网络指标
- **数据库监控**：MySQL 性能指标采集
- **异常检测**：统计分析、Z-Score、IQR 算法
- **阈值告警**：多级阈值检查和通知

### 易用性
- **Web UI**：直观的管理界面
- **REST API**：完整的 API 接口
- **灵活调度**：间隔、Cron、事件驱动
- **邮件告警**：SMTP 邮件通知

## 🏗️ 系统架构

```
┌─────────────────────────────────────────┐
│           AIOps 监控系统                │
├─────────────────────────────────────────┤
│  Web UI (管理界面)                      │
├─────────────────────────────────────────┤
│  HTTP API (任务管理)                    │
├─────────────────────────────────────────┤
│  任务调度器 (Task Scheduler)            │
├─────────────────────────────────────────┤
│  内置功能模块                           │
│  ├── 系统监控模块                       │
│  ├── 数据库监控模块                     │
│  ├── 异常检测模块                       │
│  ├── 阈值分析模块                       │
│  └── 邮件告警模块                       │
├─────────────────────────────────────────┤
│  数据存储 (SQLite)                      │
├─────────────────────────────────────────┤
│  配置管理                               │
└─────────────────────────────────────────┘
```

### 核心组件

- **任务调度器**：统一的任务管理和调度
- **采集器管理器**：内置多种数据采集器
- **分析器管理器**：异常检测和阈值分析
- **告警器管理器**：多渠道告警通知
- **存储层**：SQLite 数据库存储
- **API 服务器**：RESTful HTTP 接口

## 🚀 快速开始

### 方式一：直接运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd aiops

# 2. 安装依赖
make deps

# 3. 编译运行
make run
```

### 方式二：使用 Makefile

```bash
# 查看所有可用命令
make help

# 编译应用
make build

# 运行应用
make run

# 运行测试
make test

# 清理文件
make clean
```

### 访问系统

启动后访问以下地址：

- **Web UI**: http://localhost:8080
- **API 文档**: http://localhost:8080/health
- **健康检查**: http://localhost:8080/health

## 📋 使用指南

### 任务管理

系统支持三种任务类型：

1. **采集任务 (collect)**：数据采集
2. **分析任务 (analyze)**：数据分析
3. **告警任务 (alert)**：告警通知

### 创建监控任务

#### 1. 系统监控任务

```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "系统指标采集",
    "type": "collect",
    "config": {
      "collector_type": "system",
      "metrics": ["cpu", "memory", "disk", "network"]
    },
    "schedule": {
      "type": "interval",
      "interval": "30s"
    },
    "enabled": true
  }'
```

#### 2. 异常检测任务

```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "CPU异常检测",
    "type": "analyze",
    "config": {
      "analyzer_type": "anomaly_detection",
      "metric": "cpu_usage_percent",
      "algorithm": "statistical",
      "threshold": 2.0,
      "window": "5m"
    },
    "schedule": {
      "type": "interval",
      "interval": "1m"
    },
    "enabled": true
  }'
```

### API 接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/v1/tasks` | 获取任务列表 |
| POST | `/api/v1/tasks` | 创建任务 |
| GET | `/api/v1/tasks/{id}` | 获取任务详情 |
| PUT | `/api/v1/tasks/{id}` | 更新任务 |
| DELETE | `/api/v1/tasks/{id}` | 删除任务 |
| POST | `/api/v1/tasks/{id}/start` | 立即执行任务 |
| GET | `/api/v1/metrics` | 获取指标数据 |
| GET | `/api/v1/alerts` | 获取告警事件 |

## 🔧 配置说明

### 主配置文件 (config/aiops.yaml)

```yaml
# HTTP 服务器配置
server:
  port: 8080

# 数据库配置
database:
  driver: sqlite
  dsn: ./data/aiops.db

# 任务配置
tasks:
  default_interval: 30s
  max_concurrent: 10

# 邮件配置
email:
  smtp_host: smtp.example.com
  smtp_port: 587
  smtp_username: <EMAIL>
  smtp_password: your_password
  from_email: <EMAIL>
```

## 🏗️ 开发指南

### 项目结构

```
aiops/
├── cmd/main.go                    # 应用入口
├── internal/                      # 内部模块
│   ├── api/                      # HTTP API 服务
│   ├── alerter/                  # 告警器模块
│   ├── analyzer/                 # 分析器模块
│   ├── collector/                # 采集器模块
│   ├── config/                   # 配置管理
│   ├── database/                 # 数据库初始化
│   ├── scheduler/                # 任务调度器
│   ├── storage/                  # 存储层
│   └── task/                     # 任务类型定义
├── config/aiops.yaml             # 主配置文件
├── examples/tasks/               # 示例任务配置
├── web/index.html                # Web UI
└── data/                         # 数据目录
```

### 环境要求

- Go 1.24.3+
- SQLite3 (内置)
- Make (可选)

### 扩展开发

#### 添加新的采集器

1. 在 `internal/collector/` 目录下创建新文件
2. 实现 `Collector` 接口
3. 在 `manager.go` 中注册

#### 添加新的分析器

1. 在 `internal/analyzer/` 目录下创建新文件
2. 实现 `Analyzer` 接口
3. 在 `manager.go` 中注册

#### 添加新的告警器

1. 在 `internal/alerter/` 目录下创建新文件
2. 实现 `Alerter` 接口
3. 在 `manager.go` 中注册

## 🆚 架构对比

| 特性 | 旧架构 | 新架构 |
|------|--------|--------|
| 部署复杂度 | 高 | 低 |
| 配置管理 | 复杂 | 简单 |
| 网络依赖 | 需要 gRPC | 无 |
| 故障点 | 多个 | 少 |
| 学习成本 | 高 | 低 |
| 扩展性 | 复杂 | 简单 |

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

---

**AIOps 监控系统 v3.0.0 - 简化架构版本**

🚀 现代化 | 🎯 简单易用 | 🔧 高度可扩展
