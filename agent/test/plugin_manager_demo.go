package main

import (
	"fmt"

	"aiops/agent/internal/pipeline"
	cf "aiops/pkg/config"
	"aiops/pkg/log"
	pipelinepkg "aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

func main() {
	// 初始化日志 - 使用默认配置
	logConfig := cf.NewConfig("config/agent_config.yaml")
	logger := log.NewLog(logConfig)

	fmt.Println("=== 插件管理器测试 ===")

	// 创建插件管理器
	pluginManager := pipeline.NewPluginManager("plugins/build", logger.Logger)

	// 初始化插件管理器
	fmt.Println("\n1. 初始化插件管理器...")
	if err := pluginManager.Initialize(); err != nil {
		logger.Error("初始化插件管理器失败", zap.Error(err))
		fmt.Printf("错误: %v\n", err)
		// 不退出，继续测试其他功能
	} else {
		fmt.Println("✓ 插件管理器初始化成功")
	}

	// 获取已加载的插件列表
	fmt.Println("\n2. 获取已加载的插件列表...")
	loadedPlugins := pluginManager.GetLoadedPlugins()
	fmt.Printf("已加载插件数量: %d\n", len(loadedPlugins))

	for _, plugin := range loadedPlugins {
		fmt.Printf("  - %s:%s (%s) - %s\n",
			plugin.Name,
			plugin.Version,
			plugin.Type,
			plugin.Path)
	}

	// 获取可用插件列表
	fmt.Println("\n3. 获取可用插件列表...")
	availablePlugins := pluginManager.GetAvailablePlugins()
	fmt.Printf("可用插件数量: %d\n", len(availablePlugins))

	// 测试插件可用性检查
	fmt.Println("\n4. 测试插件可用性检查...")
	testPlugins := []struct {
		name       string
		pluginType string
	}{
		{"system_collector", "collector"},
		{"mysql_collector", "collector"},
		{"threshold_processor", "processor"},
		{"email_alerter", "alerter"},
		{"nonexistent_plugin", "collector"},
	}

	for _, test := range testPlugins {
		available := pluginManager.IsPluginAvailable(test.name, pipelinepkg.PluginType(test.pluginType))
		status := "❌"
		if available {
			status = "✓"
		}
		fmt.Printf("  %s %s (%s): %v\n", status, test.name, test.pluginType, available)
	}

	// 获取插件统计信息
	fmt.Println("\n5. 获取插件统计信息...")
	stats := pluginManager.GetPluginStats()
	fmt.Printf("插件统计信息:\n")
	for key, value := range stats {
		if key == "plugins" {
			continue // 跳过详细插件列表，已经在上面显示了
		}
		fmt.Printf("  %s: %v\n", key, value)
	}

	// 测试插件验证
	fmt.Println("\n6. 测试插件验证...")
	pluginPaths := []string{
		"plugins/build/system_collector.so",
		"plugins/build/mysql_collector-1.0.0.so",
		"plugins/build/threshold_processor.so",
		"plugins/build/nonexistent.so",
	}

	for _, path := range pluginPaths {
		err := pluginManager.ValidatePlugin(path)
		status := "✓"
		if err != nil {
			status = "❌"
		}
		fmt.Printf("  %s %s: %v\n", status, path, err)
	}

	// 测试创建插件实例
	fmt.Println("\n7. 测试创建插件实例...")

	// 测试创建采集器插件
	if pluginManager.IsPluginAvailable("system_collector", pipelinepkg.CollectorType) {
		collectorConfig := &pb.CollectorPluginConfig{
			Name:            "system_collector",
			Type:            "collector",
			Enabled:         true,
			IntervalSeconds: 30,
			Config: map[string]string{
				"enable_cpu":    "true",
				"enable_memory": "true",
				"enable_disk":   "true",
			},
		}

		collector, err := pluginManager.CreateCollectorPlugin(collectorConfig)
		if err != nil {
			fmt.Printf("  ❌ 创建采集器插件失败: %v\n", err)
		} else {
			fmt.Printf("  ✓ 创建采集器插件成功: %s\n", collector.GetName())
		}
	} else {
		fmt.Println("  ⚠️ system_collector 插件不可用，跳过测试")
	}

	// 测试创建处理器插件
	if pluginManager.IsPluginAvailable("threshold_processor", pipelinepkg.ProcessorType) {
		processorConfig := &pb.ProcessorPluginConfig{
			Name:           "threshold_processor",
			Type:           "processor",
			Enabled:        true,
			Concurrency:    2,
			TimeoutSeconds: 30,
			Order:          1,
			Config: map[string]string{
				"cpu_threshold":    "80.0",
				"memory_threshold": "90.0",
			},
		}

		processor, err := pluginManager.CreateProcessorPlugin(processorConfig)
		if err != nil {
			fmt.Printf("  ❌ 创建处理器插件失败: %v\n", err)
		} else {
			fmt.Printf("  ✓ 创建处理器插件成功: %s\n", processor.GetName())
		}
	} else {
		fmt.Println("  ⚠️ threshold_processor 插件不可用，跳过测试")
	}

	fmt.Println("\n=== 测试完成 ===")
}
