package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
)

// ControlPlaneClientImpl Control Plane客户端实现
type ControlPlaneClientImpl struct {
	// 连接配置
	serverAddr string
	agentID    string
	logger     *zap.Logger

	// gRPC连接
	conn   *grpc.ClientConn
	client pb.AgentServiceClient

	// 流管理
	pipelineStream pb.AgentService_StreamPipelinesClient
	streamMutex    sync.RWMutex

	// 状态管理
	connected bool
	ctx       context.Context
	cancel    context.CancelFunc
	mutex     sync.RWMutex

	// 心跳管理
	heartbeatInterval time.Duration
	heartbeatTicker   *time.Ticker
	heartbeatStop     chan struct{}

	// 重连管理
	reconnectInterval time.Duration
	maxReconnectDelay time.Duration
	reconnectAttempts int
}

// NewControlPlaneClient 创建新的Control Plane客户端
func NewControlPlaneClient(serverAddr, agentID string, logger *zap.Logger) *ControlPlaneClientImpl {
	ctx, cancel := context.WithCancel(context.Background())
	return &ControlPlaneClientImpl{
		serverAddr:        serverAddr,
		agentID:           agentID,
		logger:            logger,
		ctx:               ctx,
		cancel:            cancel,
		heartbeatInterval: 60 * time.Second, // 增加心跳间隔到60秒，减少频率
		heartbeatStop:     make(chan struct{}),
		reconnectInterval: 5 * time.Second,
		maxReconnectDelay: 5 * time.Minute,
		reconnectAttempts: 0,
	}
}

// Connect 连接到Control Plane
func (c *ControlPlaneClientImpl) Connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.connected {
		return fmt.Errorf("already connected")
	}

	c.logger.Info("Connecting to Control Plane", zap.String("server", c.serverAddr))

	// 配置 keepalive 参数，与服务器端配置匹配
	kacp := keepalive.ClientParameters{
		Time:                30 * time.Second, // 每30秒发送keepalive ping，匹配服务器的30秒Time设置
		Timeout:             5 * time.Second,  // 等待keepalive ping ack的超时时间，匹配服务器的5秒Timeout
		PermitWithoutStream: true,             // 允许在没有活动流时发送keepalive ping
	}

	// 建立gRPC连接
	conn, err := grpc.NewClient(c.serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(kacp),
	)
	if err != nil {
		return fmt.Errorf("failed to create grpc client: %w", err)
	}

	c.conn = conn
	c.client = pb.NewAgentServiceClient(conn)

	// 注册Agent
	if err := c.registerAgent(); err != nil {
		c.conn.Close()
		return fmt.Errorf("failed to register agent: %w", err)
	}

	c.connected = true
	c.logger.Info("注册Agent成功", zap.String("agent_id", c.agentID))

	return nil
}

// Disconnect 断开连接
func (c *ControlPlaneClientImpl) Disconnect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return fmt.Errorf("not connected")
	}

	c.logger.Info("Disconnecting from Control Plane")

	// 关闭流
	c.streamMutex.Lock()
	if c.pipelineStream != nil {
		c.pipelineStream.CloseSend()
		c.pipelineStream = nil
	}
	c.streamMutex.Unlock()

	// 关闭连接
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.client = nil
	c.connected = false
	c.cancel()

	c.logger.Info("Disconnected from Control Plane")

	return nil
}

// registerAgent 注册Agent
func (c *ControlPlaneClientImpl) registerAgent() error {
	req := &pb.RegisterAgentRequest{
		AgentId:                 c.agentID,
		AgentIp:                 "127.0.0.1", // 简化实现
		SupportedCollectorTypes: []string{"system", "mysql", "redis", "http"},
	}

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.RegisterAgent(ctx, req)
	if err != nil {
		return fmt.Errorf("register agent failed: %w", err)
	}

	if !resp.Success {
		return fmt.Errorf("register agent failed: %s", resp.Message)
	}

	c.logger.Info("Agent registered successfully", zap.String("message", resp.Message))

	return nil
}

// StreamPipelines 创建流水线流
func (c *ControlPlaneClientImpl) StreamPipelines(ctx context.Context) (pb.AgentService_StreamPipelinesClient, error) {
	c.streamMutex.Lock()
	defer c.streamMutex.Unlock()

	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	// 如果已有流，先关闭
	if c.pipelineStream != nil {
		err := c.pipelineStream.CloseSend()
		if err != nil {
			return nil, fmt.Errorf("failed to close existing pipeline stream: %w", err)
		}
		c.pipelineStream = nil
		c.logger.Info("Closed existing pipeline stream")
	}

	// 创建新流
	stream, err := c.client.StreamPipelines(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create pipeline stream: %w", err)
	}

	c.pipelineStream = stream
	c.logger.Info("流水线流创建成功")

	return stream, nil
}

// SendPipelineStatus 发送流水线状态
func (c *ControlPlaneClientImpl) SendPipelineStatus(status *pb.PipelineStatus) error {
	c.streamMutex.RLock()
	defer c.streamMutex.RUnlock()

	if c.pipelineStream == nil {
		return fmt.Errorf("pipeline stream not available")
	}

	if err := c.pipelineStream.Send(status); err != nil {
		return fmt.Errorf("failed to send pipeline status: %w", err)
	}

	return nil
}

// GetPipelineTemplates 获取流水线模板
func (c *ControlPlaneClientImpl) GetPipelineTemplates(req *pb.PipelineTemplateRequest) (*pb.PipelineTemplateResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	resp, err := c.client.GetPipelineTemplates(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get pipeline templates: %w", err)
	}

	return resp, nil
}

// ValidatePipelineConfig 验证流水线配置
func (c *ControlPlaneClientImpl) ValidatePipelineConfig(req *pb.PipelineConfigValidationRequest) (*pb.PipelineConfigValidationResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	resp, err := c.client.ValidatePipelineConfig(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate pipeline config: %w", err)
	}

	return resp, nil
}

// RequestPlugin 请求插件
func (c *ControlPlaneClientImpl) RequestPlugin(req *pb.PluginRequest) (*pb.PluginResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 60*time.Second) // 插件下载可能需要更长时间
	defer cancel()

	resp, err := c.client.RequestPlugin(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to request plugin: %w", err)
	}

	return resp, nil
}

// ReportPluginStatus 报告插件状态
func (c *ControlPlaneClientImpl) ReportPluginStatus(req *pb.PluginStatusReport) (*pb.PluginStatusResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.ReportPluginStatus(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to report plugin status: %w", err)
	}

	return resp, nil
}

// IsConnected 检查是否已连接
func (c *ControlPlaneClientImpl) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected
}

// GetServerAddr 获取服务器地址
func (c *ControlPlaneClientImpl) GetServerAddr() string {
	return c.serverAddr
}

// GetAgentID 获取Agent ID
func (c *ControlPlaneClientImpl) GetAgentID() string {
	return c.agentID
}

// Reconnect 重新连接
func (c *ControlPlaneClientImpl) Reconnect() error {
	c.logger.Info("Attempting to reconnect to Control Plane")

	// 先断开现有连接
	if c.IsConnected() {
		if err := c.Disconnect(); err != nil {
			c.logger.Error("Failed to disconnect before reconnect", zap.Error(err))
		}
	}

	// 重新连接
	return c.Connect()
}

// StartHeartbeat 启动心跳
func (c *ControlPlaneClientImpl) StartHeartbeat() {
	if c.heartbeatTicker != nil {
		c.heartbeatTicker.Stop()
	}

	c.heartbeatTicker = time.NewTicker(c.heartbeatInterval)
	go c.heartbeatLoop()
}

// StopHeartbeat 停止心跳
func (c *ControlPlaneClientImpl) StopHeartbeat() {
	if c.heartbeatTicker != nil {
		c.heartbeatTicker.Stop()
		c.heartbeatTicker = nil
	}

	select {
	case c.heartbeatStop <- struct{}{}:
	default:
	}
}

// heartbeatLoop 心跳循环
func (c *ControlPlaneClientImpl) heartbeatLoop() {
	defer func() {
		if c.heartbeatTicker != nil {
			c.heartbeatTicker.Stop()
		}
	}()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-c.heartbeatStop:
			return
		case <-c.heartbeatTicker.C:
			if c.IsConnected() {
				// 使用专门的心跳方法
				req := &pb.HeartbeatRequest{
					AgentId:   c.agentID,
					Timestamp: time.Now().Unix(),
				}

				ctx, cancel := context.WithTimeout(c.ctx, 5*time.Second) // 减少超时时间
				resp, err := c.client.Heartbeat(ctx, req)
				cancel()

				if err != nil {
					c.logger.Error("Heartbeat failed", zap.Error(err))
					c.reconnectAttempts++
					// 启动重连
					go c.startReconnect()
				} else if !resp.Success {
					c.logger.Warn("Heartbeat response not successful", zap.String("message", resp.Message))
				} else {
					// 心跳成功，使用Debug级别日志减少噪音
					c.logger.Debug("Heartbeat successful", zap.String("agent_id", c.agentID))
					c.reconnectAttempts = 0 // 重置重连计数
				}
			} else {
				// 如果未连接，尝试重连
				c.logger.Debug("Not connected, attempting reconnect")
				go c.startReconnect()
			}
		}
	}
}

// startReconnect 启动重连
func (c *ControlPlaneClientImpl) startReconnect() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.reconnectAttempts > 0 {
		// 已经在重连中，避免重复
		return
	}

	go c.reconnectLoop()
}

// reconnectLoop 重连循环
func (c *ControlPlaneClientImpl) reconnectLoop() {
	for {
		c.reconnectAttempts++

		// 计算退避延迟
		delay := time.Duration(c.reconnectAttempts) * c.reconnectInterval
		if delay > c.maxReconnectDelay {
			delay = c.maxReconnectDelay
		}

		c.logger.Info("Attempting to reconnect",
			zap.Int("attempt", c.reconnectAttempts),
			zap.Duration("delay", delay))

		time.Sleep(delay)

		// 尝试重连
		if err := c.Reconnect(); err != nil {
			c.logger.Error("Reconnect failed",
				zap.Int("attempt", c.reconnectAttempts),
				zap.Error(err))

			// 检查是否应该继续重连
			if c.reconnectAttempts >= 10 {
				c.logger.Error("Max reconnect attempts reached, giving up")
				return
			}
			continue
		}

		c.logger.Info("Reconnected successfully")
		c.reconnectAttempts = 0
		return
	}
}

// GetConnectionStats 获取连接统计信息
func (c *ControlPlaneClientImpl) GetConnectionStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["connected"] = c.connected
	stats["server_addr"] = c.serverAddr
	stats["agent_id"] = c.agentID

	if c.conn != nil {
		stats["connection_state"] = c.conn.GetState().String()
	}

	return stats
}
