package pipeline

import (
	"aiops/pkg/pipeline"
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PipelineOrchestrator 流水线编排器
type PipelineOrchestrator struct {
	logger        *zap.Logger
	pipelines     map[string]*PipelineInstance
	pluginManager *PluginManager
	scheduler     *PipelineScheduler
	monitor       *PipelineMonitor
	mutex         sync.RWMutex

	// 配置
	maxConcurrentPipelines int
	defaultTimeout         time.Duration

	// 状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewPipelineOrchestrator 创建新的流水线编排器
func NewPipelineOrchestrator(pluginManager *PluginManager, logger *zap.Logger) *PipelineOrchestrator {
	ctx, cancel := context.WithCancel(context.Background())

	return &PipelineOrchestrator{
		logger:                 logger,
		pipelines:              make(map[string]*PipelineInstance),
		pluginManager:          pluginManager,
		maxConcurrentPipelines: 10,
		defaultTimeout:         30 * time.Minute,
		ctx:                    ctx,
		cancel:                 cancel,
	}
}

// Start 启动编排器
func (o *PipelineOrchestrator) Start() error {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	if o.running {
		return fmt.Errorf("orchestrator already running")
	}

	// 创建调度器
	o.scheduler = NewPipelineScheduler(o.logger)
	if err := o.scheduler.Start(); err != nil {
		return fmt.Errorf("failed to start scheduler: %w", err)
	}

	// 创建监控器
	o.monitor = NewPipelineMonitor(o.logger)
	if err := o.monitor.Start(); err != nil {
		o.scheduler.Stop()
		return fmt.Errorf("failed to start monitor: %w", err)
	}

	o.running = true
	o.logger.Info("Pipeline orchestrator started")

	return nil
}

// Stop 停止编排器
func (o *PipelineOrchestrator) Stop() error {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	if !o.running {
		return nil
	}

	o.logger.Info("Stopping pipeline orchestrator")

	// 停止所有流水线
	for id, pipeline := range o.pipelines {
		o.logger.Info("Stopping pipeline", zap.String("id", id))
		if err := pipeline.Stop(); err != nil {
			o.logger.Error("Failed to stop pipeline", zap.String("id", id), zap.Error(err))
		}
	}

	// 停止调度器和监控器
	if o.scheduler != nil {
		o.scheduler.Stop()
	}
	if o.monitor != nil {
		o.monitor.Stop()
	}

	o.cancel()
	o.running = false
	o.logger.Info("Pipeline orchestrator stopped")

	return nil
}

// CreatePipeline 创建流水线
func (o *PipelineOrchestrator) CreatePipeline(config *PipelineConfig) (*PipelineInstance, error) {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	if !o.running {
		return nil, fmt.Errorf("orchestrator not running")
	}

	// 检查是否已存在
	if _, exists := o.pipelines[config.ID]; exists {
		return nil, fmt.Errorf("pipeline %s already exists", config.ID)
	}

	// 检查并发限制
	if len(o.pipelines) >= o.maxConcurrentPipelines {
		return nil, fmt.Errorf("maximum concurrent pipelines reached (%d)", o.maxConcurrentPipelines)
	}

	// 验证配置
	if err := o.validatePipelineConfig(config); err != nil {
		return nil, fmt.Errorf("invalid pipeline config: %w", err)
	}

	// 创建流水线实例
	instance := NewPipelineInstance(config, o.pluginManager, o.logger)

	// 注册到调度器
	if err := o.scheduler.RegisterPipeline(instance); err != nil {
		return nil, fmt.Errorf("failed to register pipeline to scheduler: %w", err)
	}

	// 注册到监控器
	if err := o.monitor.RegisterPipeline(instance); err != nil {
		o.scheduler.UnregisterPipeline(config.ID)
		return nil, fmt.Errorf("failed to register pipeline to monitor: %w", err)
	}

	o.pipelines[config.ID] = instance
	o.logger.Info("Pipeline created", zap.String("id", config.ID), zap.String("name", config.Name))

	return instance, nil
}

// DeletePipeline 删除流水线
func (o *PipelineOrchestrator) DeletePipeline(id string) error {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	pipeline, exists := o.pipelines[id]
	if !exists {
		return fmt.Errorf("pipeline %s not found", id)
	}

	// 停止流水线
	if err := pipeline.Stop(); err != nil {
		o.logger.Error("Failed to stop pipeline during deletion", zap.String("id", id), zap.Error(err))
	}

	// 从调度器和监控器中移除
	o.scheduler.UnregisterPipeline(id)
	o.monitor.UnregisterPipeline(id)

	delete(o.pipelines, id)
	o.logger.Info("Pipeline deleted", zap.String("id", id))

	return nil
}

// GetPipeline 获取流水线
func (o *PipelineOrchestrator) GetPipeline(id string) (*PipelineInstance, error) {
	o.mutex.RLock()
	defer o.mutex.RUnlock()

	pipeline, exists := o.pipelines[id]
	if !exists {
		return nil, fmt.Errorf("pipeline %s not found", id)
	}

	return pipeline, nil
}

// ListPipelines 列出所有流水线
func (o *PipelineOrchestrator) ListPipelines() []*PipelineInstance {
	o.mutex.RLock()
	defer o.mutex.RUnlock()

	pipelines := make([]*PipelineInstance, 0, len(o.pipelines))
	for _, pipeline := range o.pipelines {
		pipelines = append(pipelines, pipeline)
	}

	return pipelines
}

// UpdatePipeline 更新流水线配置
func (o *PipelineOrchestrator) UpdatePipeline(id string, config *PipelineConfig) error {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	pipeline, exists := o.pipelines[id]
	if !exists {
		return fmt.Errorf("pipeline %s not found", id)
	}

	// 验证新配置
	if err := o.validatePipelineConfig(config); err != nil {
		return fmt.Errorf("invalid pipeline config: %w", err)
	}

	// 更新配置
	if err := pipeline.UpdateConfig(config); err != nil {
		return fmt.Errorf("failed to update pipeline config: %w", err)
	}

	o.logger.Info("Pipeline updated", zap.String("id", id))

	return nil
}

// StartPipeline 启动流水线
func (o *PipelineOrchestrator) StartPipeline(id string) error {
	pipeline, err := o.GetPipeline(id)
	if err != nil {
		return err
	}

	return pipeline.Start(o.ctx)
}

// StopPipeline 停止流水线
func (o *PipelineOrchestrator) StopPipeline(id string) error {
	pipeline, err := o.GetPipeline(id)
	if err != nil {
		return err
	}

	return pipeline.Stop()
}

// GetOrchestratorStatus 获取编排器状态
func (o *PipelineOrchestrator) GetOrchestratorStatus() *OrchestratorStatus {
	o.mutex.RLock()
	defer o.mutex.RUnlock()

	status := &OrchestratorStatus{
		Running:          o.running,
		TotalPipelines:   len(o.pipelines),
		RunningPipelines: 0,
		StoppedPipelines: 0,
		ErrorPipelines:   0,
		MaxConcurrent:    o.maxConcurrentPipelines,
		Uptime:           time.Since(time.Now()), // 简化实现
	}

	// 统计各状态的流水线数量
	for _, pipeline := range o.pipelines {
		pipelineStatus := pipeline.GetStatus()
		switch pipelineStatus.State {
		case "running":
			status.RunningPipelines++
		case "stopped":
			status.StoppedPipelines++
		case "error":
			status.ErrorPipelines++
		}
	}

	return status
}

// validatePipelineConfig 验证流水线配置
func (o *PipelineOrchestrator) validatePipelineConfig(config *PipelineConfig) error {
	if config.ID == "" {
		return fmt.Errorf("pipeline ID is required")
	}

	if config.Name == "" {
		return fmt.Errorf("pipeline name is required")
	}

	if config.Collector == nil {
		return fmt.Errorf("collector configuration is required")
	}

	// 验证采集器插件是否存在
	if !o.pluginManager.IsPluginAvailable(config.Collector.Name, pipeline.CollectorType) {
		return fmt.Errorf("collector plugin %s not available", config.Collector.Name)
	}

	// 验证处理器插件
	for _, processor := range config.Processors {
		if !o.pluginManager.IsPluginAvailable(processor.Name, pipeline.ProcessorType) {
			return fmt.Errorf("processor plugin %s not available", processor.Name)
		}
	}

	return nil
}

// OrchestratorStatus 编排器状态
type OrchestratorStatus struct {
	Running          bool          `json:"running"`
	TotalPipelines   int           `json:"total_pipelines"`
	RunningPipelines int           `json:"running_pipelines"`
	StoppedPipelines int           `json:"stopped_pipelines"`
	ErrorPipelines   int           `json:"error_pipelines"`
	MaxConcurrent    int           `json:"max_concurrent"`
	Uptime           time.Duration `json:"uptime"`
}
