package pipeline

import (
	"fmt"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	"maps"

	"go.uber.org/zap"
)

// DataTransformer 数据转换器，负责将不同格式的数据转换为统一的 PipelineData 格式
type DataTransformer struct {
	logger *zap.Logger
}

// NewDataTransformer 创建新的数据转换器
func NewDataTransformer(logger *zap.Logger) *DataTransformer {
	return &DataTransformer{
		logger: logger,
	}
}

// TransformMetricData 转换指标数据为 PipelineData
func (dt *DataTransformer) TransformMetricData(metrics []*pb.MetricData, source string) *pipeline.PipelineData {
	if len(metrics) == 0 {
		return nil
	}

	// 使用第一个指标的设备ID和时间戳作为基础信息
	firstMetric := metrics[0]

	data := &pipeline.PipelineData{
		ID:        dt.generateDataID("metric", firstMetric.DeviceId, time.Now()),
		Type:      pipeline.MetricDataType,
		Source:    source,
		DeviceID:  firstMetric.DeviceId,
		Timestamp: time.Unix(firstMetric.Timestamp, 0),
		Metrics:   metrics,
		Metadata:  make(map[string]interface{}),
		Context:   make(map[string]interface{}),
		Tags:      make(map[string]string),
	}

	// 添加元数据
	data.Metadata["metric_count"] = len(metrics)
	data.Metadata["transformed_at"] = time.Now()
	data.Tags["data_type"] = "metric"
	data.Tags["source"] = source

	return data
}

// TransformLogData 转换日志数据为 PipelineData
func (dt *DataTransformer) TransformLogData(logs []*pb.LogEntry, source string) *pipeline.PipelineData {
	if len(logs) == 0 {
		return nil
	}

	// 使用第一个日志的设备ID和时间戳作为基础信息
	firstLog := logs[0]

	data := &pipeline.PipelineData{
		ID:        dt.generateDataID("log", firstLog.DeviceId, time.Now()),
		Type:      pipeline.LogDataType,
		Source:    source,
		DeviceID:  firstLog.DeviceId,
		Timestamp: time.Unix(firstLog.Timestamp, 0),
		Logs:      logs,
		Metadata:  make(map[string]interface{}),
		Context:   make(map[string]interface{}),
		Tags:      make(map[string]string),
	}

	// 添加元数据
	data.Metadata["log_count"] = len(logs)
	data.Metadata["transformed_at"] = time.Now()
	data.Tags["data_type"] = "log"
	data.Tags["source"] = source

	return data
}

// TransformEventData 转换事件数据为 PipelineData
func (dt *DataTransformer) TransformEventData(events []*pipeline.Event, source string, deviceID string) *pipeline.PipelineData {
	if len(events) == 0 {
		return nil
	}

	// 使用第一个事件的时间戳作为基础信息
	firstEvent := events[0]

	data := &pipeline.PipelineData{
		ID:        dt.generateDataID("event", deviceID, time.Now()),
		Type:      pipeline.EventDataType,
		Source:    source,
		DeviceID:  deviceID,
		Timestamp: firstEvent.Timestamp,
		Events:    events,
		Metadata:  make(map[string]interface{}),
		Context:   make(map[string]interface{}),
		Tags:      make(map[string]string),
	}

	// 添加元数据
	data.Metadata["event_count"] = len(events)
	data.Metadata["transformed_at"] = time.Now()
	data.Tags["data_type"] = "event"
	data.Tags["source"] = source

	return data
}

// TransformAnomalyData 转换异常数据为 PipelineData
func (dt *DataTransformer) TransformAnomalyData(anomalies []*pipeline.Anomaly, source string, deviceID string) *pipeline.PipelineData {
	if len(anomalies) == 0 {
		return nil
	}

	// 使用第一个异常的时间戳作为基础信息
	firstAnomaly := anomalies[0]

	data := &pipeline.PipelineData{
		ID:        dt.generateDataID("anomaly", deviceID, time.Now()),
		Type:      pipeline.AnomalyDataType,
		Source:    source,
		DeviceID:  deviceID,
		Timestamp: firstAnomaly.Timestamp,
		Anomalies: anomalies,
		Metadata:  make(map[string]interface{}),
		Context:   make(map[string]interface{}),
		Tags:      make(map[string]string),
	}

	// 添加元数据
	data.Metadata["anomaly_count"] = len(anomalies)
	data.Metadata["transformed_at"] = time.Now()
	data.Tags["data_type"] = "anomaly"
	data.Tags["source"] = source

	return data
}

// TransformAlertData 转换告警数据为 PipelineData
func (dt *DataTransformer) TransformAlertData(alerts []*pipeline.Alert, source string, deviceID string) *pipeline.PipelineData {
	if len(alerts) == 0 {
		return nil
	}

	// 使用第一个告警的时间戳作为基础信息
	firstAlert := alerts[0]

	data := &pipeline.PipelineData{
		ID:        dt.generateDataID("alert", deviceID, time.Now()),
		Type:      pipeline.AlertDataType,
		Source:    source,
		DeviceID:  deviceID,
		Timestamp: firstAlert.Timestamp,
		Alerts:    alerts,
		Metadata:  make(map[string]any),
		Context:   make(map[string]any),
		Tags:      make(map[string]string),
	}

	// 添加元数据
	data.Metadata["alert_count"] = len(alerts)
	data.Metadata["transformed_at"] = time.Now()
	data.Tags["data_type"] = "alert"
	data.Tags["source"] = source

	return data
}

// MergePipelineData 合并多个 PipelineData
func (dt *DataTransformer) MergePipelineData(dataList []*pipeline.PipelineData) *pipeline.PipelineData {
	if len(dataList) == 0 {
		return nil
	}

	if len(dataList) == 1 {
		return dataList[0]
	}

	// 使用第一个数据作为基础
	merged := &pipeline.PipelineData{
		ID:        dt.generateDataID("merged", dataList[0].DeviceID, time.Now()),
		Type:      pipeline.MixedDataType,
		Source:    "merged",
		DeviceID:  dataList[0].DeviceID,
		Timestamp: dataList[0].Timestamp,
		Metadata:  make(map[string]interface{}),
		Context:   make(map[string]interface{}),
		Tags:      make(map[string]string),
	}

	// 合并所有数据
	for _, data := range dataList {
		merged.Metrics = append(merged.Metrics, data.Metrics...)
		merged.Logs = append(merged.Logs, data.Logs...)
		merged.Events = append(merged.Events, data.Events...)
		merged.Anomalies = append(merged.Anomalies, data.Anomalies...)
		merged.Alerts = append(merged.Alerts, data.Alerts...)

		// 合并元数据和标签
		maps.Copy(merged.Metadata, data.Metadata)
		maps.Copy(merged.Context, data.Context)
		maps.Copy(merged.Tags, data.Tags)
	}

	// 添加合并信息
	merged.Metadata["merged_count"] = len(dataList)
	merged.Metadata["merged_at"] = time.Now()
	merged.Tags["data_type"] = "mixed"

	return merged
}

// ValidatePipelineData 验证 PipelineData 的完整性
func (dt *DataTransformer) ValidatePipelineData(data *pipeline.PipelineData) error {
	if data == nil {
		return fmt.Errorf("pipeline data is nil")
	}

	if data.ID == "" {
		return fmt.Errorf("pipeline data ID is empty")
	}

	if data.DeviceID == "" {
		return fmt.Errorf("pipeline data device ID is empty")
	}

	if data.Source == "" {
		return fmt.Errorf("pipeline data source is empty")
	}

	if data.Timestamp.IsZero() {
		return fmt.Errorf("pipeline data timestamp is zero")
	}

	// 检查数据类型和内容的一致性
	switch data.Type {
	case pipeline.MetricDataType:
		if len(data.Metrics) == 0 {
			return fmt.Errorf("metric data type but no metrics present")
		}
	case pipeline.LogDataType:
		if len(data.Logs) == 0 {
			return fmt.Errorf("log data type but no logs present")
		}
	case pipeline.EventDataType:
		if len(data.Events) == 0 {
			return fmt.Errorf("event data type but no events present")
		}
	case pipeline.AnomalyDataType:
		if len(data.Anomalies) == 0 {
			return fmt.Errorf("anomaly data type but no anomalies present")
		}
	case pipeline.AlertDataType:
		if len(data.Alerts) == 0 {
			return fmt.Errorf("alert data type but no alerts present")
		}
	}

	return nil
}

// generateDataID 生成数据ID
func (dt *DataTransformer) generateDataID(dataType, deviceID string, timestamp time.Time) string {
	return fmt.Sprintf("%s_%s_%d", dataType, deviceID, timestamp.UnixNano())
}

// EnrichPipelineData 增强 PipelineData，添加额外的上下文信息
func (dt *DataTransformer) EnrichPipelineData(data *pipeline.PipelineData, enrichments map[string]interface{}) {
	if data == nil || enrichments == nil {
		return
	}

	if data.Context == nil {
		data.Context = make(map[string]interface{})
	}

	for k, v := range enrichments {
		data.Context[k] = v
	}

	// 添加增强时间戳
	data.Context["enriched_at"] = time.Now()
}
