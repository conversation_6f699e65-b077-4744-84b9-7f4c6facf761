package pipeline

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"aiops/pkg/pipeline"

	"go.uber.org/zap"
)

// PipelineInstance 流水线实例
type PipelineInstance struct {
	config        *PipelineConfig
	pluginManager *PluginManager
	logger        *zap.Logger

	// 插件实例
	collector  pipeline.PipelinePlugin
	processors []pipeline.PipelinePlugin

	// 状态管理
	state  string
	mutex  sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc

	// 数据通道
	dataChan   chan *pipeline.PipelineData
	resultChan chan *pipeline.PipelineData
	errorChan  chan error

	// 统计信息
	startTime      time.Time
	processedCount int64
	errorCount     int64
	lastError      error
	lastExecution  time.Time

	// 性能指标
	avgLatency   time.Duration
	maxLatency   time.Duration
	minLatency   time.Duration
	latencySum   time.Duration
	latencyCount int64

	// 资源使用
	memoryUsage    int64
	cpuUsage       float64
	goroutineCount int
}

// PipelineConfig 流水线配置
type PipelineConfig struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Version     string `json:"version"`
	Enabled     bool   `json:"enabled"`

	// 执行配置
	BufferSize  int           `json:"buffer_size"`
	WorkerCount int           `json:"worker_count"`
	Timeout     time.Duration `json:"timeout"`

	// 重试配置
	RetryAttempts int           `json:"retry_attempts"`
	RetryDelay    time.Duration `json:"retry_delay"`

	// 监控配置
	EnableMetrics   bool          `json:"enable_metrics"`
	EnableTracing   bool          `json:"enable_tracing"`
	MetricsInterval time.Duration `json:"metrics_interval"`

	// 插件配置
	Collector  *CollectorConfig   `json:"collector"`
	Processors []*ProcessorConfig `json:"processors"`

	// 错误处理配置
	ErrorHandling *ErrorHandlingConfig `json:"error_handling"`

	// 资源限制配置
	ResourceLimits *ResourceLimitsConfig `json:"resource_limits"`

	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CollectorConfig 采集器配置
type CollectorConfig struct {
	Name     string                 `json:"name"`
	Type     string                 `json:"type"`
	Interval time.Duration          `json:"interval"`
	Enabled  bool                   `json:"enabled"`
	Config   map[string]interface{} `json:"config"`
}

// ProcessorConfig 处理器配置
type ProcessorConfig struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Enabled     bool                   `json:"enabled"`
	Config      map[string]interface{} `json:"config"`
	Concurrency int                    `json:"concurrency"`
	Timeout     time.Duration          `json:"timeout"`
	Order       int                    `json:"order"`
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
	Strategy   string        `json:"strategy"`
	MaxRetries int           `json:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay"`
}

// ResourceLimitsConfig 资源限制配置
type ResourceLimitsConfig struct {
	MaxMemoryMB    int           `json:"max_memory_mb"`
	MaxCPUPercent  float64       `json:"max_cpu_percent"`
	MaxGoroutines  int           `json:"max_goroutines"`
	ProcessTimeout time.Duration `json:"process_timeout"`
	CollectTimeout time.Duration `json:"collect_timeout"`
}

// PipelineStatus 流水线状态
type PipelineStatus struct {
	ID             string           `json:"id"`
	State          string           `json:"state"`
	StartTime      time.Time        `json:"start_time"`
	LastExecution  time.Time        `json:"last_execution"`
	ProcessedCount int64            `json:"processed_count"`
	ErrorCount     int64            `json:"error_count"`
	LastError      string           `json:"last_error"`
	Metrics        *PipelineMetrics `json:"metrics"`
	Plugins        []PluginStatus   `json:"plugins"`
}

// PluginStatus 插件状态
type PluginStatus struct {
	Name    string                  `json:"name"`
	Type    string                  `json:"type"`
	Status  string                  `json:"status"`
	Metrics *pipeline.PluginMetrics `json:"metrics"`
}

// PipelineMetrics 流水线指标
type PipelineMetrics struct {
	ProcessedCount int64         `json:"processed_count"`
	ErrorCount     int64         `json:"error_count"`
	AvgLatency     time.Duration `json:"avg_latency"`
	MaxLatency     time.Duration `json:"max_latency"`
	MinLatency     time.Duration `json:"min_latency"`
	Throughput     float64       `json:"throughput"`
	MemoryUsage    int64         `json:"memory_usage"`
	CPUUsage       float64       `json:"cpu_usage"`
	GoroutineCount int           `json:"goroutine_count"`
}

// NewPipelineInstance 创建新的流水线实例
func NewPipelineInstance(config *PipelineConfig, pluginManager *PluginManager, logger *zap.Logger) *PipelineInstance {
	ctx, cancel := context.WithCancel(context.Background())

	return &PipelineInstance{
		config:        config,
		pluginManager: pluginManager,
		logger:        logger,
		state:         "idle",
		ctx:           ctx,
		cancel:        cancel,
		dataChan:      make(chan *pipeline.PipelineData, config.BufferSize),
		resultChan:    make(chan *pipeline.PipelineData, config.BufferSize),
		errorChan:     make(chan error, 100),
		minLatency:    time.Hour, // 初始化为一个大值
	}
}

// Start 启动流水线
func (p *PipelineInstance) Start(ctx context.Context) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.state == "running" {
		return fmt.Errorf("pipeline already running")
	}

	p.logger.Info("Starting pipeline", zap.String("id", p.config.ID))

	// 加载插件
	if err := p.loadPlugins(); err != nil {
		return fmt.Errorf("failed to load plugins: %w", err)
	}

	// 启动插件
	if err := p.startPlugins(); err != nil {
		return fmt.Errorf("failed to start plugins: %w", err)
	}

	// 启动数据处理循环
	go p.dataProcessingLoop()

	// 启动采集循环
	go p.collectionLoop()

	p.state = "running"
	p.startTime = time.Now()

	p.logger.Info("Pipeline started successfully", zap.String("id", p.config.ID))

	return nil
}

// Stop 停止流水线
func (p *PipelineInstance) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.state == "stopped" {
		return nil
	}

	p.logger.Info("Stopping pipeline", zap.String("id", p.config.ID))

	// 停止插件
	p.stopPlugins()

	// 取消上下文
	p.cancel()

	// 关闭通道
	close(p.dataChan)
	close(p.resultChan)
	close(p.errorChan)

	p.state = "stopped"

	p.logger.Info("Pipeline stopped", zap.String("id", p.config.ID))

	return nil
}

// Execute 执行一次流水线（用于调度器）
func (p *PipelineInstance) Execute(ctx context.Context) error {
	if p.state != "running" {
		return fmt.Errorf("pipeline not running")
	}

	startTime := time.Now()
	defer func() {
		p.updateLatencyMetrics(time.Since(startTime))
		p.lastExecution = time.Now()
	}()

	// 如果有采集器，执行采集
	if p.collector != nil {
		if collector, ok := p.collector.(interface {
			Collect(context.Context) (*pipeline.PipelineData, error)
		}); ok {
			data, err := collector.Collect(ctx)
			if err != nil {
				p.errorCount++
				p.lastError = err
				return err
			}

			if data != nil {
				// 通过处理器链处理数据
				processedData, err := p.processData(ctx, data)
				if err != nil {
					p.errorCount++
					p.lastError = err
					return err
				}

				p.processedCount++

				// 发送到结果通道
				select {
				case p.resultChan <- processedData:
				case <-ctx.Done():
					return ctx.Err()
				}
			}
		}
	}

	return nil
}

// HealthCheck 健康检查
func (p *PipelineInstance) HealthCheck(ctx context.Context) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if p.state != "running" {
		return fmt.Errorf("pipeline not running")
	}

	// 检查采集器健康状态
	if p.collector != nil {
		if err := p.collector.Health(); err != nil {
			return fmt.Errorf("collector health check failed: %w", err)
		}
	}

	// 检查处理器健康状态
	for _, processor := range p.processors {
		if err := processor.Health(); err != nil {
			return fmt.Errorf("processor %s health check failed: %w", processor.GetName(), err)
		}
	}

	return nil
}

// GetStatus 获取流水线状态
func (p *PipelineInstance) GetStatus() *PipelineStatus {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	status := &PipelineStatus{
		ID:             p.config.ID,
		State:          p.state,
		StartTime:      p.startTime,
		LastExecution:  p.lastExecution,
		ProcessedCount: p.processedCount,
		ErrorCount:     p.errorCount,
		Metrics:        p.GetMetrics(),
		Plugins:        make([]PluginStatus, 0),
	}

	if p.lastError != nil {
		status.LastError = p.lastError.Error()
	}

	// 添加插件状态
	if p.collector != nil {
		status.Plugins = append(status.Plugins, PluginStatus{
			Name:    p.collector.GetName(),
			Type:    string(p.collector.GetType()),
			Status:  "running",
			Metrics: p.collector.GetMetrics(),
		})
	}

	for _, processor := range p.processors {
		status.Plugins = append(status.Plugins, PluginStatus{
			Name:    processor.GetName(),
			Type:    string(processor.GetType()),
			Status:  "running",
			Metrics: processor.GetMetrics(),
		})
	}

	return status
}

// GetMetrics 获取流水线指标
func (p *PipelineInstance) GetMetrics() *PipelineMetrics {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// 计算吞吐量
	var throughput float64
	if !p.startTime.IsZero() {
		duration := time.Since(p.startTime).Seconds()
		if duration > 0 {
			throughput = float64(p.processedCount) / duration
		}
	}

	// 更新资源使用情况
	p.updateResourceMetrics()

	return &PipelineMetrics{
		ProcessedCount: p.processedCount,
		ErrorCount:     p.errorCount,
		AvgLatency:     p.avgLatency,
		MaxLatency:     p.maxLatency,
		MinLatency:     p.minLatency,
		Throughput:     throughput,
		MemoryUsage:    p.memoryUsage,
		CPUUsage:       p.cpuUsage,
		GoroutineCount: p.goroutineCount,
	}
}

// GetConfig 获取配置
func (p *PipelineInstance) GetConfig() *PipelineConfig {
	return p.config
}

// GetPlugins 获取插件列表
func (p *PipelineInstance) GetPlugins() []pipeline.PipelinePlugin {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	plugins := make([]pipeline.PipelinePlugin, 0)

	if p.collector != nil {
		plugins = append(plugins, p.collector)
	}

	plugins = append(plugins, p.processors...)

	return plugins
}

// UpdateConfig 更新配置
func (p *PipelineInstance) UpdateConfig(config *PipelineConfig) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 简化实现：重新加载插件
	p.config = config

	if p.state == "running" {
		// 重新加载插件
		p.stopPlugins()
		if err := p.loadPlugins(); err != nil {
			return err
		}
		return p.startPlugins()
	}

	return nil
}

// loadPlugins 加载插件
func (p *PipelineInstance) loadPlugins() error {
	// 加载采集器
	if p.config.Collector != nil && p.config.Collector.Enabled {
		collector, err := p.pluginManager.LoadPlugin(
			p.config.Collector.Name,
			pipeline.CollectorType,
			p.config.Collector.Config,
		)
		if err != nil {
			return fmt.Errorf("failed to load collector %s: %w", p.config.Collector.Name, err)
		}
		p.collector = collector
	}

	// 加载处理器
	p.processors = make([]pipeline.PipelinePlugin, 0, len(p.config.Processors))
	for _, processorConfig := range p.config.Processors {
		if !processorConfig.Enabled {
			continue
		}

		processor, err := p.pluginManager.LoadPlugin(
			processorConfig.Name,
			pipeline.ProcessorType,
			processorConfig.Config,
		)
		if err != nil {
			return fmt.Errorf("failed to load processor %s: %w", processorConfig.Name, err)
		}
		p.processors = append(p.processors, processor)
	}

	return nil
}

// startPlugins 启动插件
func (p *PipelineInstance) startPlugins() error {
	// 启动采集器
	if p.collector != nil {
		if err := p.collector.Start(p.ctx); err != nil {
			return fmt.Errorf("failed to start collector: %w", err)
		}
	}

	// 启动处理器
	for _, processor := range p.processors {
		if err := processor.Start(p.ctx); err != nil {
			return fmt.Errorf("failed to start processor %s: %w", processor.GetName(), err)
		}
	}

	return nil
}

// stopPlugins 停止插件
func (p *PipelineInstance) stopPlugins() {
	// 停止采集器
	if p.collector != nil {
		if err := p.collector.Stop(); err != nil {
			p.logger.Error("Failed to stop collector", zap.Error(err))
		}
	}

	// 停止处理器
	for _, processor := range p.processors {
		if err := processor.Stop(); err != nil {
			p.logger.Error("Failed to stop processor",
				zap.String("name", processor.GetName()),
				zap.Error(err))
		}
	}
}

// collectionLoop 采集循环
func (p *PipelineInstance) collectionLoop() {
	if p.collector == nil {
		return
	}

	collector, ok := p.collector.(interface {
		Collect(context.Context) (*pipeline.PipelineData, error)
		GetCollectInterval() time.Duration
	})
	if !ok {
		p.logger.Error("Collector does not support collection interface")
		return
	}

	interval := collector.GetCollectInterval()
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			data, err := collector.Collect(p.ctx)
			if err != nil {
				p.logger.Error("Collection failed", zap.Error(err))
				p.errorCount++
				p.lastError = err
				continue
			}

			if data != nil {
				select {
				case p.dataChan <- data:
				case <-p.ctx.Done():
					return
				default:
					p.logger.Warn("Data channel full, dropping data")
				}
			}
		}
	}
}

// dataProcessingLoop 数据处理循环
func (p *PipelineInstance) dataProcessingLoop() {
	for {
		select {
		case <-p.ctx.Done():
			return
		case data, ok := <-p.dataChan:
			if !ok {
				return
			}

			startTime := time.Now()
			processedData, err := p.processData(p.ctx, data)
			if err != nil {
				p.logger.Error("Data processing failed", zap.Error(err))
				p.errorCount++
				p.lastError = err
				continue
			}

			p.processedCount++
			p.updateLatencyMetrics(time.Since(startTime))

			// 发送到结果通道
			select {
			case p.resultChan <- processedData:
			case <-p.ctx.Done():
				return
			default:
				p.logger.Warn("Result channel full, dropping result")
			}
		}
	}
}

// processData 处理数据
func (p *PipelineInstance) processData(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	currentData := data

	// 通过处理器链处理数据
	for _, processor := range p.processors {
		processedData, err := processor.Process(ctx, currentData)
		if err != nil {
			return nil, fmt.Errorf("processor %s failed: %w", processor.GetName(), err)
		}
		currentData = processedData
	}

	return currentData, nil
}

// updateLatencyMetrics 更新延迟指标
func (p *PipelineInstance) updateLatencyMetrics(latency time.Duration) {
	p.latencySum += latency
	p.latencyCount++
	p.avgLatency = p.latencySum / time.Duration(p.latencyCount)

	if latency > p.maxLatency {
		p.maxLatency = latency
	}

	if latency < p.minLatency {
		p.minLatency = latency
	}
}

// updateResourceMetrics 更新资源使用指标
func (p *PipelineInstance) updateResourceMetrics() {
	// 获取内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	p.memoryUsage = int64(memStats.Alloc / 1024 / 1024) // MB

	// 获取Goroutine数量
	p.goroutineCount = runtime.NumGoroutine()

	// CPU使用率需要更复杂的计算，这里简化为0
	p.cpuUsage = 0.0
}
