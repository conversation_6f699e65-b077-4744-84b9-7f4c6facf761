package pipeline

import (
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"strings"
	"sync"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

// PluginManager 简化的插件管理器
type PluginManager struct {
	// 基础配置
	pluginsDir string
	logger     *zap.Logger

	// 插件注册表
	loadedPlugins map[string]*LoadedPlugin
	factories     map[string]pipeline.PluginFactory // 按插件名称存储工厂

	// 同步
	mutex sync.RWMutex
}

// LoadedPlugin 已加载的插件
type LoadedPlugin struct {
	Name     string
	Version  string
	Type     pipeline.PluginType
	Path     string
	Plugin   *plugin.Plugin
	Factory  pipeline.PluginFactory
	LoadedAt int64
}

// NewPluginManager 创建新的插件管理器
func NewPluginManager(pluginsDir string, logger *zap.Logger) *PluginManager {
	return &PluginManager{
		pluginsDir:    pluginsDir,
		logger:        logger,
		loadedPlugins: make(map[string]*LoadedPlugin),
		factories:     make(map[string]pipeline.PluginFactory),
	}
}

// Initialize 初始化插件管理器
func (pm *PluginManager) Initialize() error {
	pm.logger.Info("Initializing plugin manager", zap.String("plugins_dir", pm.pluginsDir))

	// 确保插件目录存在
	if err := os.MkdirAll(pm.pluginsDir, 0755); err != nil {
		return fmt.Errorf("failed to create plugins directory: %w", err)
	}

	// 扫描并加载插件
	if err := pm.scanAndLoadPlugins(); err != nil {
		return fmt.Errorf("failed to scan and load plugins: %w", err)
	}

	pm.logger.Info("Plugin manager initialized successfully",
		zap.Int("loaded_plugins", len(pm.loadedPlugins)))

	return nil
}

// scanAndLoadPlugins 扫描并加载插件
func (pm *PluginManager) scanAndLoadPlugins() error {
	// 用于跟踪已加载的插件名称（不包含版本）
	loadedPluginNames := make(map[string]string) // name -> version

	return filepath.Walk(pm.pluginsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.so文件
		if !info.IsDir() && strings.HasSuffix(path, ".so") {
			// 优先加载带版本号的插件文件
			fileName := info.Name()
			pluginName := pm.extractPluginName(fileName)

			// 检查是否已经加载了该插件的其他版本
			if existingVersion, exists := loadedPluginNames[pluginName]; exists {
				// 比较版本，优先加载版本号更高的插件
				currentVersion := pm.extractPluginVersion(fileName)
				if pm.compareVersions(currentVersion, existingVersion) <= 0 {
					pm.logger.Info("Skipping plugin due to higher version already loaded",
						zap.String("path", path),
						zap.String("current_version", currentVersion),
						zap.String("loaded_version", existingVersion))
					return nil
				} else {
					// 卸载旧版本
					pm.unloadPluginByName(pluginName, existingVersion)
				}
			}

			if err := pm.loadPlugin(path); err != nil {
				pm.logger.Error("Failed to load plugin",
					zap.String("path", path),
					zap.Error(err))
				// 继续加载其他插件，不因为一个插件失败而停止
			} else {
				// 记录已加载的插件
				version := pm.extractPluginVersion(fileName)
				loadedPluginNames[pluginName] = version
			}
		}

		return nil
	})
}

// loadPlugin 加载单个插件
func (pm *PluginManager) loadPlugin(pluginPath string) error {
	pm.logger.Info("Loading plugin", zap.String("path", pluginPath))

	// 打开插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return fmt.Errorf("failed to open plugin: %w", err)
	}

	// 查找插件工厂函数
	factorySymbol, err := p.Lookup("NewPluginFactory")
	if err != nil {
		return fmt.Errorf("plugin factory function not found: %w", err)
	}

	// 类型断言
	factoryFunc, ok := factorySymbol.(func() pipeline.PluginFactory)
	if !ok {
		return fmt.Errorf("invalid plugin factory function signature")
	}

	// 创建工厂实例
	factory := factoryFunc()
	if factory == nil {
		return fmt.Errorf("plugin factory returned nil")
	}

	// 获取插件信息
	info := factory.GetPluginInfo()
	if info == nil {
		return fmt.Errorf("plugin info is nil")
	}

	// 生成插件键
	pluginKey := fmt.Sprintf("%s:%s", info.Name, info.Version)

	// 检查是否已加载
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if _, exists := pm.loadedPlugins[pluginKey]; exists {
		return fmt.Errorf("plugin already loaded: %s", pluginKey)
	}

	// 保存插件信息
	loadedPlugin := &LoadedPlugin{
		Name:     info.Name,
		Version:  info.Version,
		Type:     info.Type,
		Path:     pluginPath,
		Plugin:   p,
		Factory:  factory,
		LoadedAt: info.LoadedAt.Unix(),
	}

	pm.loadedPlugins[pluginKey] = loadedPlugin
	pm.factories[info.Name] = factory

	pm.logger.Info("Plugin loaded successfully",
		zap.String("name", info.Name),
		zap.String("version", info.Version),
		zap.String("type", string(info.Type)))

	return nil
}

// LoadPlugin 加载插件实例
func (pm *PluginManager) LoadPlugin(name string, pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[name]
	if !exists {
		return nil, fmt.Errorf("plugin factory not found for name: %s", name)
	}

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pluginType, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create plugin %s: %w", name, err)
	}

	// 初始化插件
	if err := pluginInstance.Initialize(config); err != nil {
		return nil, fmt.Errorf("failed to initialize plugin %s: %w", name, err)
	}

	return pluginInstance, nil
}

// IsPluginAvailable 检查插件是否可用
func (pm *PluginManager) IsPluginAvailable(name string, pluginType pipeline.PluginType) bool {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[name]
	if !exists {
		return false
	}

	// 检查工厂是否支持指定类型
	supportedTypes := factory.GetSupportedTypes()
	for _, supportedType := range supportedTypes {
		if supportedType == pluginType {
			return true
		}
	}

	return false
}

// GetAvailablePlugins 获取可用插件列表
func (pm *PluginManager) GetAvailablePlugins() []*LoadedPlugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]*LoadedPlugin, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// CreateCollectorPlugin 创建采集器插件
func (pm *PluginManager) CreateCollectorPlugin(config *pb.CollectorPluginConfig) (pipeline.CollectorPlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[config.Name]
	if !exists {
		return nil, fmt.Errorf("collector plugin factory not found for: %s", config.Name)
	}

	// 转换配置
	pluginConfig := convertStringMapToInterface(config.Config)

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pipeline.CollectorType, pluginConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create collector plugin: %w", err)
	}

	// 类型断言
	collector, ok := pluginInstance.(pipeline.CollectorPlugin)
	if !ok {
		return nil, fmt.Errorf("plugin is not a collector")
	}

	return collector, nil
}

// CreateProcessorPlugin 创建处理器插件
func (pm *PluginManager) CreateProcessorPlugin(config *pb.ProcessorPluginConfig) (pipeline.PipelinePlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[config.Name]
	if !exists {
		return nil, fmt.Errorf("processor plugin factory not found for: %s", config.Name)
	}

	// 转换配置
	pluginConfig := convertStringMapToInterface(config.Config)

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pipeline.ProcessorType, pluginConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create processor plugin: %w", err)
	}

	return pluginInstance, nil
}

// GetLoadedPlugins 获取已加载的插件列表
func (pm *PluginManager) GetLoadedPlugins() []*LoadedPlugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]*LoadedPlugin, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// GetPluginByName 根据名称获取插件
func (pm *PluginManager) GetPluginByName(name, version string) (*LoadedPlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	plugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return nil, fmt.Errorf("plugin not found: %s", pluginKey)
	}

	return plugin, nil
}

// ReloadPlugin 重新加载插件
func (pm *PluginManager) ReloadPlugin(name, version string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	loadedPlugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin not found: %s", pluginKey)
	}

	// 移除旧插件
	delete(pm.loadedPlugins, pluginKey)
	delete(pm.factories, loadedPlugin.Name)

	// 重新加载
	pm.mutex.Unlock() // 临时释放锁以调用loadPlugin
	err := pm.loadPlugin(loadedPlugin.Path)
	pm.mutex.Lock() // 重新获取锁

	if err != nil {
		return fmt.Errorf("failed to reload plugin: %w", err)
	}

	pm.logger.Info("Plugin reloaded successfully",
		zap.String("name", name),
		zap.String("version", version))

	return nil
}

// UnloadPlugin 卸载插件
func (pm *PluginManager) UnloadPlugin(name, version string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	loadedPlugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin not found: %s", pluginKey)
	}

	// 移除插件
	delete(pm.loadedPlugins, pluginKey)
	delete(pm.factories, loadedPlugin.Name)

	pm.logger.Info("Plugin unloaded",
		zap.String("name", name),
		zap.String("version", version))

	return nil
}

// ValidatePlugin 验证插件
func (pm *PluginManager) ValidatePlugin(pluginPath string) error {
	// 尝试打开插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return fmt.Errorf("failed to open plugin: %w", err)
	}

	// 查找必需的符号
	_, err = p.Lookup("NewPluginFactory")
	if err != nil {
		return fmt.Errorf("plugin factory function not found: %w", err)
	}

	return nil
}

// GetPluginStats 获取插件统计信息
func (pm *PluginManager) GetPluginStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_plugins"] = len(pm.loadedPlugins)
	stats["plugins_dir"] = pm.pluginsDir

	// 按类型统计
	typeStats := make(map[string]int)
	for _, plugin := range pm.loadedPlugins {
		typeStats[string(plugin.Type)]++
	}
	stats["by_type"] = typeStats

	// 插件列表
	pluginList := make([]map[string]interface{}, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		pluginInfo := map[string]interface{}{
			"name":      plugin.Name,
			"version":   plugin.Version,
			"type":      string(plugin.Type),
			"path":      plugin.Path,
			"loaded_at": plugin.LoadedAt,
		}
		pluginList = append(pluginList, pluginInfo)
	}
	stats["plugins"] = pluginList

	return stats
}

// Shutdown 关闭插件管理器
func (pm *PluginManager) Shutdown() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.logger.Info("Shutting down plugin manager")

	// 清理资源
	pm.loadedPlugins = make(map[string]*LoadedPlugin)
	pm.factories = make(map[string]pipeline.PluginFactory)

	pm.logger.Info("Plugin manager shutdown complete")

	return nil
}

// extractPluginName 从文件名中提取插件名称
func (pm *PluginManager) extractPluginName(fileName string) string {
	// 移除.so后缀
	name := strings.TrimSuffix(fileName, ".so")

	// 如果包含版本号（格式：name-version.so），提取名称部分
	if idx := strings.LastIndex(name, "-"); idx != -1 {
		// 检查是否为版本号格式（数字.数字.数字）
		version := name[idx+1:]
		if pm.isValidVersion(version) {
			return name[:idx]
		}
	}

	return name
}

// extractPluginVersion 从文件名中提取版本号
func (pm *PluginManager) extractPluginVersion(fileName string) string {
	// 移除.so后缀
	name := strings.TrimSuffix(fileName, ".so")

	// 如果包含版本号（格式：name-version.so），提取版本部分
	if idx := strings.LastIndex(name, "-"); idx != -1 {
		version := name[idx+1:]
		if pm.isValidVersion(version) {
			return version
		}
	}

	// 如果没有版本号，返回默认版本
	return "1.0.0"
}

// isValidVersion 检查是否为有效的版本号格式
func (pm *PluginManager) isValidVersion(version string) bool {
	// 简单的版本号验证：x.y.z 格式
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 {
			return false
		}
		for _, char := range part {
			if char < '0' || char > '9' {
				return false
			}
		}
	}

	return true
}

// compareVersions 比较两个版本号，返回 -1, 0, 1
func (pm *PluginManager) compareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	// 确保两个版本都有3个部分
	for len(parts1) < 3 {
		parts1 = append(parts1, "0")
	}
	for len(parts2) < 3 {
		parts2 = append(parts2, "0")
	}

	for i := 0; i < 3; i++ {
		num1 := pm.parseVersionPart(parts1[i])
		num2 := pm.parseVersionPart(parts2[i])

		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	return 0
}

// parseVersionPart 解析版本号的一个部分
func (pm *PluginManager) parseVersionPart(part string) int {
	num := 0
	for _, char := range part {
		if char >= '0' && char <= '9' {
			num = num*10 + int(char-'0')
		}
	}
	return num
}

// unloadPluginByName 根据名称和版本卸载插件
func (pm *PluginManager) unloadPluginByName(name, version string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	loadedPlugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin not found: %s", pluginKey)
	}

	// 移除插件
	delete(pm.loadedPlugins, pluginKey)
	delete(pm.factories, loadedPlugin.Name)

	pm.logger.Info("Plugin unloaded by name",
		zap.String("name", name),
		zap.String("version", version))

	return nil
}
