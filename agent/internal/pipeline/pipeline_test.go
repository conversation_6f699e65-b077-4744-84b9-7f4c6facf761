package pipeline

import (
	"testing"
	"time"

	cf "aiops/pkg/config"
	"aiops/pkg/log"
)

// TestPluginManagerBasic 测试插件管理器基本功能
func TestPluginManagerBasic(t *testing.T) {
	// 创建日志
	logConfig := cf.NewConfig("../../../config/agent_config.yaml")
	logger := log.NewLog(logConfig)

	// 创建插件管理器
	pluginManager := NewPluginManager("../../../plugins/build", logger.Logger)
	if err := pluginManager.Initialize(); err != nil {
		t.Fatalf("初始化插件管理器失败: %v", err)
	}

	t.Log("插件管理器初始化成功")
}

// TestPipelineOrchestrator 测试流水线编排器
func TestPipelineOrchestrator(t *testing.T) {
	// 创建日志
	logConfig := cf.NewConfig("../../../config/agent_config.yaml")
	logger := log.NewLog(logConfig)

	// 创建插件管理器
	pluginManager := NewPluginManager("../../../plugins/build", logger.Logger)
	if err := pluginManager.Initialize(); err != nil {
		t.Fatalf("初始化插件管理器失败: %v", err)
	}

	// 创建编排器
	orchestrator := NewPipelineOrchestrator(pluginManager, logger.Logger)
	if err := orchestrator.Start(); err != nil {
		t.Fatalf("启动编排器失败: %v", err)
	}
	defer orchestrator.Stop()

	// 创建测试流水线配置
	config := createTestPipelineConfig()

	// 创建流水线
	instance, err := orchestrator.CreatePipeline(config)
	if err != nil {
		t.Fatalf("创建流水线失败: %v", err)
	}

	// 启动流水线
	if err := orchestrator.StartPipeline(config.ID); err != nil {
		t.Fatalf("启动流水线失败: %v", err)
	}

	// 等待一段时间
	time.Sleep(2 * time.Second)

	// 检查流水线状态
	status := instance.GetStatus()
	if status.State != "running" {
		t.Errorf("期望流水线状态为 'running'，实际为 '%s'", status.State)
	}

	// 停止流水线
	if err := orchestrator.StopPipeline(config.ID); err != nil {
		t.Errorf("停止流水线失败: %v", err)
	}

	// 删除流水线
	if err := orchestrator.DeletePipeline(config.ID); err != nil {
		t.Errorf("删除流水线失败: %v", err)
	}

	t.Log("流水线编排器测试完成")
}

// createTestPipelineConfig 创建测试流水线配置
func createTestPipelineConfig() *PipelineConfig {
	return &PipelineConfig{
		ID:          "test-pipeline",
		Name:        "测试流水线",
		Description: "用于测试的流水线",
		Version:     "1.0",
		Enabled:     true,
		BufferSize:  100,
		WorkerCount: 1,
		Timeout:     30 * time.Second,
		Collector: &CollectorConfig{
			Name:     "system_collector",
			Type:     "system",
			Interval: 5 * time.Second,
			Enabled:  true,
			Config: map[string]interface{}{
				"interval":      "5s",
				"enable_cpu":    true,
				"enable_memory": true,
			},
		},
		Processors: []*ProcessorConfig{
			{
				Name:        "threshold_processor",
				Type:        "processor",
				Enabled:     true,
				Concurrency: 1,
				Timeout:     10 * time.Second,
				Order:       1,
				Config: map[string]interface{}{
					"thresholds": map[string]interface{}{
						"system.memory.alloc": 1073741824.0, // 1GB
						"system.goroutines":   1000.0,
					},
				},
			},
		},
		ErrorHandling: &ErrorHandlingConfig{
			Strategy:   "retry",
			MaxRetries: 3,
			RetryDelay: 5 * time.Second,
		},
		ResourceLimits: &ResourceLimitsConfig{
			MaxMemoryMB:    256,
			MaxCPUPercent:  50,
			MaxGoroutines:  100,
			ProcessTimeout: 30 * time.Second,
			CollectTimeout: 10 * time.Second,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}
