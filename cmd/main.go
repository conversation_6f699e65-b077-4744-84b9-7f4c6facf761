package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"aiops/internal/alerter"
	"aiops/internal/analyzer"
	"aiops/internal/api"
	"aiops/internal/collector"
	"aiops/internal/config"
	"aiops/internal/database"
	"aiops/internal/scheduler"
	"aiops/internal/storage"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

func main() {
	// 初始化日志
	logger := log.NewLog(nil)
	logger.Info("AIOps 监控系统启动中...", zap.String("version", "3.0.0-simplified"))

	// 加载配置
	cfg, err := config.Load("config/aiops.yaml")
	if err != nil {
		logger.Error("加载配置失败", zap.Error(err))
		os.Exit(1)
	}

	// 初始化数据库
	db, err := database.Initialize(cfg.Database, logger.Logger)
	if err != nil {
		logger.Error("初始化数据库失败", zap.Error(err))
		os.Exit(1)
	}

	// 初始化存储层
	storage := storage.New(db, logger.Logger)

	// 初始化管理器
	collectorManager := collector.NewManager(logger.Logger)
	analyzerManager := analyzer.NewManager(storage, logger.Logger)
	alerterManager := alerter.NewManager(cfg.Email, logger.Logger)

	// 初始化任务调度器
	scheduler := scheduler.New(storage, collectorManager, analyzerManager, alerterManager, logger.Logger)

	// 启动调度器
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := scheduler.Start(ctx); err != nil {
		logger.Error("启动调度器失败", zap.Error(err))
		os.Exit(1)
	}

	// 初始化并启动 HTTP API 服务器
	apiServer := api.NewServer(cfg.Server, storage, scheduler, logger.Logger)
	if err := apiServer.Start(); err != nil {
		logger.Error("启动 API 服务器失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("AIOps 监控系统启动完成")
	logger.Info("HTTP API 服务监听于", zap.String("地址", fmt.Sprintf("http://localhost:%d", cfg.Server.Port)))
	logger.Info("按 Ctrl+C 退出")

	// 等待中断信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	// 优雅关闭
	logger.Info("正在关闭服务...")

	// 停止调度器
	cancel()

	// 关闭 API 服务器
	if err := apiServer.Stop(); err != nil {
		logger.Error("关闭 API 服务器失败", zap.Error(err))
	}

	logger.Info("AIOps 监控系统已关闭")
}
