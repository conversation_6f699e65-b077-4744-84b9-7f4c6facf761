<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIOps 监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .status-item {
            text-align: center;
            padding: 1rem;
            border-radius: 6px;
            background: #f8f9fa;
        }
        
        .status-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .status-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0.25rem;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .task-list {
            margin-top: 1rem;
        }
        
        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-name {
            font-weight: 500;
        }
        
        .task-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        
        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }
        
        .api-section {
            margin-top: 2rem;
        }
        
        .api-endpoint {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .method {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 AIOps 监控系统</h1>
        <p>简化架构 - 统一任务管理</p>
    </div>
    
    <div class="container">
        <div class="dashboard">
            <div class="card">
                <h3>📊 系统状态</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="taskCount">-</div>
                        <div class="status-label">活跃任务</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="alertCount">-</div>
                        <div class="status-label">未解决告警</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>⚡ 快速操作</h3>
                <button class="btn btn-success" onclick="createSampleTasks()">创建示例任务</button>
                <button class="btn" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-danger" onclick="clearAllTasks()">清空所有任务</button>
            </div>
        </div>
        
        <div class="card">
            <h3>📋 任务列表</h3>
            <div id="taskList" class="task-list">
                <p>正在加载任务...</p>
            </div>
        </div>
        
        <div class="card api-section">
            <h3>🔗 API 接口</h3>
            <div class="api-endpoint">
                <span class="method">GET</span> /api/v1/tasks - 获取任务列表
            </div>
            <div class="api-endpoint">
                <span class="method">POST</span> /api/v1/tasks - 创建新任务
            </div>
            <div class="api-endpoint">
                <span class="method">GET</span> /api/v1/metrics - 获取指标数据
            </div>
            <div class="api-endpoint">
                <span class="method">GET</span> /api/v1/alerts - 获取告警事件
            </div>
            <div class="api-endpoint">
                <span class="method">GET</span> /health - 健康检查
            </div>
        </div>
    </div>
    
    <script>
        // 加载任务列表
        async function loadTasks() {
            try {
                const response = await fetch('/api/v1/tasks');
                const data = await response.json();
                const tasks = data.tasks || [];
                
                document.getElementById('taskCount').textContent = tasks.length;
                
                const taskList = document.getElementById('taskList');
                if (tasks.length === 0) {
                    taskList.innerHTML = '<p>暂无任务</p>';
                } else {
                    taskList.innerHTML = tasks.map(task => `
                        <div class="task-item">
                            <div>
                                <div class="task-name">${task.name}</div>
                                <small>${task.type} - ${task.description || '无描述'}</small>
                            </div>
                            <div>
                                <span class="task-status ${task.enabled ? 'status-running' : 'status-stopped'}">
                                    ${task.enabled ? '运行中' : '已停止'}
                                </span>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载任务失败:', error);
                document.getElementById('taskList').innerHTML = '<p>加载失败</p>';
            }
        }
        
        // 加载告警数量
        async function loadAlerts() {
            try {
                const response = await fetch('/api/v1/alerts/unresolved');
                const data = await response.json();
                const alerts = data.alerts || [];
                document.getElementById('alertCount').textContent = alerts.length;
            } catch (error) {
                console.error('加载告警失败:', error);
                document.getElementById('alertCount').textContent = '?';
            }
        }
        
        // 创建示例任务
        async function createSampleTasks() {
            const sampleTasks = [
                {
                    name: "系统指标采集",
                    type: "collect",
                    config: {
                        collector_type: "system",
                        metrics: ["cpu", "memory", "disk", "network"]
                    },
                    schedule: {
                        type: "interval",
                        interval: "30s"
                    },
                    enabled: true,
                    description: "每30秒采集一次系统指标"
                },
                {
                    name: "CPU异常检测",
                    type: "analyze",
                    config: {
                        analyzer_type: "anomaly_detection",
                        metric: "cpu_usage_percent",
                        algorithm: "statistical",
                        threshold: 2.0,
                        window: "5m"
                    },
                    schedule: {
                        type: "interval",
                        interval: "1m"
                    },
                    enabled: true,
                    description: "每分钟检测CPU使用率异常"
                }
            ];
            
            for (const task of sampleTasks) {
                try {
                    await fetch('/api/v1/tasks', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(task)
                    });
                } catch (error) {
                    console.error('创建任务失败:', error);
                }
            }
            
            alert('示例任务创建完成！');
            refreshData();
        }
        
        // 清空所有任务
        async function clearAllTasks() {
            if (!confirm('确定要删除所有任务吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/v1/tasks');
                const data = await response.json();
                const tasks = data.tasks || [];
                
                for (const task of tasks) {
                    await fetch(`/api/v1/tasks/${task.id}`, {
                        method: 'DELETE'
                    });
                }
                
                alert('所有任务已删除！');
                refreshData();
            } catch (error) {
                console.error('删除任务失败:', error);
                alert('删除失败！');
            }
        }
        
        // 刷新数据
        function refreshData() {
            loadTasks();
            loadAlerts();
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            // 每30秒自动刷新
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
