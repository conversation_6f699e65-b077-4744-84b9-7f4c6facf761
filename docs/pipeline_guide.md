# DevInsight 流水线系统指南

## 概述

DevInsight 流水线系统采用插件式架构，主要用于数据收集、处理和分析。系统由控制平面(Control Plane)和代理(Agent)组成，通过灵活的配置实现各种数据处理任务。

## 流水线核心组件

1. **采集器 (Collector)** - 负责从各种数据源收集数据
2. **处理器 (Processor)** - 处理、转换和分析采集的数据
3. **管道 (Pipeline)** - 连接采集器和处理器，形成完整的数据流
4. **插件系统** - 提供可扩展性，支持自定义采集器和处理器

## 流水线配置详解

### 基本结构

流水线配置采用YAML格式，主要包含以下几个部分：

```yaml
pipeline_id: "my_pipeline"   # 唯一标识符
name: "我的流水线"           # 流水线名称
description: "描述信息"      # 详细描述
version: "1.0.0"            # 版本号
enabled: true               # 是否启用
```

### 基础配置项

```yaml
buffer_size: 100           # 缓冲区大小
worker_count: 2            # 工作线程数
timeout_seconds: 30        # 超时时间(秒)
retry_attempts: 3          # 重试次数
retry_delay_seconds: 5     # 重试延迟(秒)
```

### 采集器配置

采集器负责从数据源获取数据，每个流水线只能有一个采集器：

```yaml
collector:
  name: "system_collector"        # 采集器名称
  type: "system"                  # 采集器类型
  interval_seconds: 60            # 采集间隔(秒)
  enabled: true                   # 是否启用
  config:                         # 采集器特定配置
    collect_cpu: "true"
    collect_memory: "true"
```

### 处理器配置

处理器对采集的数据进行处理，一个流水线可以有多个处理器，按order顺序执行：

```yaml
processors:
  - name: "threshold_processor"   # 处理器名称
    type: "threshold"             # 处理器类型
    enabled: true                 # 是否启用
    order: 1                      # 执行顺序
    config:                       # 处理器特定配置
      metric_key: "cpu_usage"
      threshold: "80"
      
  - name: "another_processor"
    type: "filter"
    enabled: true
    order: 2
    config:
      filter_condition: "value > 10"
```

### 监控配置

```yaml
enable_metrics: true              # 是否启用指标监控
enable_tracing: false             # 是否启用追踪
metrics_interval_seconds: 60      # 指标收集间隔(秒)
```

### 错误处理配置

```yaml
error_handling:
  strategy: "retry"               # 错误处理策略: retry, ignore, circuit_breaker
  max_retries: 3                  # 最大重试次数
  retry_delay_seconds: 5          # 重试延迟(秒)
  
  # 熔断器配置(可选)
  circuit_breaker:
    error_threshold: 5            # 错误阈值
    success_threshold: 2          # 成功阈值
    timeout_seconds: 60           # 熔断超时(秒)
    
  # 死信队列配置(可选)
  dead_letter:
    enabled: true                 # 是否启用死信队列
    path: "/path/to/dead/letter"  # 死信存储路径
```

### 资源限制配置

```yaml
resource_limits:
  max_cpu_percent: 50             # 最大CPU使用率(%)
  max_memory_mb: 100              # 最大内存使用量(MB)
  max_disk_mb: 500                # 最大磁盘使用量(MB)
```

## 内置采集器类型

DevInsight系统内置了多种采集器，以下是常用类型：

1. **system_collector** - 系统指标采集器
   - 收集CPU、内存、磁盘和网络使用情况
   - 配置项：`collect_cpu`, `collect_memory`, `collect_disk`, `collect_network`

2. **mysql_collector** - MySQL数据库采集器
   - 收集数据库性能指标和查询结果
   - 配置项：`host`, `port`, `username`, `password`, `database`, `query`

3. **redis_collector** - Redis数据库采集器
   - 收集Redis服务器状态和性能指标
   - 配置项：`host`, `port`, `password`, `commands`

## 内置处理器类型

1. **threshold_processor** - 阈值处理器
   - 当指标超过阈值时触发动作
   - 配置项：`metric_key`, `threshold`, `operator`, `alert_message`

2. **filter_processor** - 过滤处理器
   - 根据条件过滤数据
   - 配置项：`filter_condition`, `action`

3. **transform_processor** - 转换处理器
   - 数据格式转换和映射
   - 配置项：`mapping`, `format_type`

4. **email_alerter** - 邮件告警处理器
   - 发送告警邮件
   - 配置项：`smtp_server`, `smtp_port`, `username`, `password`, `recipients`

## 流水线生命周期

1. **创建** - 通过API创建流水线配置
2. **部署** - 将流水线部署到特定的Agent
3. **启动** - 启动流水线处理数据
4. **监控** - 监控流水线运行状态和性能
5. **停止/重启** - 停止或重启流水线
6. **更新** - 更新流水线配置
7. **删除** - 删除流水线

## 常用API

以下是管理流水线的常用API：

1. 创建流水线: `POST /api/pipelines`
2. 获取流水线列表: `GET /api/pipelines`
3. 获取流水线详情: `GET /api/pipelines/{id}`
4. 更新流水线: `PUT /api/pipelines/{id}`
5. 删除流水线: `DELETE /api/pipelines/{id}`
6. 启动流水线: `POST /api/pipelines/{id}/start`
7. 停止流水线: `POST /api/pipelines/{id}/stop`
8. 重启流水线: `POST /api/pipelines/{id}/restart`
9. 获取流水线状态: `GET /api/pipelines/{id}/status`
10. 部署流水线到Agent: `POST /api/agents/{agentId}/deploy`

## 示例应用场景

### 1. 系统监控流水线

```yaml
pipeline_id: "system_monitor"
name: "系统监控流水线"
description: "监控服务器系统指标并发送告警"
collector:
  name: "system_collector"
  type: "system"
  interval_seconds: 30
processors:
  - name: "high_cpu_alert"
    type: "threshold"
    order: 1
    config:
      metric_key: "cpu_usage_percent"
      threshold: "90"
      operator: ">"
  - name: "email_notification"
    type: "email_alerter"
    order: 2
    config:
      recipients: "<EMAIL>"
```

### 2. 数据库监控流水线

```yaml
pipeline_id: "db_monitor"
name: "数据库监控流水线"
description: "监控MySQL数据库性能并记录慢查询"
collector:
  name: "mysql_collector"
  type: "mysql"
  interval_seconds: 60
  config:
    host: "db.example.com"
    query: "SHOW GLOBAL STATUS"
processors:
  - name: "connections_filter"
    type: "filter"
    order: 1
    config:
      include: "Connections,Threads_connected,Slow_queries"
  - name: "slow_query_alert"
    type: "threshold"
    order: 2
    config:
      metric_key: "Slow_queries"
      threshold: "10"
      operator: ">"
```

## 最佳实践

1. **合理配置采集间隔** - 根据数据源特性和系统负载设置合适的采集间隔
2. **优化处理器顺序** - 先进行数据过滤再进行复杂处理，减少资源消耗
3. **设置资源限制** - 防止单个流水线占用过多系统资源
4. **使用错误处理机制** - 合理配置重试和熔断策略
5. **监控流水线状态** - 定期检查流水线运行状况和处理效率

## 流水线故障排查

1. 检查流水线状态 - 使用API获取最新状态
2. 查看系统日志 - 流水线日志位于 `./logs/` 目录
3. 验证插件可用性 - 确保所需插件已正确加载
4. 检查网络连接 - 确保Agent与Control Plane通信正常
5. 资源使用情况 - 检查系统资源是否充足
