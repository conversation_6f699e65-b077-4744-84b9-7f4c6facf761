# AIOps 简化架构设计方案

## 背景

当前架构存在以下复杂性：
1. Agent 和 Control Plane 分离导致部署复杂
2. 流水线概念抽象，用户理解成本高
3. 插件系统增加了不必要的复杂性

## 简化架构设计

### 1. 单体架构

```
┌─────────────────────────────────────────┐
│           AIOps 监控系统                │
├─────────────────────────────────────────┤
│  Web UI (管理界面)                      │
├─────────────────────────────────────────┤
│  HTTP API (任务管理)                    │
├─────────────────────────────────────────┤
│  任务调度器 (Task Scheduler)            │
├─────────────────────────────────────────┤
│  内置功能模块                           │
│  ├── 系统监控模块                       │
│  ├── 日志收集模块                       │
│  ├── 数据库监控模块                     │
│  ├── HTTP 监控模块                      │
│  ├── 分析引擎模块                       │
│  └── 告警通知模块                       │
├─────────────────────────────────────────┤
│  数据存储 (SQLite/PostgreSQL)          │
├─────────────────────────────────────────┤
│  配置管理                               │
└─────────────────────────────────────────┘
```

### 2. 任务式设计

#### 2.1 任务类型

```go
type TaskType string

const (
    TaskTypeCollect  TaskType = "collect"   // 采集任务
    TaskTypeAnalyze  TaskType = "analyze"   // 分析任务
    TaskTypeAlert    TaskType = "alert"     // 告警任务
)

type Task struct {
    ID          string            `json:"id"`
    Name        string            `json:"name"`
    Type        TaskType          `json:"type"`
    Config      map[string]interface{} `json:"config"`
    Schedule    ScheduleConfig    `json:"schedule"`
    Enabled     bool              `json:"enabled"`
    CreatedAt   time.Time         `json:"created_at"`
    UpdatedAt   time.Time         `json:"updated_at"`
}

type ScheduleConfig struct {
    Type        string `json:"type"`        // "interval", "cron"
    Interval    string `json:"interval"`    // "30s", "5m", "1h"
    CronExpr    string `json:"cron_expr"`   // "0 */5 * * * *"
}
```

#### 2.2 采集任务示例

```json
{
  "id": "system-metrics-001",
  "name": "系统指标采集",
  "type": "collect",
  "config": {
    "collector_type": "system",
    "metrics": ["cpu", "memory", "disk", "network"],
    "targets": ["localhost"]
  },
  "schedule": {
    "type": "interval",
    "interval": "30s"
  },
  "enabled": true
}
```

#### 2.3 分析任务示例

```json
{
  "id": "cpu-anomaly-001",
  "name": "CPU异常检测",
  "type": "analyze",
  "config": {
    "analyzer_type": "anomaly_detection",
    "metric": "cpu_usage",
    "threshold": 80,
    "window": "5m",
    "algorithm": "statistical"
  },
  "schedule": {
    "type": "interval",
    "interval": "1m"
  },
  "enabled": true
}
```

#### 2.4 告警任务示例

```json
{
  "id": "email-alert-001",
  "name": "邮件告警",
  "type": "alert",
  "config": {
    "alert_type": "email",
    "recipients": ["<EMAIL>"],
    "conditions": [
      {
        "metric": "cpu_usage",
        "operator": ">",
        "value": 90
      }
    ]
  },
  "schedule": {
    "type": "event_driven"
  },
  "enabled": true
}
```

### 3. 内置功能模块

#### 3.1 系统监控模块

```go
type SystemCollector struct {
    logger *zap.Logger
    config SystemCollectorConfig
}

type SystemCollectorConfig struct {
    Interval time.Duration `json:"interval"`
    Metrics  []string      `json:"metrics"`
}

func (c *SystemCollector) Collect(ctx context.Context) (*MetricData, error) {
    // 直接使用 gopsutil 等库收集系统指标
    // 无需动态加载插件
}
```

#### 3.2 日志收集模块

```go
type LogCollector struct {
    logger *zap.Logger
    config LogCollectorConfig
}

type LogCollectorConfig struct {
    LogFiles []string `json:"log_files"`
    Pattern  string   `json:"pattern"`
}

func (c *LogCollector) Collect(ctx context.Context) (*LogData, error) {
    // 直接实现日志收集逻辑
}
```

#### 3.3 分析引擎模块

```go
type AnalysisEngine struct {
    logger *zap.Logger
}

func (e *AnalysisEngine) DetectAnomalies(data *MetricData, config AnomalyConfig) (*AnalysisResult, error) {
    // 内置异常检测算法
    // 统计分析、机器学习等
}
```

### 4. 简化的 API 设计

#### 4.1 任务管理 API

```
POST   /api/v1/tasks              # 创建任务
GET    /api/v1/tasks              # 获取任务列表
GET    /api/v1/tasks/{id}         # 获取任务详情
PUT    /api/v1/tasks/{id}         # 更新任务
DELETE /api/v1/tasks/{id}         # 删除任务
POST   /api/v1/tasks/{id}/start   # 启动任务
POST   /api/v1/tasks/{id}/stop    # 停止任务
```

#### 4.2 监控数据 API

```
GET    /api/v1/metrics            # 获取指标数据
GET    /api/v1/logs               # 获取日志数据
GET    /api/v1/alerts             # 获取告警信息
```

## 实施计划

### Phase 1: 核心重构 (1-2周)

1. **移除 Agent 架构**
   - 删除 agent/ 目录
   - 合并功能到 control_plane
   - 移除 gRPC 通信逻辑

2. **简化项目结构**
   ```
   aiops/
   ├── cmd/main.go                # 单一入口
   ├── internal/
   │   ├── task/                  # 任务管理
   │   ├── collector/             # 内置采集器
   │   ├── analyzer/              # 内置分析器
   │   ├── alerter/               # 内置告警器
   │   ├── scheduler/             # 任务调度器
   │   ├── storage/               # 数据存储
   │   └── api/                   # HTTP API
   ├── web/                       # Web UI
   └── config/
   ```

3. **任务系统实现**
   - 实现 Task 模型和管理
   - 实现任务调度器
   - 实现基础的采集、分析、告警模块

### Phase 2: 功能迁移 (2-3周)

1. **迁移现有功能**
   - 将插件功能内置到对应模块
   - 迁移配置系统
   - 迁移数据存储逻辑

2. **简化配置**
   ```yaml
   # 简化的配置文件
   server:
     port: 8080
     
   database:
     driver: sqlite
     dsn: ./data/aiops.db
     
   tasks:
     default_interval: 30s
     max_concurrent: 10
   ```

### Phase 3: 测试和优化 (1周)

1. **功能测试**
2. **性能优化**
3. **文档更新**

## 优势分析

### 1. 降低复杂性
- **部署简单**：单个二进制文件
- **配置简单**：统一配置文件
- **维护简单**：单一服务架构

### 2. 提高可靠性
- **减少故障点**：无网络通信故障
- **简化状态管理**：无分布式状态同步
- **更好的错误处理**：本地调用更容易处理

### 3. 更好的用户体验
- **概念简单**：任务概念更直观
- **配置直观**：JSON/YAML 配置更简单
- **功能明确**：采集、分析、告警功能清晰

### 4. 开发效率
- **代码简化**：减少抽象层
- **测试简单**：单体架构更容易测试
- **调试方便**：本地调用更容易调试

## 风险评估

### 1. 功能限制
- **扩展性**：失去分布式部署能力
- **缓解方案**：支持多实例部署，通过负载均衡实现扩展

### 2. 单点故障
- **风险**：单个服务故障影响全部功能
- **缓解方案**：实现健康检查、自动重启、备份恢复

### 3. 资源限制
- **风险**：单机资源限制
- **缓解方案**：优化资源使用，支持水平扩展

## 结论

简化架构能够显著降低系统复杂性，提高开发和维护效率。对于中小型部署场景，这种架构更加实用和可靠。建议逐步实施，先实现核心功能，再根据实际需求决定是否需要分布式架构。
