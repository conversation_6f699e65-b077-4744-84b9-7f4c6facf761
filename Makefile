# AIOps 监控系统 Makefile

.PHONY: build run test clean install deps help

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
BINARY_NAME=aiops
BUILD_DIR=build
CONFIG_FILE=config/aiops.yaml

# 构建应用
build: ## 编译应用
	@echo "🔨 编译 AIOps 监控系统..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) cmd/main.go
	@echo "✅ 编译完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行应用
run: build ## 编译并运行应用
	@echo "🚀 启动 AIOps 监控系统..."
	@mkdir -p data logs web/static
	@./$(BUILD_DIR)/$(BINARY_NAME)

# 安装依赖
deps: ## 安装依赖包
	@echo "📦 安装依赖包..."
	@go mod tidy
	@go mod download
	@echo "✅ 依赖安装完成"

# 运行测试
test: build ## 运行系统测试
	@echo "🧪 运行系统测试..."
	@./test_system.sh

# 清理构建文件
clean: ## 清理构建文件和数据
	@echo "🧹 清理文件..."
	@rm -rf $(BUILD_DIR)
	@rm -rf data
	@rm -rf logs
	@echo "✅ 清理完成"

# 安装到系统
install: build ## 安装到系统路径
	@echo "📥 安装到系统..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "✅ 安装完成: /usr/local/bin/$(BINARY_NAME)"

# 创建示例配置
config: ## 创建示例配置文件
	@echo "⚙️ 创建配置文件..."
	@mkdir -p config
	@if [ ! -f $(CONFIG_FILE) ]; then \
		cp config/aiops.yaml.example $(CONFIG_FILE) 2>/dev/null || \
		echo "请手动创建配置文件: $(CONFIG_FILE)"; \
	fi

# 开发模式运行
dev: ## 开发模式运行（自动重启）
	@echo "🔧 开发模式启动..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "请安装 air: go install github.com/cosmtrek/air@latest"; \
		make run; \
	fi

# 格式化代码
fmt: ## 格式化代码
	@echo "🎨 格式化代码..."
	@go fmt ./...
	@echo "✅ 代码格式化完成"

# 代码检查
lint: ## 运行代码检查
	@echo "🔍 运行代码检查..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "请安装 golangci-lint"; \
		go vet ./...; \
	fi

# 生成文档
docs: ## 生成 API 文档
	@echo "📚 生成文档..."
	@if command -v swag > /dev/null; then \
		swag init -g cmd/main.go; \
	else \
		echo "请安装 swag: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# 创建发布包
release: build ## 创建发布包
	@echo "📦 创建发布包..."
	@mkdir -p release
	@tar -czf release/aiops-$(shell date +%Y%m%d).tar.gz \
		$(BUILD_DIR)/$(BINARY_NAME) \
		config/aiops.yaml \
		examples/ \
		web/ \
		README_NEW.md
	@echo "✅ 发布包创建完成: release/"

# 显示帮助信息
help: ## 显示帮助信息
	@echo "AIOps 监控系统 - 可用命令:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | \
		awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-12s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "使用示例:"
	@echo "  make build    # 编译应用"
	@echo "  make run      # 运行应用"
	@echo "  make test     # 运行测试"
	@echo "  make clean    # 清理文件"
