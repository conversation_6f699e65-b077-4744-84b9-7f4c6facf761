package main

import (
	"fmt"
	"time"

	"aiops/pkg/pipeline"
	plugininterface "aiops/plugins/interface"
)

// 错误定义
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.<PERSON>rrorf("plugin not running")
)

// MySQLPluginFactory MySQL插件工厂
type MySQLPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &MySQLPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *MySQLPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.CollectorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &MySQLCollectorPlugin{
		name:    "mysql_collector",
		version: "2.0.0",
		config:  config,
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *MySQLPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.CollectorType}
}

// GetPluginInfo 获取插件信息
func (f *MySQLPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "mysql_collector",
		Version:  "2.0.0",
		Type:     pipeline.CollectorType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "Enhanced MySQL database monitoring collector with multi-database support",
			"author":      "DevInsight Team",
			"category":    "database",
			"features": []string{
				"Multi-database support",
				"Custom SQL queries",
				"Extended metric collection",
				"Cross-database queries",
				"Connection pooling",
			},
		},
	}
}

// ValidateConfig 验证配置
func (f *MySQLPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 检查是否有数据库配置
	if databases, exists := config["databases"]; exists {
		return validateDatabasesConfig(databases)
	}

	// 向后兼容：检查单个数据库配置
	return validateSingleDatabaseConfig(config)
}

// validateDatabasesConfig 验证多数据库配置
func validateDatabasesConfig(databases interface{}) error {
	dbList, ok := databases.([]interface{})
	if !ok {
		return fmt.Errorf("databases must be an array")
	}

	if len(dbList) == 0 {
		return fmt.Errorf("at least one database must be configured")
	}

	for i, db := range dbList {
		dbConfig, ok := db.(map[string]interface{})
		if !ok {
			return fmt.Errorf("database[%d] must be an object", i)
		}

		if err := validateSingleDatabaseConfig(dbConfig); err != nil {
			return fmt.Errorf("database[%d]: %w", i, err)
		}
	}

	return nil
}

// validateSingleDatabaseConfig 验证单个数据库配置
func validateSingleDatabaseConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"host", "port", "username", "password"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}

	// 验证端口号
	if port, exists := config["port"]; exists {
		switch v := port.(type) {
		case int:
			if v <= 0 || v > 65535 {
				return fmt.Errorf("invalid port number: %d", v)
			}
		case float64:
			if v <= 0 || v > 65535 {
				return fmt.Errorf("invalid port number: %f", v)
			}
		case string:
			// 这里可以进一步验证字符串格式的端口号
		default:
			return fmt.Errorf("port must be a number")
		}
	}

	// 验证超时设置
	if timeout, exists := config["timeout"]; exists {
		if timeoutStr, ok := timeout.(string); ok {
			if _, err := time.ParseDuration(timeoutStr); err != nil {
				return fmt.Errorf("invalid timeout format: %s", timeoutStr)
			}
		}
	}

	return nil
}

// 全局插件入口函数 - 插件加载器需要

// GetPluginInfo 获取插件信息 (全局函数)
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.PluginInfo{
		Name:        "mysql_collector",
		Version:     "2.0.0",
		Type:        plugininterface.CollectorPlugin,
		Description: "MySQL数据库监控采集器插件 - 模块化版本",
		Author:      "DevInsight Team",
		License:     "MIT",
		Tags:        []string{"mysql", "database", "collector", "monitoring"},
		Homepage:    "https://github.com/devinsight/mysql-collector",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// CreatePluginFactory 创建插件工厂 (全局函数)
func CreatePluginFactory() interface{} {
	return NewPluginFactory()
}
