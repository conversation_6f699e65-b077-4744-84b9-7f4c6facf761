package main

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	pb "aiops/pkg/proto"
)

// DataCollector 数据采集器
type DataCollector struct {
	queries []DataQueryConfig
}

// NewDataCollector 创建数据采集器
func NewDataCollector(queries []DataQueryConfig) *DataCollector {
	return &DataCollector{
		queries: queries,
	}
}

// CollectData 采集数据
func (dc *DataCollector) CollectData(ctx context.Context, cm *ConnectionManager) ([]*pb.MetricData, error) {
	var allMetrics []*pb.MetricData

	for _, queryConfig := range dc.queries {
		metrics, err := dc.executeQuery(ctx, cm, queryConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to execute query %s: %w", queryConfig.Name, err)
		}
		allMetrics = append(allMetrics, metrics...)
	}

	return allMetrics, nil
}

// executeQuery 执行查询
func (dc *DataCollector) executeQuery(ctx context.Context, cm *ConnectionManager, config DataQueryConfig) ([]*pb.MetricData, error) {
	if config.CrossDB {
		return dc.executeCrossDBQuery(ctx, cm, config)
	}
	return dc.executeRegularQuery(ctx, cm, config)
}

// executeRegularQuery 执行常规查询
func (dc *DataCollector) executeRegularQuery(ctx context.Context, cm *ConnectionManager, config DataQueryConfig) ([]*pb.MetricData, error) {
	var allMetrics []*pb.MetricData

	// 确定目标数据库
	targetDatabases := config.Databases
	if len(targetDatabases) == 0 {
		// 如果没有指定，则在所有数据库执行
		connections := cm.GetAllConnections()
		for name := range connections {
			targetDatabases = append(targetDatabases, name)
		}
	}

	// 在每个目标数据库执行查询
	for _, dbName := range targetDatabases {
		db, err := cm.GetConnection(dbName)
		if err != nil {
			continue
		}

		metrics, err := dc.executeSingleQuery(ctx, db, dbName, config, cm)
		if err != nil {
			return nil, fmt.Errorf("failed to execute query on %s: %w", dbName, err)
		}
		allMetrics = append(allMetrics, metrics...)
	}

	return allMetrics, nil
}

// executeCrossDBQuery 执行跨数据库查询
func (dc *DataCollector) executeCrossDBQuery(ctx context.Context, cm *ConnectionManager, config DataQueryConfig) ([]*pb.MetricData, error) {
	// 跨数据库查询需要特殊处理
	// 这里简化实现，选择第一个数据库连接执行查询
	connections := cm.GetAllConnections()
	if len(connections) == 0 {
		return nil, fmt.Errorf("no database connections available")
	}

	var primaryDB *sql.DB
	var primaryDBName string
	for name, db := range connections {
		primaryDB = db
		primaryDBName = name
		break
	}

	return dc.executeSingleQuery(ctx, primaryDB, primaryDBName, config, cm)
}

// executeSingleQuery 执行单个查询
func (dc *DataCollector) executeSingleQuery(ctx context.Context, db *sql.DB, dbName string, config DataQueryConfig, cm *ConnectionManager) ([]*pb.MetricData, error) {
	// 设置查询超时
	queryCtx := ctx
	if config.Timeout > 0 {
		var cancel context.CancelFunc
		queryCtx, cancel = context.WithTimeout(ctx, config.Timeout)
		defer cancel()
	}

	rows, err := db.QueryContext(queryCtx, config.SQL)
	if err != nil {
		return nil, fmt.Errorf("failed to execute SQL: %w", err)
	}
	defer rows.Close()

	return dc.processQueryResults(rows, dbName, config, cm)
}

// processQueryResults 处理查询结果
func (dc *DataCollector) processQueryResults(rows *sql.Rows, dbName string, config DataQueryConfig, cm *ConnectionManager) ([]*pb.MetricData, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	var metrics []*pb.MetricData
	now := time.Now().Unix()

	// 获取数据库配置用于设备ID
	dbConfig, err := cm.GetDatabaseConfig(dbName)
	if err != nil {
		return nil, err
	}
	deviceID := fmt.Sprintf("%s:%d", dbConfig.Host, dbConfig.Port)

	for rows.Next() {
		// 扫描所有列
		values := make([]interface{}, len(columns))
		scanArgs := make([]interface{}, len(columns))
		for i := range values {
			scanArgs[i] = &values[i]
		}

		if err := rows.Scan(scanArgs...); err != nil {
			continue
		}

		// 根据查询结果创建指标
		resultMetrics := dc.createMetricsFromRow(columns, values, now, deviceID, dbName, config)
		metrics = append(metrics, resultMetrics...)
	}

	return metrics, nil
}

// createMetricsFromRow 从行数据创建指标
func (dc *DataCollector) createMetricsFromRow(columns []string, values []interface{}, timestamp int64, deviceID, dbName string, config DataQueryConfig) []*pb.MetricData {
	var metrics []*pb.MetricData

	// 基础标签
	baseLabels := make(map[string]string)
	for k, v := range config.Tags {
		baseLabels[k] = v
	}
	baseLabels["database"] = dbName
	baseLabels["query"] = config.Name

	// 如果有指定输出键，则创建单个指标
	if config.OutputKey != "" {
		metric := dc.createSingleMetric(columns, values, timestamp, deviceID, config.OutputKey, baseLabels)
		if metric != nil {
			metrics = append(metrics, metric)
		}
		return metrics
	}

	// 否则，为每个数值列创建一个指标
	for i, column := range columns {
		value := values[i]
		if value == nil {
			continue
		}

		// 尝试转换为数值
		var numericValue float64
		var isNumeric bool

		switch v := value.(type) {
		case int64:
			numericValue = float64(v)
			isNumeric = true
		case float64:
			numericValue = v
			isNumeric = true
		case string:
			// 字符串类型的指标
			labels := make(map[string]string)
			for k, v := range baseLabels {
				labels[k] = v
			}
			labels["column"] = column

			metricKey := fmt.Sprintf("mysql.data.%s.%s", config.Name, column)
			metric := &pb.MetricData{
				MetricKey: metricKey,
				ValueType: &pb.MetricData_StringValue{
					StringValue: v,
				},
				Timestamp: timestamp,
				DeviceId:  deviceID,
				Labels:    labels,
			}
			metrics = append(metrics, metric)
		case []byte:
			// 字节数组转换为字符串
			labels := make(map[string]string)
			for k, v := range baseLabels {
				labels[k] = v
			}
			labels["column"] = column

			metricKey := fmt.Sprintf("mysql.data.%s.%s", config.Name, column)
			metric := &pb.MetricData{
				MetricKey: metricKey,
				ValueType: &pb.MetricData_StringValue{
					StringValue: string(v),
				},
				Timestamp: timestamp,
				DeviceId:  deviceID,
				Labels:    labels,
			}
			metrics = append(metrics, metric)
		}

		// 数值类型的指标
		if isNumeric {
			labels := make(map[string]string)
			for k, v := range baseLabels {
				labels[k] = v
			}
			labels["column"] = column

			metricKey := fmt.Sprintf("mysql.data.%s.%s", config.Name, column)
			metric := &pb.MetricData{
				MetricKey: metricKey,
				ValueType: &pb.MetricData_NumericValue{
					NumericValue: numericValue,
				},
				Timestamp: timestamp,
				DeviceId:  deviceID,
				Labels:    labels,
			}
			metrics = append(metrics, metric)
		}
	}

	return metrics
}

// createSingleMetric 创建单个指标
func (dc *DataCollector) createSingleMetric(columns []string, values []interface{}, timestamp int64, deviceID, outputKey string, baseLabels map[string]string) *pb.MetricData {
	// 查找第一个数值列作为值
	var metricValue float64
	var hasValue bool

	// 将所有列作为标签
	labels := make(map[string]string)
	for k, v := range baseLabels {
		labels[k] = v
	}

	for i, column := range columns {
		value := values[i]
		if value == nil {
			continue
		}

		switch v := value.(type) {
		case int64:
			if !hasValue {
				metricValue = float64(v)
				hasValue = true
			} else {
				labels[strings.ToLower(column)] = fmt.Sprintf("%d", v)
			}
		case float64:
			if !hasValue {
				metricValue = v
				hasValue = true
			} else {
				labels[strings.ToLower(column)] = fmt.Sprintf("%f", v)
			}
		case string:
			labels[strings.ToLower(column)] = v
		case []byte:
			labels[strings.ToLower(column)] = string(v)
		default:
			labels[strings.ToLower(column)] = fmt.Sprintf("%v", v)
		}
	}

	if !hasValue {
		return nil
	}

	return &pb.MetricData{
		MetricKey: outputKey,
		ValueType: &pb.MetricData_NumericValue{
			NumericValue: metricValue,
		},
		Timestamp: timestamp,
		DeviceId:  deviceID,
		Labels:    labels,
	}
}

// AddQuery 添加查询
func (dc *DataCollector) AddQuery(config DataQueryConfig) {
	dc.queries = append(dc.queries, config)
}

// RemoveQuery 移除查询
func (dc *DataCollector) RemoveQuery(name string) error {
	for i, query := range dc.queries {
		if query.Name == name {
			dc.queries = append(dc.queries[:i], dc.queries[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("query not found: %s", name)
}

// GetQueries 获取所有查询
func (dc *DataCollector) GetQueries() []DataQueryConfig {
	return dc.queries
}

// UpdateQuery 更新查询
func (dc *DataCollector) UpdateQuery(name string, config DataQueryConfig) error {
	for i, query := range dc.queries {
		if query.Name == name {
			dc.queries[i] = config
			return nil
		}
	}
	return fmt.Errorf("query not found: %s", name)
}

// ValidateQuery 验证查询
func (dc *DataCollector) ValidateQuery(ctx context.Context, cm *ConnectionManager, config DataQueryConfig) error {
	// 选择一个数据库进行验证
	connections := cm.GetAllConnections()
	if len(connections) == 0 {
		return fmt.Errorf("no database connections available")
	}

	var db *sql.DB
	for _, connection := range connections {
		db = connection
		break
	}

	// 执行查询验证（使用LIMIT 0来避免返回数据）
	validateSQL := config.SQL
	if !strings.Contains(strings.ToUpper(validateSQL), "LIMIT") {
		validateSQL += " LIMIT 0"
	}

	_, err := db.QueryContext(ctx, validateSQL)
	return err
}
