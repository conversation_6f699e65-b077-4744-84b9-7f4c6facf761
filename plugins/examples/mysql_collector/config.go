package main

import (
	"fmt"
	"strconv"
	"time"
)

// PluginConfig 插件配置结构
type PluginConfig struct {
	Databases   []DatabaseConfig  `json:"databases"`
	Metrics     MetricsConfig     `json:"metrics"`
	DataQueries []DataQueryConfig `json:"data_queries"`
	Global      GlobalConfig      `json:"global"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Name     string            `json:"name"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Username string            `json:"username"`
	Password string            `json:"password"`
	Database string            `json:"database"`
	Charset  string            `json:"charset"`
	Timeout  time.Duration     `json:"timeout"`
	MaxConns int               `json:"max_connections"`
	Options  map[string]string `json:"options"`
	Tags     map[string]string `json:"tags"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	Enabled bool                 `json:"enabled"`
	Groups  []MetricGroupConfig  `json:"groups"`
	Custom  []CustomMetricConfig `json:"custom"`
	Global  MetricGlobalConfig   `json:"global"`
}

// MetricGroupConfig 指标组配置
type MetricGroupConfig struct {
	Name        string            `json:"name"`
	Enabled     bool              `json:"enabled"`
	Metrics     []string          `json:"metrics"`
	Description string            `json:"description"`
	Tags        map[string]string `json:"tags"`
}

// CustomMetricConfig 自定义指标配置
type CustomMetricConfig struct {
	Name         string            `json:"name"`
	SQL          string            `json:"sql"`
	MetricKey    string            `json:"metric_key"`
	ValueColumn  string            `json:"value_column"`
	LabelColumns []string          `json:"label_columns"`
	Description  string            `json:"description"`
	Tags         map[string]string `json:"tags"`
	Databases    []string          `json:"databases"` // 限制在特定数据库执行
}

// MetricGlobalConfig 指标全局配置
type MetricGlobalConfig struct {
	Prefix string            `json:"prefix"`
	Tags   map[string]string `json:"tags"`
	Labels map[string]string `json:"labels"`
}

// DataQueryConfig 数据查询配置
type DataQueryConfig struct {
	Name      string            `json:"name"`
	SQL       string            `json:"sql"`
	Databases []string          `json:"databases"`
	Schedule  string            `json:"schedule"`
	Timeout   time.Duration     `json:"timeout"`
	Tags      map[string]string `json:"tags"`
	CrossDB   bool              `json:"cross_db"`
	OutputKey string            `json:"output_key"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	ConnectionPool ConnectionPoolConfig `json:"connection_pool"`
	Logging        LoggingConfig        `json:"logging"`
	Performance    PerformanceConfig    `json:"performance"`
}

// ConnectionPoolConfig 连接池配置
type ConnectionPoolConfig struct {
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level          string        `json:"level"`
	EnableSQL      bool          `json:"enable_sql"`
	SlowQuery      bool          `json:"slow_query"`
	QueryThreshold time.Duration `json:"query_threshold"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	BatchSize     int           `json:"batch_size"`
	Concurrency   int           `json:"concurrency"`
	QueryTimeout  time.Duration `json:"query_timeout"`
	RetryAttempts int           `json:"retry_attempts"`
	RetryInterval time.Duration `json:"retry_interval"`
}

// parsePluginConfig 解析插件配置
func parsePluginConfig(config map[string]interface{}) (*PluginConfig, error) {
	pluginConfig := &PluginConfig{
		Global: GlobalConfig{
			ConnectionPool: ConnectionPoolConfig{
				MaxOpenConns:    10,
				MaxIdleConns:    5,
				ConnMaxLifetime: 1 * time.Hour,
				ConnMaxIdleTime: 10 * time.Minute,
			},
			Logging: LoggingConfig{
				Level:          "info",
				EnableSQL:      false,
				SlowQuery:      true,
				QueryThreshold: 1 * time.Second,
			},
			Performance: PerformanceConfig{
				BatchSize:     100,
				Concurrency:   5,
				QueryTimeout:  30 * time.Second,
				RetryAttempts: 3,
				RetryInterval: 1 * time.Second,
			},
		},
		Metrics: MetricsConfig{
			Enabled: true,
			Groups:  getDefaultMetricGroups(),
		},
	}

	// 解析数据库配置
	if err := parseDatabasesConfig(config, pluginConfig); err != nil {
		return nil, err
	}

	// 解析指标配置
	if err := parseMetricsConfig(config, pluginConfig); err != nil {
		return nil, err
	}

	// 解析数据查询配置
	if err := parseDataQueriesConfig(config, pluginConfig); err != nil {
		return nil, err
	}

	// 解析全局配置
	if err := parseGlobalConfig(config, pluginConfig); err != nil {
		return nil, err
	}

	return pluginConfig, nil
}

// parseDatabasesConfig 解析数据库配置
func parseDatabasesConfig(config map[string]interface{}, pluginConfig *PluginConfig) error {
	// 检查多数据库配置
	if databases, exists := config["databases"]; exists {
		dbList, ok := databases.([]interface{})
		if !ok {
			return fmt.Errorf("databases must be an array")
		}

		for i, db := range dbList {
			dbConfig, ok := db.(map[string]interface{})
			if !ok {
				return fmt.Errorf("database[%d] must be an object", i)
			}

			dbCfg, err := parseSingleDatabaseConfig(dbConfig)
			if err != nil {
				return fmt.Errorf("database[%d]: %w", i, err)
			}

			pluginConfig.Databases = append(pluginConfig.Databases, *dbCfg)
		}
	} else {
		// 向后兼容：单数据库配置
		dbCfg, err := parseSingleDatabaseConfig(config)
		if err != nil {
			return err
		}
		pluginConfig.Databases = append(pluginConfig.Databases, *dbCfg)
	}

	return nil
}

// parseSingleDatabaseConfig 解析单个数据库配置
func parseSingleDatabaseConfig(config map[string]interface{}) (*DatabaseConfig, error) {
	dbConfig := &DatabaseConfig{
		Database: "information_schema", // 默认数据库
		Charset:  "utf8mb4",            // 默认字符集
		Timeout:  30 * time.Second,     // 默认超时
		MaxConns: 5,                    // 默认最大连接数
		Options:  make(map[string]string),
		Tags:     make(map[string]string),
	}

	// 解析基本配置
	if name, ok := config["name"].(string); ok {
		dbConfig.Name = name
	}

	if host, ok := config["host"].(string); ok {
		dbConfig.Host = host
	}

	if port, ok := config["port"]; ok {
		switch v := port.(type) {
		case int:
			dbConfig.Port = v
		case float64:
			dbConfig.Port = int(v)
		case string:
			if portInt, err := strconv.Atoi(v); err == nil {
				dbConfig.Port = portInt
			}
		}
	}

	if username, ok := config["username"].(string); ok {
		dbConfig.Username = username
	}

	if password, ok := config["password"].(string); ok {
		dbConfig.Password = password
	}

	if database, ok := config["database"].(string); ok {
		dbConfig.Database = database
	}

	if charset, ok := config["charset"].(string); ok {
		dbConfig.Charset = charset
	}

	// 解析超时配置
	if timeout, ok := config["timeout"].(string); ok {
		if d, err := time.ParseDuration(timeout); err == nil {
			dbConfig.Timeout = d
		}
	}

	// 解析最大连接数
	if maxConns, ok := config["max_connections"]; ok {
		switch v := maxConns.(type) {
		case int:
			dbConfig.MaxConns = v
		case float64:
			dbConfig.MaxConns = int(v)
		}
	}

	// 解析选项
	if options, ok := config["options"].(map[string]interface{}); ok {
		for k, v := range options {
			if str, ok := v.(string); ok {
				dbConfig.Options[k] = str
			}
		}
	}

	// 解析标签
	if tags, ok := config["tags"].(map[string]interface{}); ok {
		for k, v := range tags {
			if str, ok := v.(string); ok {
				dbConfig.Tags[k] = str
			}
		}
	}

	return dbConfig, nil
}

// parseMetricsConfig 解析指标配置
func parseMetricsConfig(config map[string]interface{}, pluginConfig *PluginConfig) error {
	if metricsConfig, exists := config["metrics"]; exists {
		metricsMap, ok := metricsConfig.(map[string]interface{})
		if !ok {
			return fmt.Errorf("metrics config must be an object")
		}

		// 解析启用状态
		if enabled, ok := metricsMap["enabled"].(bool); ok {
			pluginConfig.Metrics.Enabled = enabled
		}

		// 解析指标组
		if groups, ok := metricsMap["groups"].([]interface{}); ok {
			var metricGroups []MetricGroupConfig
			for _, group := range groups {
				if groupMap, ok := group.(map[string]interface{}); ok {
					groupConfig := MetricGroupConfig{
						Enabled: true,
						Tags:    make(map[string]string),
					}

					if name, ok := groupMap["name"].(string); ok {
						groupConfig.Name = name
					}
					if enabled, ok := groupMap["enabled"].(bool); ok {
						groupConfig.Enabled = enabled
					}
					if desc, ok := groupMap["description"].(string); ok {
						groupConfig.Description = desc
					}

					// 解析指标列表
					if metrics, ok := groupMap["metrics"].([]interface{}); ok {
						for _, metric := range metrics {
							if metricStr, ok := metric.(string); ok {
								groupConfig.Metrics = append(groupConfig.Metrics, metricStr)
							}
						}
					}

					metricGroups = append(metricGroups, groupConfig)
				}
			}
			pluginConfig.Metrics.Groups = metricGroups
		}

		// 解析自定义指标
		if custom, ok := metricsMap["custom"].([]interface{}); ok {
			var customMetrics []CustomMetricConfig
			for _, customMetric := range custom {
				if customMap, ok := customMetric.(map[string]interface{}); ok {
					customConfig := CustomMetricConfig{
						Tags: make(map[string]string),
					}

					if name, ok := customMap["name"].(string); ok {
						customConfig.Name = name
					}
					if sql, ok := customMap["sql"].(string); ok {
						customConfig.SQL = sql
					}
					if metricKey, ok := customMap["metric_key"].(string); ok {
						customConfig.MetricKey = metricKey
					}
					if valueColumn, ok := customMap["value_column"].(string); ok {
						customConfig.ValueColumn = valueColumn
					}

					customMetrics = append(customMetrics, customConfig)
				}
			}
			pluginConfig.Metrics.Custom = customMetrics
		}
	}

	return nil
}

// parseDataQueriesConfig 解析数据查询配置
func parseDataQueriesConfig(config map[string]interface{}, pluginConfig *PluginConfig) error {
	if dataQueries, exists := config["data_queries"]; exists {
		queryList, ok := dataQueries.([]interface{})
		if !ok {
			return fmt.Errorf("data_queries must be an array")
		}

		for _, query := range queryList {
			if queryMap, ok := query.(map[string]interface{}); ok {
				queryConfig := DataQueryConfig{
					Timeout: 30 * time.Second,
					Tags:    make(map[string]string),
				}

				if name, ok := queryMap["name"].(string); ok {
					queryConfig.Name = name
				}
				if sql, ok := queryMap["sql"].(string); ok {
					queryConfig.SQL = sql
				}
				if outputKey, ok := queryMap["output_key"].(string); ok {
					queryConfig.OutputKey = outputKey
				}
				if crossDB, ok := queryMap["cross_db"].(bool); ok {
					queryConfig.CrossDB = crossDB
				}

				// 解析数据库列表
				if databases, ok := queryMap["databases"].([]interface{}); ok {
					for _, db := range databases {
						if dbStr, ok := db.(string); ok {
							queryConfig.Databases = append(queryConfig.Databases, dbStr)
						}
					}
				}

				pluginConfig.DataQueries = append(pluginConfig.DataQueries, queryConfig)
			}
		}
	}

	return nil
}

// parseGlobalConfig 解析全局配置
func parseGlobalConfig(config map[string]interface{}, pluginConfig *PluginConfig) error {
	if globalConfig, exists := config["global"]; exists {
		globalMap, ok := globalConfig.(map[string]interface{})
		if !ok {
			return fmt.Errorf("global config must be an object")
		}

		// 解析连接池配置
		if connPool, ok := globalMap["connection_pool"].(map[string]interface{}); ok {
			if maxOpen, ok := connPool["max_open_conns"]; ok {
				if val, ok := maxOpen.(float64); ok {
					pluginConfig.Global.ConnectionPool.MaxOpenConns = int(val)
				}
			}
			if maxIdle, ok := connPool["max_idle_conns"]; ok {
				if val, ok := maxIdle.(float64); ok {
					pluginConfig.Global.ConnectionPool.MaxIdleConns = int(val)
				}
			}
		}

		// 解析性能配置
		if performance, ok := globalMap["performance"].(map[string]interface{}); ok {
			if batchSize, ok := performance["batch_size"]; ok {
				if val, ok := batchSize.(float64); ok {
					pluginConfig.Global.Performance.BatchSize = int(val)
				}
			}
			if concurrency, ok := performance["concurrency"]; ok {
				if val, ok := concurrency.(float64); ok {
					pluginConfig.Global.Performance.Concurrency = int(val)
				}
			}
		}
	}

	return nil
}

// getDefaultMetricGroups 获取默认指标组
func getDefaultMetricGroups() []MetricGroupConfig {
	return []MetricGroupConfig{
		{
			Name:        "basic",
			Enabled:     true,
			Description: "Basic MySQL metrics",
			Metrics: []string{
				"Threads_connected",
				"Questions",
				"Uptime",
			},
		},
		{
			Name:        "innodb",
			Enabled:     false,
			Description: "InnoDB storage engine metrics",
			Metrics: []string{
				"Innodb_buffer_pool_size",
				"Innodb_buffer_pool_pages_total",
				"Innodb_buffer_pool_pages_free",
				"Innodb_buffer_pool_pages_dirty",
				"Innodb_buffer_pool_reads",
				"Innodb_buffer_pool_read_requests",
			},
		},
		{
			Name:        "performance",
			Enabled:     false,
			Description: "Performance related metrics",
			Metrics: []string{
				"Slow_queries",
				"Com_select",
				"Com_insert",
				"Com_update",
				"Com_delete",
				"Handler_read_rnd_next",
				"Handler_read_key",
			},
		},
		{
			Name:        "replication",
			Enabled:     false,
			Description: "MySQL replication metrics",
			Metrics: []string{
				"Slave_running",
				"Seconds_Behind_Master",
				"Slave_SQL_Running",
				"Slave_IO_Running",
			},
		},
	}
}
