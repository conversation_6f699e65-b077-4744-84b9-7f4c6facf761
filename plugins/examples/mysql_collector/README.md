# MySQL Collector Plugin (Enhanced)

Enhanced MySQL collector plugin for the DevInsight AIOps system with comprehensive monitoring capabilities.

## Features

- **Multi-Database Support**: Connect and monitor multiple MySQL instances
- **Extended Metric Collection**: Comprehensive MySQL metrics including InnoDB, performance, and replication metrics
- **Custom SQL Queries**: Execute custom SQL queries for specialized data collection
- **Cross-Database Queries**: Query across multiple database instances
- **Connection Pool Management**: Efficient connection pooling for multiple databases
- **Flexible Configuration**: Comprehensive configuration system with support for metric groups and custom queries
- **Modular Architecture**: Clean, maintainable code structure

## Architecture

The plugin has been refactored into multiple modules for better maintainability:

```
mysql_collector/
├── main.go              # (deprecated, to be removed)
├── plugin.go            # Main plugin implementation
├── factory.go           # Plugin factory and validation
├── config.go            # Configuration structures and parsing
├── connection.go        # Database connection management
├── metrics.go           # Metric collection logic
├── data.go              # Custom data collection
├── utils.go             # Utility functions
├── config.example.json  # Example configuration
└── README.md           # This file
```

### Components

1. **Plugin Core** (`plugin.go`): Main plugin implementation with lifecycle management
2. **Factory** (`factory.go`): Plugin factory for creation and validation
3. **Configuration** (`config.go`): Comprehensive configuration system
4. **Connection Manager** (`connection.go`): Database connection pooling and management
5. **Metric Collector** (`metrics.go`): Standard MySQL metrics collection
6. **Data Collector** (`data.go`): Custom SQL query execution
7. **Utilities** (`utils.go`): Helper functions

> **Note**: Collection scheduling is now handled by the Agent's PipelineScheduler for better resource management and centralized control.

## Configuration

### Basic Configuration (Single Database)

```json
{
  "host": "localhost",
  "port": 3306,
  "username": "monitor",
  "password": "password",
  "database": "information_schema"
}
```

### Advanced Configuration (Multi-Database)

```json
{
  "databases": [
    {
      "name": "primary",
      "host": "localhost",
      "port": 3306,
      "username": "monitor",
      "password": "password",
      "database": "information_schema",
      "tags": {
        "role": "primary",
        "environment": "production"
      }
    },
    {
      "name": "replica",
      "host": "replica.example.com",
      "port": 3306,
      "username": "monitor",
      "password": "password",
      "database": "information_schema",
      "tags": {
        "role": "replica"
      }
    }
  ],
  "metrics": {
    "enabled": true,
    "groups": [
      {
        "name": "basic",
        "enabled": true,
        "metrics": ["Threads_connected", "Questions", "Uptime"]
      },
      {
        "name": "innodb",
        "enabled": true,
        "metrics": ["Innodb_buffer_pool_size", "Innodb_buffer_pool_pages_total"]
      }
    ]
  }
}
```

## Metric Groups

### Built-in Metric Groups

1. **Basic Metrics** (`basic`):
   - `Threads_connected`: Current number of connections
   - `Questions`: Total number of queries
   - `Uptime`: Server uptime in seconds
   - `Slow_queries`: Number of slow queries

2. **InnoDB Metrics** (`innodb`):
   - `Innodb_buffer_pool_size`: Buffer pool size
   - `Innodb_buffer_pool_pages_total`: Total buffer pool pages
   - `Innodb_buffer_pool_pages_free`: Free buffer pool pages
   - `Innodb_buffer_pool_reads`: Buffer pool reads

3. **Performance Metrics** (`performance`):
   - `Com_select`: SELECT statements executed
   - `Com_insert`: INSERT statements executed
   - `Com_update`: UPDATE statements executed
   - `Com_delete`: DELETE statements executed

4. **Replication Metrics** (`replication`):
   - `Slave_running`: Slave status
   - `Seconds_Behind_Master`: Replication lag
   - `Slave_SQL_Running`: SQL thread status
   - `Slave_IO_Running`: IO thread status

## Custom Metrics

Add custom metrics with SQL queries:

```json
{
  "metrics": {
    "custom": [
      {
        "name": "table_sizes",
        "sql": "SELECT table_schema, table_name, ROUND((data_length + index_length) / 1024 / 1024) AS size_mb FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys') ORDER BY size_mb DESC LIMIT 10",
        "metric_key": "mysql.table.size_mb",
        "value_column": "size_mb",
        "label_columns": ["table_schema", "table_name"]
      }
    ]
  }
}
```

## Data Queries

Execute custom SQL queries for specialized data collection:

```json
{
  "data_queries": [
    {
      "name": "slow_queries_analysis",
      "sql": "SELECT db, user, host, time, state, info FROM information_schema.processlist WHERE time > 10",
      "databases": ["primary"],
      "timeout": "10s"
    }
  ]
}
```

## Usage

### Plugin Registration

The plugin is automatically registered through the factory pattern:

```go
func NewPluginFactory() pipeline.PluginFactory {
    return &MySQLPluginFactory{}
}
```

### Configuration Examples

See `config.example.json` for comprehensive configuration examples.

### Collected Metrics

Metrics are collected in the following format:

```
mysql.{group}.{metric_name}
```

Examples:
- `mysql.basic.Threads_connected`
- `mysql.innodb.Innodb_buffer_pool_size`
- `mysql.performance.Com_select`

### Labels

Each metric includes labels for filtering and grouping:
- `database`: Database connection name
- `group`: Metric group name
- `type`: Metric type
- Custom labels from configuration

## Development

### Building

```bash
go build -buildmode=plugin -o mysql_collector.so .
```

### Testing

```bash
go test ./...
```

### Adding New Metric Groups

1. Define metrics in the configuration
2. Add to `getDefaultMetricGroups()` in `config.go`
3. Update documentation

### Adding New Collectors

1. Implement collection logic in `metrics.go` or `data.go`
2. Add configuration structure in `config.go`
3. Update factory validation in `factory.go`

## Migration from v1.0

The enhanced plugin maintains backward compatibility with the original configuration format. Single database configurations are automatically converted to the new multi-database format.

### Breaking Changes

- None - fully backward compatible

### New Features

- Multi-database support
- Extended metric groups
- Custom SQL queries
- Connection pooling
- Cross-database queries

## Performance Considerations

- Connection pooling reduces connection overhead
- Configurable query timeouts prevent hanging
- Batch processing for large result sets
- Concurrent collection for multiple databases

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check database credentials and network connectivity
2. **Query Timeout**: Increase timeout in configuration
3. **Missing Metrics**: Verify metric groups are enabled
4. **High Memory Usage**: Reduce batch size or concurrency

### Logging

Enable SQL logging for debugging:

```json
{
  "global": {
    "logging": {
      "level": "debug",
      "enable_sql": true
    }
  }
}
```

## License

Copyright DevInsight Team. All rights reserved.
