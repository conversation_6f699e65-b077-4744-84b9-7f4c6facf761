package main

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// ConnectionManager 连接管理器
type ConnectionManager struct {
	databases   []DatabaseConfig
	connections map[string]*sql.DB
	poolConfig  ConnectionPoolConfig
	mutex       sync.RWMutex
	running     bool
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(databases []DatabaseConfig) *ConnectionManager {
	return &ConnectionManager{
		databases:   databases,
		connections: make(map[string]*sql.DB),
		poolConfig: ConnectionPoolConfig{
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: 1 * time.Hour,
			ConnMaxIdleTime: 10 * time.Minute,
		},
	}
}

// Start 启动连接管理器
func (cm *ConnectionManager) Start(ctx context.Context) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	for _, dbConfig := range cm.databases {
		if err := cm.createConnection(ctx, dbConfig); err != nil {
			// 清理已创建的连接
			cm.cleanup()
			return fmt.Errorf("failed to create connection for %s: %w", dbConfig.Name, err)
		}
	}

	cm.running = true
	return nil
}

// Stop 停止连接管理器
func (cm *ConnectionManager) Stop() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.running = false
	cm.cleanup()
	return nil
}

// cleanup 清理所有连接
func (cm *ConnectionManager) cleanup() {
	for name, db := range cm.connections {
		if db != nil {
			db.Close()
		}
		delete(cm.connections, name)
	}
}

// createConnection 创建数据库连接
func (cm *ConnectionManager) createConnection(ctx context.Context, dbConfig DatabaseConfig) error {
	dsn := cm.buildDSN(dbConfig)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open connection: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(cm.poolConfig.MaxOpenConns)
	db.SetMaxIdleConns(cm.poolConfig.MaxIdleConns)
	db.SetConnMaxLifetime(cm.poolConfig.ConnMaxLifetime)
	db.SetConnMaxIdleTime(cm.poolConfig.ConnMaxIdleTime)

	// 测试连接
	testCtx, cancel := context.WithTimeout(ctx, dbConfig.Timeout)
	defer cancel()

	if err := db.PingContext(testCtx); err != nil {
		db.Close()
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// 存储连接
	connectionKey := cm.getConnectionKey(dbConfig)
	cm.connections[connectionKey] = db

	return nil
}

// buildDSN 构建数据源名称
func (cm *ConnectionManager) buildDSN(dbConfig DatabaseConfig) string {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database)

	// 添加字符集和其他选项
	params := make(map[string]string)
	params["charset"] = dbConfig.Charset
	params["parseTime"] = "true"
	params["loc"] = "Local"

	// 添加自定义选项
	for k, v := range dbConfig.Options {
		params[k] = v
	}

	// 构建参数字符串
	if len(params) > 0 {
		dsn += "?"
		first := true
		for k, v := range params {
			if !first {
				dsn += "&"
			}
			dsn += fmt.Sprintf("%s=%s", k, v)
			first = false
		}
	}

	return dsn
}

// getConnectionKey 获取连接键
func (cm *ConnectionManager) getConnectionKey(dbConfig DatabaseConfig) string {
	if dbConfig.Name != "" {
		return dbConfig.Name
	}
	return fmt.Sprintf("%s:%d/%s", dbConfig.Host, dbConfig.Port, dbConfig.Database)
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(name string) (*sql.DB, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if !cm.running {
		return nil, fmt.Errorf("connection manager not running")
	}

	if name == "" && len(cm.connections) > 0 {
		// 返回第一个连接（默认连接）
		for _, db := range cm.connections {
			return db, nil
		}
	}

	db, exists := cm.connections[name]
	if !exists {
		return nil, fmt.Errorf("connection not found: %s", name)
	}

	return db, nil
}

// GetAllConnections 获取所有连接
func (cm *ConnectionManager) GetAllConnections() map[string]*sql.DB {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	connections := make(map[string]*sql.DB)
	for name, db := range cm.connections {
		connections[name] = db
	}

	return connections
}

// GetDatabasesInfo 获取数据库信息
func (cm *ConnectionManager) GetDatabasesInfo() []map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	var infos []map[string]interface{}
	for _, dbConfig := range cm.databases {
		info := map[string]interface{}{
			"name":     dbConfig.Name,
			"host":     dbConfig.Host,
			"port":     dbConfig.Port,
			"database": dbConfig.Database,
			"tags":     dbConfig.Tags,
		}
		infos = append(infos, info)
	}

	return infos
}

// GetPrimaryDeviceID 获取主要设备ID
func (cm *ConnectionManager) GetPrimaryDeviceID() string {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if len(cm.databases) > 0 {
		primary := cm.databases[0]
		return fmt.Sprintf("%s:%d", primary.Host, primary.Port)
	}

	return "unknown"
}

// Health 健康检查
func (cm *ConnectionManager) Health() error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if !cm.running {
		return fmt.Errorf("connection manager not running")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var errors []error
	for name, db := range cm.connections {
		if err := db.PingContext(ctx); err != nil {
			errors = append(errors, fmt.Errorf("connection %s: %w", name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("health check failed: %v", errors)
	}

	return nil
}

// GetConnectionCount 获取连接数量
func (cm *ConnectionManager) GetConnectionCount() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return len(cm.connections)
}

// ReconnectDatabase 重连数据库
func (cm *ConnectionManager) ReconnectDatabase(name string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 找到数据库配置
	var dbConfig *DatabaseConfig
	for _, config := range cm.databases {
		if cm.getConnectionKey(config) == name {
			dbConfig = &config
			break
		}
	}

	if dbConfig == nil {
		return fmt.Errorf("database config not found: %s", name)
	}

	// 关闭现有连接
	if db, exists := cm.connections[name]; exists {
		db.Close()
		delete(cm.connections, name)
	}

	// 重新创建连接
	ctx, cancel := context.WithTimeout(context.Background(), dbConfig.Timeout)
	defer cancel()

	return cm.createConnection(ctx, *dbConfig)
}

// GetDatabaseConfig 获取数据库配置
func (cm *ConnectionManager) GetDatabaseConfig(name string) (*DatabaseConfig, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	for _, config := range cm.databases {
		if cm.getConnectionKey(config) == name {
			return &config, nil
		}
	}

	return nil, fmt.Errorf("database config not found: %s", name)
}

// SetPoolConfig 设置连接池配置
func (cm *ConnectionManager) SetPoolConfig(config ConnectionPoolConfig) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.poolConfig = config

	// 更新现有连接的池配置
	for _, db := range cm.connections {
		db.SetMaxOpenConns(config.MaxOpenConns)
		db.SetMaxIdleConns(config.MaxIdleConns)
		db.SetConnMaxLifetime(config.ConnMaxLifetime)
		db.SetConnMaxIdleTime(config.ConnMaxIdleTime)
	}
}
