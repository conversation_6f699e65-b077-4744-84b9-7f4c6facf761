package main

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	pb "aiops/pkg/proto"
)

// MetricCollector 指标采集器
type MetricCollector struct {
	config MetricsConfig
}

// NewMetricCollector 创建指标采集器
func NewMetricCollector(config MetricsConfig) *MetricCollector {
	return &MetricCollector{
		config: config,
	}
}

// CollectMetrics 采集指标
func (mc *MetricCollector) CollectMetrics(ctx context.Context, cm *ConnectionManager) ([]*pb.MetricData, error) {
	if !mc.config.Enabled {
		return nil, nil
	}

	var allMetrics []*pb.MetricData

	// 为每个数据库采集指标
	connections := cm.GetAllConnections()
	for name, db := range connections {
		dbMetrics, err := mc.collectDatabaseMetrics(ctx, db, name, cm)
		if err != nil {
			return nil, fmt.Errorf("failed to collect metrics for %s: %w", name, err)
		}
		allMetrics = append(allMetrics, dbMetrics...)
	}

	// 采集自定义指标
	customMetrics, err := mc.collectCustomMetrics(ctx, cm)
	if err != nil {
		return nil, fmt.Errorf("failed to collect custom metrics: %w", err)
	}
	allMetrics = append(allMetrics, customMetrics...)

	return allMetrics, nil
}

// collectDatabaseMetrics 采集数据库指标
func (mc *MetricCollector) collectDatabaseMetrics(ctx context.Context, db *sql.DB, dbName string, cm *ConnectionManager) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	// 获取数据库配置
	dbConfig, err := cm.GetDatabaseConfig(dbName)
	if err != nil {
		return nil, err
	}

	deviceID := fmt.Sprintf("%s:%d", dbConfig.Host, dbConfig.Port)

	// 为每个启用的指标组采集数据
	for _, group := range mc.config.Groups {
		if !group.Enabled {
			continue
		}

		groupMetrics, err := mc.collectMetricGroup(ctx, db, group, deviceID, dbName)
		if err != nil {
			return nil, fmt.Errorf("failed to collect metric group %s: %w", group.Name, err)
		}
		metrics = append(metrics, groupMetrics...)
	}

	return metrics, nil
}

// collectMetricGroup 采集指标组
func (mc *MetricCollector) collectMetricGroup(ctx context.Context, db *sql.DB, group MetricGroupConfig, deviceID, dbName string) ([]*pb.MetricData, error) {
	if len(group.Metrics) == 0 {
		return nil, nil
	}

	// 构建查询语句
	placeholders := ""
	args := make([]interface{}, len(group.Metrics))
	for i, metric := range group.Metrics {
		if i > 0 {
			placeholders += ","
		}
		placeholders += "?"
		args[i] = metric
	}

	query := fmt.Sprintf("SHOW STATUS WHERE Variable_name IN (%s)", placeholders)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query status: %w", err)
	}
	defer rows.Close()

	var metrics []*pb.MetricData
	now := time.Now().Unix()

	for rows.Next() {
		var name, value string
		if err := rows.Scan(&name, &value); err != nil {
			continue
		}

		// 转换值
		floatValue, err := strconv.ParseFloat(value, 64)
		if err != nil {
			continue
		}

		// 生成指标键
		metricKey := mc.generateMetricKey(group.Name, name)

		// 创建标签
		labels := make(map[string]string)
		for k, v := range group.Tags {
			labels[k] = v
		}
		for k, v := range mc.config.Global.Tags {
			labels[k] = v
		}
		labels["group"] = group.Name
		labels["database"] = dbName
		labels["metric"] = name

		metric := &pb.MetricData{
			MetricKey: metricKey,
			ValueType: &pb.MetricData_NumericValue{
				NumericValue: floatValue,
			},
			Timestamp: now,
			DeviceId:  deviceID,
			Labels:    labels,
		}

		metrics = append(metrics, metric)
	}

	return metrics, nil
}

// collectCustomMetrics 采集自定义指标
func (mc *MetricCollector) collectCustomMetrics(ctx context.Context, cm *ConnectionManager) ([]*pb.MetricData, error) {
	var allMetrics []*pb.MetricData

	for _, customMetric := range mc.config.Custom {
		metrics, err := mc.collectSingleCustomMetric(ctx, cm, customMetric)
		if err != nil {
			return nil, fmt.Errorf("failed to collect custom metric %s: %w", customMetric.Name, err)
		}
		allMetrics = append(allMetrics, metrics...)
	}

	return allMetrics, nil
}

// collectSingleCustomMetric 采集单个自定义指标
func (mc *MetricCollector) collectSingleCustomMetric(ctx context.Context, cm *ConnectionManager, config CustomMetricConfig) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	// 确定要执行的数据库
	var targetDatabases []string
	if len(config.Databases) > 0 {
		targetDatabases = config.Databases
	} else {
		// 所有数据库
		connections := cm.GetAllConnections()
		for name := range connections {
			targetDatabases = append(targetDatabases, name)
		}
	}

	// 在每个目标数据库执行查询
	for _, dbName := range targetDatabases {
		db, err := cm.GetConnection(dbName)
		if err != nil {
			continue
		}
		dbMetrics, err := mc.executeCustomMetricQuery(ctx, db, dbName, config, cm)
		if err != nil {
			return nil, err
		}
		metrics = append(metrics, dbMetrics...)
	}

	return metrics, nil
}

// executeCustomMetricQuery 执行自定义指标查询
func (mc *MetricCollector) executeCustomMetricQuery(ctx context.Context, db *sql.DB, dbName string, config CustomMetricConfig, cm *ConnectionManager) ([]*pb.MetricData, error) {
	rows, err := db.QueryContext(ctx, config.SQL)
	if err != nil {
		return nil, fmt.Errorf("failed to execute custom metric query: %w", err)
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	// 找到值列索引
	valueColumnIndex := -1
	for i, col := range columns {
		if col == config.ValueColumn {
			valueColumnIndex = i
			break
		}
	}
	if valueColumnIndex == -1 {
		return nil, fmt.Errorf("value column %s not found", config.ValueColumn)
	}

	// 找到标签列索引
	labelColumnIndexes := make(map[string]int)
	for _, labelCol := range config.LabelColumns {
		for i, col := range columns {
			if col == labelCol {
				labelColumnIndexes[labelCol] = i
				break
			}
		}
	}

	var metrics []*pb.MetricData
	now := time.Now().Unix()

	// 获取数据库配置用于设备ID
	dbConfig, err := cm.GetDatabaseConfig(dbName)
	if err != nil {
		return nil, err
	}
	deviceID := fmt.Sprintf("%s:%d", dbConfig.Host, dbConfig.Port)

	for rows.Next() {
		// 扫描所有列
		values := make([]interface{}, len(columns))
		scanArgs := make([]interface{}, len(columns))
		for i := range values {
			scanArgs[i] = &values[i]
		}

		if err := rows.Scan(scanArgs...); err != nil {
			continue
		}

		// 获取值
		var metricValue float64
		if val := values[valueColumnIndex]; val != nil {
			switch v := val.(type) {
			case int64:
				metricValue = float64(v)
			case float64:
				metricValue = v
			case string:
				if parsed, err := strconv.ParseFloat(v, 64); err == nil {
					metricValue = parsed
				} else {
					continue
				}
			case []byte:
				if parsed, err := strconv.ParseFloat(string(v), 64); err == nil {
					metricValue = parsed
				} else {
					continue
				}
			default:
				continue
			}
		} else {
			continue
		}

		// 构建标签
		labels := make(map[string]string)
		for k, v := range config.Tags {
			labels[k] = v
		}
		for k, v := range mc.config.Global.Tags {
			labels[k] = v
		}
		labels["database"] = dbName
		labels["custom_metric"] = config.Name

		// 添加标签列的值
		for labelCol, index := range labelColumnIndexes {
			if val := values[index]; val != nil {
				labels[labelCol] = fmt.Sprintf("%v", val)
			}
		}

		// 生成指标键
		metricKey := config.MetricKey
		if metricKey == "" {
			metricKey = mc.generateMetricKey("custom", config.Name)
		}

		metric := &pb.MetricData{
			MetricKey: metricKey,
			ValueType: &pb.MetricData_NumericValue{
				NumericValue: metricValue,
			},
			Timestamp: now,
			DeviceId:  deviceID,
			Labels:    labels,
		}

		metrics = append(metrics, metric)
	}

	return metrics, nil
}

// generateMetricKey 生成指标键
func (mc *MetricCollector) generateMetricKey(group, name string) string {
	prefix := mc.config.Global.Prefix
	if prefix == "" {
		prefix = "mysql"
	}

	return fmt.Sprintf("%s.%s.%s", prefix, group, name)
}

// GetEnabledGroups 获取启用的指标组
func (mc *MetricCollector) GetEnabledGroups() []string {
	var groups []string
	for _, group := range mc.config.Groups {
		if group.Enabled {
			groups = append(groups, group.Name)
		}
	}
	return groups
}

// EnableGroup 启用指标组
func (mc *MetricCollector) EnableGroup(groupName string) error {
	for i := range mc.config.Groups {
		if mc.config.Groups[i].Name == groupName {
			mc.config.Groups[i].Enabled = true
			return nil
		}
	}
	return fmt.Errorf("metric group not found: %s", groupName)
}

// DisableGroup 禁用指标组
func (mc *MetricCollector) DisableGroup(groupName string) error {
	for i := range mc.config.Groups {
		if mc.config.Groups[i].Name == groupName {
			mc.config.Groups[i].Enabled = false
			return nil
		}
	}
	return fmt.Errorf("metric group not found: %s", groupName)
}

// AddCustomMetric 添加自定义指标
func (mc *MetricCollector) AddCustomMetric(config CustomMetricConfig) {
	mc.config.Custom = append(mc.config.Custom, config)
}

// RemoveCustomMetric 移除自定义指标
func (mc *MetricCollector) RemoveCustomMetric(name string) error {
	for i, metric := range mc.config.Custom {
		if metric.Name == name {
			mc.config.Custom = append(mc.config.Custom[:i], mc.config.Custom[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("custom metric not found: %s", name)
}
