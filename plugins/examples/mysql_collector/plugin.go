package main

import (
	"context"
	"fmt"
	"time"

	"aiops/pkg/pipeline"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLCollectorPlugin MySQL 采集器插件
type MySQLCollectorPlugin struct {
	name    string
	version string
	config  map[string]interface{}
	running bool

	// 统计信息
	collectCount int64
	errorCount   int64
	lastError    error
	lastCollect  time.Time

	// 组件
	connectionManager *ConnectionManager
	metricCollector   *MetricCollector
	dataCollector     *DataCollector
}

// Initialize 初始化插件
func (p *MySQLCollectorPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析配置
	pluginConfig, err := parsePluginConfig(config)
	if err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// 初始化连接管理器
	p.connectionManager = NewConnectionManager(pluginConfig.Databases)

	// 初始化指标采集器
	p.metricCollector = NewMetricCollector(pluginConfig.Metrics)

	// 初始化数据采集器
	p.dataCollector = NewDataCollector(pluginConfig.DataQueries)

	return nil
}

// Start 启动插件
func (p *MySQLCollectorPlugin) Start(ctx context.Context) error {
	// 启动连接管理器
	if err := p.connectionManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start connection manager: %w", err)
	}

	p.running = true
	return nil
}

// Stop 停止插件
func (p *MySQLCollectorPlugin) Stop() error {
	p.running = false

	// 停止连接管理器
	if p.connectionManager != nil {
		if err := p.connectionManager.Stop(); err != nil {
			p.lastError = fmt.Errorf("failed to stop connection manager: %w", err)
			return p.lastError
		}
	}

	return nil
}

// Health 健康检查
func (p *MySQLCollectorPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}

	return p.connectionManager.Health()
}

// Process 处理数据（采集器不需要实现）
func (p *MySQLCollectorPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	return data, nil
}

// GetName 获取插件名称
func (p *MySQLCollectorPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *MySQLCollectorPlugin) GetType() pipeline.PluginType {
	return pipeline.CollectorType
}

// GetVersion 获取插件版本
func (p *MySQLCollectorPlugin) GetVersion() string {
	return p.version
}

// GetConfig 获取配置
func (p *MySQLCollectorPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新配置
func (p *MySQLCollectorPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.Initialize(config)
}

// GetInputSchema 获取输入模式
func (p *MySQLCollectorPlugin) GetInputSchema() *pipeline.Schema {
	return nil // 采集器没有输入
}

// GetOutputSchema 获取输出模式
func (p *MySQLCollectorPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"connections": {
				Type:        "int",
				Description: "当前连接数",
				Required:    true,
			},
			"queries": {
				Type:        "int",
				Description: "查询总数",
				Required:    true,
			},
			"uptime": {
				Type:        "int",
				Description: "服务器运行时间（秒）",
				Required:    true,
			},
		},
		Description: "MySQL数据库监控数据",
		Version:     "1.0",
	}
}

// GetMetrics 获取插件指标
func (p *MySQLCollectorPlugin) GetMetrics() *pipeline.PluginMetrics {
	status := "stopped"
	if p.running {
		status = "running"
	}

	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.collectCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0,
		LastProcessTime: p.lastCollect,
		Status:          status,
		CustomMetrics: map[string]any{
			"last_error": p.getLastErrorString(),
		},
	}
}

// getLastErrorString 获取最后错误的字符串表示
func (p *MySQLCollectorPlugin) getLastErrorString() string {
	if p.lastError != nil {
		return p.lastError.Error()
	}
	return ""
}

// Collect 采集数据
func (p *MySQLCollectorPlugin) Collect(ctx context.Context) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.collectCount++
		p.lastCollect = time.Now()
	}()

	// 采集指标数据
	metrics, err := p.metricCollector.CollectMetrics(ctx, p.connectionManager)
	if err != nil {
		p.errorCount++
		p.lastError = err
		return nil, err
	}

	// 采集自定义数据
	customData, err := p.dataCollector.CollectData(ctx, p.connectionManager)
	if err != nil {
		p.errorCount++
		p.lastError = err
		return nil, err
	}

	// 合并数据
	allMetrics := append(metrics, customData...)

	// 创建流水线数据
	data := &pipeline.PipelineData{
		ID:        generateDataID(),
		Type:      pipeline.MetricDataType,
		Source:    p.name,
		DeviceID:  p.connectionManager.GetPrimaryDeviceID(),
		Timestamp: time.Now(),
		Metadata: map[string]any{
			"collector": p.name,
			"version":   p.version,
		},
		Context: map[string]any{
			"databases": p.connectionManager.GetDatabasesInfo(),
		},
		Tags: map[string]string{
			"type":   "mysql",
			"source": "database",
		},
		Metrics: allMetrics,
	}

	// 清除上次错误
	p.lastError = nil
	return data, nil
}
