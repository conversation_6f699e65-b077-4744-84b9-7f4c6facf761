{"databases": [{"name": "main_db", "host": "localhost", "port": 3306, "username": "monitor", "password": "password", "database": "performance_schema"}], "metrics": {"enabled": true, "groups": [{"name": "custom_fields", "enabled": true, "description": "自定义字段采集示例", "metrics": ["Threads_connected", "Questions", "Uptime", "Com_select", "Com_insert"]}], "custom": [{"name": "table_statistics", "description": "表统计信息自定义采集", "query": "SELECT table_schema, table_name, table_rows, data_length, index_length, (data_length + index_length) as total_size FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')", "fields": [{"name": "table_schema", "type": "string", "description": "数据库名"}, {"name": "table_name", "type": "string", "description": "表名"}, {"name": "table_rows", "type": "integer", "description": "表行数"}, {"name": "data_length", "type": "integer", "description": "数据大小(字节)"}, {"name": "index_length", "type": "integer", "description": "索引大小(字节)"}, {"name": "total_size", "type": "integer", "description": "总大小(字节)"}], "tags": {"metric_type": "table_stats"}}, {"name": "connection_analysis", "description": "连接分析自定义采集", "query": "SELECT user, host, db, command, time, state, info FROM information_schema.processlist WHERE command != 'Sleep'", "fields": [{"name": "user", "type": "string", "description": "用户名"}, {"name": "host", "type": "string", "description": "客户端主机"}, {"name": "db", "type": "string", "description": "当前数据库"}, {"name": "command", "type": "string", "description": "执行的命令"}, {"name": "time", "type": "integer", "description": "执行时间(秒)"}, {"name": "state", "type": "string", "description": "当前状态"}, {"name": "info", "type": "string", "description": "执行的SQL语句"}]}, {"name": "slow_query_details", "description": "慢查询详细信息", "query": "SELECT start_time, user_host, query_time, lock_time, rows_sent, rows_examined, db, last_insert_id, insert_id, server_id, sql_text FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE) ORDER BY query_time DESC LIMIT 50", "fields": [{"name": "start_time", "type": "datetime", "description": "查询开始时间"}, {"name": "user_host", "type": "string", "description": "用户和主机"}, {"name": "query_time", "type": "float", "description": "查询执行时间"}, {"name": "lock_time", "type": "float", "description": "锁等待时间"}, {"name": "rows_sent", "type": "integer", "description": "返回行数"}, {"name": "rows_examined", "type": "integer", "description": "扫描行数"}, {"name": "db", "type": "string", "description": "数据库名"}, {"name": "sql_text", "type": "text", "description": "SQL语句文本"}], "condition": "query_time > 1.0"}]}, "data_queries": [{"name": "custom_performance_metrics", "description": "自定义性能指标采集", "query": "SELECT variable_name, variable_value FROM information_schema.global_status WHERE variable_name IN (?, ?, ?, ?, ?)", "params": ["Innodb_buffer_pool_hit_rate", "Innodb_buffer_pool_pages_dirty", "Innodb_log_waits", "Qcache_hit_rate", "Table_locks_waited"], "fields": [{"name": "variable_name", "type": "string", "description": "变量名"}, {"name": "variable_value", "type": "string", "description": "变量值"}]}, {"name": "database_size_monitoring", "description": "数据库大小监控", "query": "SELECT table_schema as database_name, ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb, COUNT(*) as table_count FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys') GROUP BY table_schema", "fields": [{"name": "database_name", "type": "string", "description": "数据库名称"}, {"name": "size_mb", "type": "float", "description": "数据库大小(MB)"}, {"name": "table_count", "type": "integer", "description": "表数量"}]}], "field_mapping": {"custom_prefixes": {"table_stats": "mysql.table", "connection": "mysql.connection", "slow_query": "mysql.slow_query", "performance": "mysql.performance", "database": "mysql.database"}, "type_conversion": {"string": "tag", "integer": "field", "float": "field", "datetime": "timestamp", "text": "field"}}}