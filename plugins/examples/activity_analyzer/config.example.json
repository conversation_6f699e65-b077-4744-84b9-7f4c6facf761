{"max_history_size": 1000, "analysis": {"algorithm": "statistical_anomaly", "sensitivity": 0.05, "training_window": "168h", "update_interval": "1h", "features": ["time_to_start", "participant_count"], "thresholds": {"large_activity_threshold": 10000, "immediate_start_threshold": 300, "z_score_threshold": 2.0, "iqr_multiplier": 1.5}, "parameters": {"enable_z_score": true, "enable_iqr": true, "enable_pattern_match": true}, "enable_learning": true}}