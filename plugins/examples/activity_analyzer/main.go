package main

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"aiops/pkg/pipeline"
)

// ActivityAnalysisPlugin 活动分析插件
type ActivityAnalysisPlugin struct {
	name    string
	version string
	config  map[string]interface{}
	running bool

	// 分析配置
	analysisConfig *pipeline.AnalysisConfig

	// 统计信息
	processedCount int64
	anomalyCount   int64
	errorCount     int64
	lastError      error
	lastProcess    time.Time

	// 历史数据缓存 (用于统计分析)
	activityHistory []ActivityRecord
	maxHistorySize  int
}

// ActivityRecord 活动记录
type ActivityRecord struct {
	ActivityID           string
	CreatedTime          time.Time
	StartTime            time.Time
	ExpectedParticipants int64
	TimeToStart          time.Duration // 创建到开始的时间间隔
	Timestamp            time.Time
}

// ActivityAnalysisPluginFactory 活动分析插件工厂
type ActivityAnalysisPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &ActivityAnalysisPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *ActivityAnalysisPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.AnalyzerType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &ActivityAnalysisPlugin{
		name:            "activity_analyzer",
		version:         "1.0.0",
		config:          config,
		maxHistorySize:  1000, // 默认保留1000条历史记录
		activityHistory: make([]ActivityRecord, 0),
		analysisConfig: &pipeline.AnalysisConfig{
			Algorithm:      "statistical_anomaly",
			Sensitivity:    0.05,               // 默认5%显著性水平
			TrainingWindow: 7 * 24 * time.Hour, // 7天训练窗口
			UpdateInterval: 1 * time.Hour,      // 1小时更新间隔
			Features:       []string{"time_to_start", "participant_count"},
			Thresholds: map[string]float64{
				"large_activity_threshold":  10000, // 大型活动阈值
				"immediate_start_threshold": 300,   // 立即开始阈值(秒)
				"z_score_threshold":         2.0,   // Z-Score阈值
				"iqr_multiplier":            1.5,   // IQR倍数
			},
			Parameters: map[string]interface{}{
				"enable_z_score":       true,
				"enable_iqr":           true,
				"enable_pattern_match": true,
			},
			EnableLearning: true,
		},
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *ActivityAnalysisPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.AnalyzerType}
}

// 错误定义
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
	ErrInvalidConfig         = fmt.Errorf("invalid configuration")
	ErrNoBusinessData        = fmt.Errorf("no business data found")
	ErrInvalidActivityData   = fmt.Errorf("invalid activity data")
)

// GetName 获取插件名称
func (p *ActivityAnalysisPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *ActivityAnalysisPlugin) GetType() pipeline.PluginType {
	return pipeline.AnalyzerType
}

// GetVersion 获取插件版本
func (p *ActivityAnalysisPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *ActivityAnalysisPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析配置
	if err := p.parseConfig(config); err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	return nil
}

// parseConfig 解析配置
func (p *ActivityAnalysisPlugin) parseConfig(config map[string]interface{}) error {
	// 解析历史记录大小
	if maxSize, ok := config["max_history_size"]; ok {
		if size, ok := maxSize.(float64); ok {
			p.maxHistorySize = int(size)
		}
	}

	// 解析分析配置
	if analysisConfig, ok := config["analysis"]; ok {
		if configMap, ok := analysisConfig.(map[string]interface{}); ok {
			if err := p.updateAnalysisConfigFromMap(configMap); err != nil {
				return err
			}
		}
	}

	return nil
}

// updateAnalysisConfigFromMap 从map更新分析配置
func (p *ActivityAnalysisPlugin) updateAnalysisConfigFromMap(configMap map[string]interface{}) error {
	if algorithm, ok := configMap["algorithm"]; ok {
		if alg, ok := algorithm.(string); ok {
			p.analysisConfig.Algorithm = alg
		}
	}

	if sensitivity, ok := configMap["sensitivity"]; ok {
		if sens, ok := sensitivity.(float64); ok {
			p.analysisConfig.Sensitivity = sens
		}
	}

	if thresholds, ok := configMap["thresholds"]; ok {
		if threshMap, ok := thresholds.(map[string]interface{}); ok {
			for key, value := range threshMap {
				if val, ok := value.(float64); ok {
					p.analysisConfig.Thresholds[key] = val
				}
			}
		}
	}

	if parameters, ok := configMap["parameters"]; ok {
		if paramMap, ok := parameters.(map[string]interface{}); ok {
			for key, value := range paramMap {
				p.analysisConfig.Parameters[key] = value
			}
		}
	}

	return nil
}

// Start 启动插件
func (p *ActivityAnalysisPlugin) Start(ctx context.Context) error {
	p.running = true
	return nil
}

// Stop 停止插件
func (p *ActivityAnalysisPlugin) Stop() error {
	p.running = false
	return nil
}

// Health 健康检查
func (p *ActivityAnalysisPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}
	return nil
}

// Process 处理数据
func (p *ActivityAnalysisPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.processedCount++
		p.lastProcess = time.Now()
	}()

	// 检查是否有业务数据
	if data.BusinessData == nil || len(data.BusinessData) == 0 {
		return data, nil // 没有业务数据，直接返回
	}

	// 分析业务数据
	analysisResult, err := p.Analyze(ctx, data)
	if err != nil {
		p.errorCount++
		p.lastError = err
		return data, fmt.Errorf("analysis failed: %w", err)
	}

	// 将分析结果添加到数据中
	if analysisResult != nil {
		// 如果发现异常，添加到异常列表
		if len(analysisResult.Anomalies) > 0 {
			if data.Anomalies == nil {
				data.Anomalies = make([]*pipeline.Anomaly, 0)
			}
			data.Anomalies = append(data.Anomalies, analysisResult.Anomalies...)
			p.anomalyCount += int64(len(analysisResult.Anomalies))
		}

		// 如果生成告警，添加到告警列表
		if len(analysisResult.Alerts) > 0 {
			if data.Alerts == nil {
				data.Alerts = make([]*pipeline.Alert, 0)
			}
			data.Alerts = append(data.Alerts, analysisResult.Alerts...)
		}

		// 添加分析元数据
		if data.Metadata == nil {
			data.Metadata = make(map[string]interface{})
		}
		data.Metadata["activity_analysis"] = map[string]interface{}{
			"plugin_name":   p.name,
			"analysis_time": time.Now(),
			"anomaly_count": len(analysisResult.Anomalies),
			"alert_count":   len(analysisResult.Alerts),
			"confidence":    analysisResult.Confidence,
			"analysis_type": analysisResult.Type,
		}
	}

	// 更新已处理插件列表
	if data.ProcessedBy == nil {
		data.ProcessedBy = make([]string, 0)
	}
	data.ProcessedBy = append(data.ProcessedBy, p.name)

	return data, nil
}

// Analyze 分析数据
func (p *ActivityAnalysisPlugin) Analyze(ctx context.Context, data *pipeline.PipelineData) (*pipeline.AnalysisResult, error) {
	result := &pipeline.AnalysisResult{
		ID:          fmt.Sprintf("activity_analysis_%d", time.Now().UnixNano()),
		Timestamp:   time.Now(),
		Type:        "activity_anomaly_detection",
		Confidence:  0.0,
		Severity:    "info",
		Description: "Activity analysis completed",
		Anomalies:   make([]*pipeline.Anomaly, 0),
		Alerts:      make([]*pipeline.Alert, 0),
		Metadata:    make(map[string]interface{}),
		DeviceID:    data.DeviceID,
		PluginName:  p.name,
	}

	// 分析每个业务实体
	for _, entity := range data.BusinessData {
		if entity.Type == string(pipeline.ActivityEntityType) {
			if err := p.analyzeActivityEntity(entity, result); err != nil {
				return nil, fmt.Errorf("failed to analyze activity entity: %w", err)
			}
		}
	}

	// 计算整体置信度
	if len(result.Anomalies) > 0 {
		totalConfidence := 0.0
		for _, anomaly := range result.Anomalies {
			totalConfidence += anomaly.Score
		}
		result.Confidence = totalConfidence / float64(len(result.Anomalies))

		if result.Confidence > 0.8 {
			result.Severity = "critical"
		} else if result.Confidence > 0.6 {
			result.Severity = "warning"
		} else {
			result.Severity = "info"
		}
	}

	return result, nil
}

// analyzeActivityEntity 分析活动实体
func (p *ActivityAnalysisPlugin) analyzeActivityEntity(entity *pipeline.BusinessEntity, result *pipeline.AnalysisResult) error {
	// 解析活动数据
	activityData, err := p.parseActivityData(entity)
	if err != nil {
		return fmt.Errorf("failed to parse activity data: %w", err)
	}

	// 创建活动记录
	record := ActivityRecord{
		ActivityID:           activityData.ActivityID,
		CreatedTime:          activityData.CreatedTime,
		StartTime:            activityData.StartTime,
		ExpectedParticipants: activityData.ExpectedParticipants,
		TimeToStart:          activityData.StartTime.Sub(activityData.CreatedTime),
		Timestamp:            entity.Timestamp,
	}

	// 添加到历史记录
	p.addToHistory(record)

	// 执行多层异常检测
	anomalies := make([]*pipeline.Anomaly, 0)

	// 1. 阈值检测
	if thresholdAnomalies := p.detectThresholdAnomalies(record); len(thresholdAnomalies) > 0 {
		anomalies = append(anomalies, thresholdAnomalies...)
	}

	// 2. 统计异常检测
	if statisticalAnomalies := p.detectStatisticalAnomalies(record); len(statisticalAnomalies) > 0 {
		anomalies = append(anomalies, statisticalAnomalies...)
	}

	// 3. 模式匹配
	if patternAnomalies := p.detectPatternAnomalies(record); len(patternAnomalies) > 0 {
		anomalies = append(anomalies, patternAnomalies...)
	}

	// 添加异常到结果
	result.Anomalies = append(result.Anomalies, anomalies...)

	// 生成告警
	if len(anomalies) > 0 {
		alerts := p.generateAlerts(record, anomalies)
		result.Alerts = append(result.Alerts, alerts...)
	}

	return nil
}

// parseActivityData 解析活动数据
func (p *ActivityAnalysisPlugin) parseActivityData(entity *pipeline.BusinessEntity) (*pipeline.ActivityData, error) {
	activityData := &pipeline.ActivityData{
		ActivityID:   entity.ID,
		ActivityName: entity.Name,
		Status:       entity.Status,
	}

	// 从Properties中解析活动特定字段
	if createdTime, ok := entity.Properties["created_time"]; ok {
		if timeStr, ok := createdTime.(string); ok {
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				activityData.CreatedTime = t
			}
		} else if timeFloat, ok := createdTime.(float64); ok {
			activityData.CreatedTime = time.Unix(int64(timeFloat), 0)
		}
	}

	if startTime, ok := entity.Properties["start_time"]; ok {
		if timeStr, ok := startTime.(string); ok {
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				activityData.StartTime = t
			}
		} else if timeFloat, ok := startTime.(float64); ok {
			activityData.StartTime = time.Unix(int64(timeFloat), 0)
		}
	}

	if endTime, ok := entity.Properties["end_time"]; ok {
		if timeStr, ok := endTime.(string); ok {
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				activityData.EndTime = t
			}
		} else if timeFloat, ok := endTime.(float64); ok {
			activityData.EndTime = time.Unix(int64(timeFloat), 0)
		}
	}

	if expectedParticipants, ok := entity.Properties["expected_participants"]; ok {
		if participants, ok := expectedParticipants.(float64); ok {
			activityData.ExpectedParticipants = int64(participants)
		}
	}

	if actualParticipants, ok := entity.Properties["actual_participants"]; ok {
		if participants, ok := actualParticipants.(float64); ok {
			activityData.ActualParticipants = int64(participants)
		}
	}

	if activityType, ok := entity.Properties["activity_type"]; ok {
		if typeStr, ok := activityType.(string); ok {
			activityData.ActivityType = typeStr
		}
	}

	if creatorID, ok := entity.Properties["creator_id"]; ok {
		if creator, ok := creatorID.(string); ok {
			activityData.CreatorID = creator
		}
	}

	if description, ok := entity.Properties["description"]; ok {
		if desc, ok := description.(string); ok {
			activityData.Description = desc
		}
	}

	return activityData, nil
}

// addToHistory 添加到历史记录
func (p *ActivityAnalysisPlugin) addToHistory(record ActivityRecord) {
	p.activityHistory = append(p.activityHistory, record)

	// 保持历史记录大小限制
	if len(p.activityHistory) > p.maxHistorySize {
		// 移除最旧的记录
		p.activityHistory = p.activityHistory[1:]
	}
}

// detectThresholdAnomalies 阈值异常检测
func (p *ActivityAnalysisPlugin) detectThresholdAnomalies(record ActivityRecord) []*pipeline.Anomaly {
	anomalies := make([]*pipeline.Anomaly, 0)

	largeActivityThreshold := p.analysisConfig.Thresholds["large_activity_threshold"]
	immediateStartThreshold := p.analysisConfig.Thresholds["immediate_start_threshold"]

	// 检测大型活动立即开始的异常模式
	if record.ExpectedParticipants >= int64(largeActivityThreshold) &&
		record.TimeToStart.Seconds() <= immediateStartThreshold {

		anomaly := &pipeline.Anomaly{
			ID:          fmt.Sprintf("threshold_anomaly_%s_%d", record.ActivityID, time.Now().UnixNano()),
			Type:        "large_activity_immediate_start",
			Severity:    "warning",
			Score:       0.8, // 高异常分数
			Threshold:   immediateStartThreshold,
			Description: fmt.Sprintf("Large activity (expected participants: %d) started immediately after creation (time to start: %.0f seconds)", record.ExpectedParticipants, record.TimeToStart.Seconds()),
			Timestamp:   record.Timestamp,
			DeviceID:    "", // 业务数据可能没有设备ID
			MetricName:  "time_to_start",
			Value:       record.TimeToStart.Seconds(),
			Expected:    immediateStartThreshold,
			Attributes: map[string]any{
				"activity_id":           record.ActivityID,
				"expected_participants": record.ExpectedParticipants,
				"time_to_start_seconds": record.TimeToStart.Seconds(),
				"detection_method":      "threshold",
				"threshold_type":        "large_activity_immediate_start",
			},
		}
		anomalies = append(anomalies, anomaly)
	}

	return anomalies
}

// detectStatisticalAnomalies 统计异常检测
func (p *ActivityAnalysisPlugin) detectStatisticalAnomalies(record ActivityRecord) []*pipeline.Anomaly {
	anomalies := make([]*pipeline.Anomaly, 0)

	// 需要足够的历史数据进行统计分析
	if len(p.activityHistory) < 10 {
		return anomalies
	}

	// Z-Score异常检测
	if p.analysisConfig.Parameters["enable_z_score"].(bool) {
		if zScoreAnomalies := p.detectZScoreAnomalies(record); len(zScoreAnomalies) > 0 {
			anomalies = append(anomalies, zScoreAnomalies...)
		}
	}

	// IQR异常检测
	if p.analysisConfig.Parameters["enable_iqr"].(bool) {
		if iqrAnomalies := p.detectIQRAnomalies(record); len(iqrAnomalies) > 0 {
			anomalies = append(anomalies, iqrAnomalies...)
		}
	}

	return anomalies
}

// detectZScoreAnomalies Z-Score异常检测
func (p *ActivityAnalysisPlugin) detectZScoreAnomalies(record ActivityRecord) []*pipeline.Anomaly {
	anomalies := make([]*pipeline.Anomaly, 0)

	// 计算时间间隔的Z-Score
	timeToStartValues := make([]float64, 0)
	for _, histRecord := range p.activityHistory {
		timeToStartValues = append(timeToStartValues, histRecord.TimeToStart.Seconds())
	}

	mean := calculateMean(timeToStartValues)
	stdDev := calculateStdDev(timeToStartValues, mean)

	if stdDev > 0 {
		zScore := (record.TimeToStart.Seconds() - mean) / stdDev
		threshold := p.analysisConfig.Thresholds["z_score_threshold"]

		if math.Abs(zScore) > threshold {
			severity := "info"
			if math.Abs(zScore) > 3.0 {
				severity = "critical"
			} else if math.Abs(zScore) > 2.5 {
				severity = "warning"
			}

			anomaly := &pipeline.Anomaly{
				ID:          fmt.Sprintf("zscore_anomaly_%s_%d", record.ActivityID, time.Now().UnixNano()),
				Type:        "statistical_time_to_start_anomaly",
				Severity:    severity,
				Score:       math.Min(math.Abs(zScore)/5.0, 1.0), // 归一化到0-1
				Threshold:   threshold,
				Description: fmt.Sprintf("Activity time-to-start is statistically anomalous (Z-Score: %.2f, value: %.0f seconds, mean: %.0f seconds)", zScore, record.TimeToStart.Seconds(), mean),
				Timestamp:   record.Timestamp,
				MetricName:  "time_to_start_z_score",
				Value:       record.TimeToStart.Seconds(),
				Expected:    mean,
				Attributes: map[string]any{
					"activity_id":      record.ActivityID,
					"z_score":          zScore,
					"mean":             mean,
					"std_dev":          stdDev,
					"detection_method": "z_score",
					"sample_size":      len(timeToStartValues),
				},
			}
			anomalies = append(anomalies, anomaly)
		}
	}

	return anomalies
}

// detectIQRAnomalies IQR异常检测
func (p *ActivityAnalysisPlugin) detectIQRAnomalies(record ActivityRecord) []*pipeline.Anomaly {
	anomalies := make([]*pipeline.Anomaly, 0)

	// 计算时间间隔的IQR
	timeToStartValues := make([]float64, 0)
	for _, histRecord := range p.activityHistory {
		timeToStartValues = append(timeToStartValues, histRecord.TimeToStart.Seconds())
	}

	sort.Float64s(timeToStartValues)
	q1, q3 := calculateQuartiles(timeToStartValues)
	iqr := q3 - q1
	multiplier := p.analysisConfig.Thresholds["iqr_multiplier"]

	lowerBound := q1 - multiplier*iqr
	upperBound := q3 + multiplier*iqr

	currentValue := record.TimeToStart.Seconds()

	if currentValue < lowerBound || currentValue > upperBound {
		severity := "info"
		if currentValue < q1-3*iqr || currentValue > q3+3*iqr {
			severity = "critical"
		} else if currentValue < q1-2*iqr || currentValue > q3+2*iqr {
			severity = "warning"
		}

		// 计算异常分数
		var score float64
		if currentValue < lowerBound {
			score = math.Min((lowerBound-currentValue)/(q1-lowerBound), 1.0)
		} else {
			score = math.Min((currentValue-upperBound)/(upperBound-q3), 1.0)
		}

		anomaly := &pipeline.Anomaly{
			ID:          fmt.Sprintf("iqr_anomaly_%s_%d", record.ActivityID, time.Now().UnixNano()),
			Type:        "iqr_time_to_start_anomaly",
			Severity:    severity,
			Score:       score,
			Threshold:   multiplier,
			Description: fmt.Sprintf("Activity time-to-start is outside IQR bounds (value: %.0f seconds, bounds: [%.0f, %.0f])", currentValue, lowerBound, upperBound),
			Timestamp:   record.Timestamp,
			MetricName:  "time_to_start_iqr",
			Value:       currentValue,
			Expected:    (q1 + q3) / 2, // 中位数作为期望值
			Attributes: map[string]any{
				"activity_id":      record.ActivityID,
				"q1":               q1,
				"q3":               q3,
				"iqr":              iqr,
				"lower_bound":      lowerBound,
				"upper_bound":      upperBound,
				"detection_method": "iqr",
				"sample_size":      len(timeToStartValues),
			},
		}
		anomalies = append(anomalies, anomaly)
	}

	return anomalies
}

// detectPatternAnomalies 模式匹配异常检测
func (p *ActivityAnalysisPlugin) detectPatternAnomalies(record ActivityRecord) []*pipeline.Anomaly {
	anomalies := make([]*pipeline.Anomaly, 0)

	// 检查是否启用模式匹配
	if !p.analysisConfig.Parameters["enable_pattern_match"].(bool) {
		return anomalies
	}

	largeActivityThreshold := p.analysisConfig.Thresholds["large_activity_threshold"]
	immediateStartThreshold := p.analysisConfig.Thresholds["immediate_start_threshold"]

	// 模式1: 大型活动 + 立即开始 + 特定时间段
	if record.ExpectedParticipants >= int64(largeActivityThreshold) &&
		record.TimeToStart.Seconds() <= immediateStartThreshold {

		// 检查是否在工作时间外创建（可能是误操作）
		hour := record.CreatedTime.Hour()
		isOffHours := hour < 8 || hour > 22

		if isOffHours {
			anomaly := &pipeline.Anomaly{
				ID:          fmt.Sprintf("pattern_anomaly_%s_%d", record.ActivityID, time.Now().UnixNano()),
				Type:        "suspicious_activity_pattern",
				Severity:    "critical",
				Score:       0.9,
				Threshold:   immediateStartThreshold,
				Description: fmt.Sprintf("Suspicious pattern: Large activity (%d participants) created and started immediately during off-hours (%02d:00)", record.ExpectedParticipants, hour),
				Timestamp:   record.Timestamp,
				MetricName:  "pattern_match",
				Value:       1.0, // 模式匹配为1
				Expected:    0.0, // 期望不匹配
				Attributes: map[string]any{
					"activity_id":           record.ActivityID,
					"expected_participants": record.ExpectedParticipants,
					"time_to_start_seconds": record.TimeToStart.Seconds(),
					"created_hour":          hour,
					"is_off_hours":          isOffHours,
					"detection_method":      "pattern_match",
					"pattern_type":          "large_activity_off_hours_immediate",
				},
			}
			anomalies = append(anomalies, anomaly)
		}
	}

	return anomalies
}

// generateAlerts 生成告警
func (p *ActivityAnalysisPlugin) generateAlerts(record ActivityRecord, anomalies []*pipeline.Anomaly) []*pipeline.Alert {
	alerts := make([]*pipeline.Alert, 0)

	for _, anomaly := range anomalies {
		var alertType, title string
		var severity string = anomaly.Severity

		switch anomaly.Type {
		case "large_activity_immediate_start":
			alertType = "activity_anomaly"
			title = "Large Activity Started Immediately"
		case "statistical_time_to_start_anomaly":
			alertType = "statistical_anomaly"
			title = "Activity Time-to-Start Statistical Anomaly"
		case "iqr_time_to_start_anomaly":
			alertType = "statistical_anomaly"
			title = "Activity Time-to-Start IQR Anomaly"
		case "suspicious_activity_pattern":
			alertType = "security_alert"
			title = "Suspicious Activity Pattern Detected"
		default:
			alertType = "general_anomaly"
			title = "Activity Anomaly Detected"
		}

		alert := &pipeline.Alert{
			ID:          fmt.Sprintf("alert_%s_%d", record.ActivityID, time.Now().UnixNano()),
			Type:        alertType,
			Severity:    severity,
			Status:      "firing",
			Title:       title,
			Description: anomaly.Description,
			Timestamp:   anomaly.Timestamp,
			DeviceID:    anomaly.DeviceID,
			RuleID:      fmt.Sprintf("activity_analysis_%s", anomaly.Type),
			Attributes: map[string]any{
				"anomaly_id":     anomaly.ID,
				"anomaly_type":   anomaly.Type,
				"anomaly_score":  anomaly.Score,
				"activity_id":    record.ActivityID,
				"plugin_name":    p.name,
				"detection_time": time.Now(),
			},
			Actions: []pipeline.AlertAction{
				{
					Type:   "notification",
					Target: "activity_monitoring_team",
					Parameters: map[string]any{
						"channel": "email",
						"urgency": severity,
					},
					Status:    "pending",
					Timestamp: time.Now(),
				},
			},
		}

		// 根据严重程度添加不同的处理动作
		if severity == "critical" {
			alert.Actions = append(alert.Actions, pipeline.AlertAction{
				Type:   "escalation",
				Target: "security_team",
				Parameters: map[string]any{
					"escalation_level": 2,
					"auto_escalate":    true,
				},
				Status:    "pending",
				Timestamp: time.Now(),
			})
		}

		alerts = append(alerts, alert)
	}

	return alerts
}

// 统计计算辅助函数

// calculateMean 计算平均值
func calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

// calculateStdDev 计算标准差
func calculateStdDev(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 0
	}
	sumSquaredDiff := 0.0
	for _, v := range values {
		diff := v - mean
		sumSquaredDiff += diff * diff
	}
	variance := sumSquaredDiff / float64(len(values)-1)
	return math.Sqrt(variance)
}

// calculateQuartiles 计算四分位数
func calculateQuartiles(sortedValues []float64) (q1, q3 float64) {
	n := len(sortedValues)
	if n == 0 {
		return 0, 0
	}

	// 计算Q1位置
	q1Index := float64(n-1) * 0.25
	q1Lower := int(q1Index)
	q1Upper := q1Lower + 1
	if q1Upper >= n {
		q1Upper = n - 1
	}
	q1Weight := q1Index - float64(q1Lower)
	q1 = sortedValues[q1Lower]*(1-q1Weight) + sortedValues[q1Upper]*q1Weight

	// 计算Q3位置
	q3Index := float64(n-1) * 0.75
	q3Lower := int(q3Index)
	q3Upper := q3Lower + 1
	if q3Upper >= n {
		q3Upper = n - 1
	}
	q3Weight := q3Index - float64(q3Lower)
	q3 = sortedValues[q3Lower]*(1-q3Weight) + sortedValues[q3Upper]*q3Weight

	return q1, q3
}

// GetConfig 获取插件配置
func (p *ActivityAnalysisPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新插件配置
func (p *ActivityAnalysisPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.parseConfig(config)
}

// GetInputSchema 获取输入数据模式
func (p *ActivityAnalysisPlugin) GetInputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"business_data": {
				Type:        "array",
				Description: "Business entity data containing activity information",
				Required:    true,
			},
		},
		Required:    []string{"business_data"},
		Description: "Activity analysis plugin input schema",
		Version:     "1.0.0",
	}
}

// GetOutputSchema 获取输出数据模式
func (p *ActivityAnalysisPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"anomalies": {
				Type:        "array",
				Description: "Detected anomalies in activity data",
				Required:    false,
			},
			"alerts": {
				Type:        "array",
				Description: "Generated alerts based on anomalies",
				Required:    false,
			},
		},
		Required:    []string{},
		Description: "Activity analysis plugin output schema",
		Version:     "1.0.0",
	}
}

// GetMetrics 获取插件指标
func (p *ActivityAnalysisPlugin) GetMetrics() *pipeline.PluginMetrics {
	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.processedCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0, // 可以在实际实现中计算
		LastProcessTime: p.lastProcess,
		Status: func() string {
			if p.running {
				return "running"
			}
			return "stopped"
		}(),
		CustomMetrics: map[string]any{
			"anomaly_count":    p.anomalyCount,
			"history_size":     len(p.activityHistory),
			"max_history_size": p.maxHistorySize,
			"last_error": func() string {
				if p.lastError != nil {
					return p.lastError.Error()
				}
				return ""
			}(),
		},
	}
}

// GetAnalysisConfig 获取分析配置
func (p *ActivityAnalysisPlugin) GetAnalysisConfig() *pipeline.AnalysisConfig {
	return p.analysisConfig
}

// UpdateAnalysisConfig 更新分析配置
func (p *ActivityAnalysisPlugin) UpdateAnalysisConfig(config *pipeline.AnalysisConfig) error {
	if config == nil {
		return fmt.Errorf("analysis config cannot be nil")
	}
	p.analysisConfig = config
	return nil
}

// GetPluginInfo 获取插件信息 (工厂方法)
func (f *ActivityAnalysisPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "activity_analyzer",
		Version:  "1.0.0",
		Type:     pipeline.AnalyzerType,
		LoadedAt: time.Now(),
		Metadata: map[string]any{
			"description": "Activity analysis plugin for detecting anomalies in business activity data",
			"author":      "AIOps Team",
			"license":     "MIT",
			"tags":        []string{"business", "activity", "anomaly-detection", "statistics"},
			"capabilities": []string{
				"threshold_detection",
				"statistical_analysis",
				"pattern_matching",
				"z_score_analysis",
				"iqr_analysis",
			},
			"config_schema": map[string]interface{}{
				"max_history_size": map[string]interface{}{
					"type":        "integer",
					"description": "Maximum number of activity records to keep in history",
					"default":     1000,
					"minimum":     10,
					"maximum":     10000,
				},
				"analysis": map[string]interface{}{
					"type":        "object",
					"description": "Analysis configuration",
					"properties": map[string]interface{}{
						"algorithm": map[string]interface{}{
							"type":        "string",
							"description": "Analysis algorithm to use",
							"default":     "statistical_anomaly",
							"enum":        []string{"statistical_anomaly", "threshold_only", "pattern_only"},
						},
						"sensitivity": map[string]interface{}{
							"type":        "number",
							"description": "Analysis sensitivity (0.01-0.1)",
							"default":     0.05,
							"minimum":     0.01,
							"maximum":     0.1,
						},
						"thresholds": map[string]interface{}{
							"type":        "object",
							"description": "Detection thresholds",
							"properties": map[string]interface{}{
								"large_activity_threshold": map[string]interface{}{
									"type":        "number",
									"description": "Threshold for large activity participant count",
									"default":     10000,
								},
								"immediate_start_threshold": map[string]interface{}{
									"type":        "number",
									"description": "Threshold for immediate start time in seconds",
									"default":     300,
								},
							},
						},
					},
				},
			},
			"dependencies": []string{},
		},
	}
}

// ValidateConfig 验证配置
func (f *ActivityAnalysisPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 验证最大历史记录大小
	if maxSize, exists := config["max_history_size"]; exists {
		if size, ok := maxSize.(float64); ok {
			if size < 10 || size > 10000 {
				return fmt.Errorf("max_history_size must be between 10 and 10000, got: %.0f", size)
			}
		} else {
			return fmt.Errorf("max_history_size must be a number")
		}
	}

	// 验证分析配置
	if analysisConfig, exists := config["analysis"]; exists {
		if configMap, ok := analysisConfig.(map[string]interface{}); ok {
			// 验证算法
			if algorithm, exists := configMap["algorithm"]; exists {
				if alg, ok := algorithm.(string); ok {
					validAlgorithms := []string{"statistical_anomaly", "threshold_only", "pattern_only"}
					valid := false
					for _, validAlg := range validAlgorithms {
						if alg == validAlg {
							valid = true
							break
						}
					}
					if !valid {
						return fmt.Errorf("invalid algorithm: %s, must be one of: %v", alg, validAlgorithms)
					}
				} else {
					return fmt.Errorf("algorithm must be a string")
				}
			}

			// 验证敏感度
			if sensitivity, exists := configMap["sensitivity"]; exists {
				if sens, ok := sensitivity.(float64); ok {
					if sens < 0.01 || sens > 0.1 {
						return fmt.Errorf("sensitivity must be between 0.01 and 0.1, got: %.3f", sens)
					}
				} else {
					return fmt.Errorf("sensitivity must be a number")
				}
			}

			// 验证阈值
			if thresholds, exists := configMap["thresholds"]; exists {
				if threshMap, ok := thresholds.(map[string]interface{}); ok {
					if largeActivityThreshold, exists := threshMap["large_activity_threshold"]; exists {
						if threshold, ok := largeActivityThreshold.(float64); ok {
							if threshold <= 0 {
								return fmt.Errorf("large_activity_threshold must be positive, got: %.0f", threshold)
							}
						} else {
							return fmt.Errorf("large_activity_threshold must be a number")
						}
					}

					if immediateStartThreshold, exists := threshMap["immediate_start_threshold"]; exists {
						if threshold, ok := immediateStartThreshold.(float64); ok {
							if threshold < 0 {
								return fmt.Errorf("immediate_start_threshold must be non-negative, got: %.0f", threshold)
							}
						} else {
							return fmt.Errorf("immediate_start_threshold must be a number")
						}
					}
				} else {
					return fmt.Errorf("thresholds must be an object")
				}
			}
		} else {
			return fmt.Errorf("analysis must be an object")
		}
	}

	return nil
}
