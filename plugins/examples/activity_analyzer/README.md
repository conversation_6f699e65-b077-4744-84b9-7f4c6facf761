# Activity Analyzer Plugin

## 概述

Activity Analyzer Plugin 是一个专门用于分析业务活动数据的AIOps插件，能够检测活动数据中的异常模式，特别是大型活动立即开始的可疑行为。

## 功能特性

### 核心分析算法

1. **阈值异常检测**
   - 检测大型活动（参与人数超过阈值）立即开始的异常模式
   - 可配置的参与人数阈值和时间阈值

2. **统计异常检测**
   - **Z-Score分析**：基于历史数据的标准差检测异常
   - **IQR分析**：使用四分位距识别异常值
   - 动态学习历史模式

3. **模式匹配**
   - 检测可疑的活动创建模式
   - 工作时间外的大型活动异常检测
   - 多维度模式组合分析

### 配置灵活性

- 支持动态配置更新
- 可选择性启用/禁用不同的检测算法
- 参数级别的细粒度调整
- 历史数据大小可配置

## 安装和配置

### 1. 构建插件

```bash
cd /path/to/aiops
./plugins/build.sh
```

### 2. 配置插件

在 `config/plugins.yaml` 中添加配置：

```yaml
plugins:
  - name: "activity-analyzer"
    version: "1.0.0"
    type: "analyzer"
    enabled: true
    path: "./plugins/build/activity-analyzer-1.0.0.so"
    config:
      max_history_size: 1000
      analysis:
        algorithm: "statistical_anomaly"
        sensitivity: 0.05
        thresholds:
          large_activity_threshold: 10000
          immediate_start_threshold: 300
          z_score_threshold: 2.0
          iqr_multiplier: 1.5
        parameters:
          enable_z_score: true
          enable_iqr: true
          enable_pattern_match: true
```

### 3. 创建流水线

参考 `config/pipeline_examples/activity_analysis_pipeline.yaml` 创建包含活动分析的流水线。

## 配置参数说明

### 基础配置

- `max_history_size`: 历史记录最大保存数量（默认：1000）

### 分析配置

- `algorithm`: 分析算法类型
  - `statistical_anomaly`: 统计异常检测（推荐）
  - `threshold_only`: 仅阈值检测
  - `pattern_only`: 仅模式匹配

- `sensitivity`: 分析敏感度（0.01-0.1，默认：0.05）

### 阈值配置

- `large_activity_threshold`: 大型活动参与人数阈值（默认：10000）
- `immediate_start_threshold`: 立即开始时间阈值，秒（默认：300）
- `z_score_threshold`: Z-Score异常阈值（默认：2.0）
- `iqr_multiplier`: IQR倍数（默认：1.5）

### 算法开关

- `enable_z_score`: 启用Z-Score检测（默认：true）
- `enable_iqr`: 启用IQR检测（默认：true）
- `enable_pattern_match`: 启用模式匹配（默认：true）

## 使用示例

### 业务数据格式

插件期望接收包含业务数据的 `PipelineData`：

```json
{
  "type": "business",
  "business_data": [
    {
      "id": "activity_001",
      "type": "activity",
      "name": "Large Conference",
      "status": "created",
      "properties": {
        "activity_id": "activity_001",
        "created_time": "2024-01-15T10:00:00Z",
        "start_time": "2024-01-15T10:05:00Z",
        "expected_participants": 15000,
        "activity_type": "conference"
      }
    }
  ]
}
```

### 异常检测结果

插件会在检测到异常时生成 `Anomaly` 和 `Alert`：

```json
{
  "anomalies": [
    {
      "id": "threshold_anomaly_activity_001_...",
      "type": "large_activity_immediate_start",
      "severity": "warning",
      "score": 0.8,
      "description": "Large activity (15000 participants) started immediately after creation (300 seconds)",
      "attributes": {
        "activity_id": "activity_001",
        "expected_participants": 15000,
        "time_to_start_seconds": 300,
        "detection_method": "threshold"
      }
    }
  ],
  "alerts": [
    {
      "type": "activity_anomaly",
      "severity": "warning",
      "title": "Large Activity Started Immediately",
      "description": "Large activity (15000 participants) started immediately after creation"
    }
  ]
}
```

## 监控和指标

插件提供以下监控指标：

- `processed_count`: 处理的数据条数
- `anomaly_count`: 检测到的异常数量
- `error_count`: 错误次数
- `history_size`: 当前历史记录数量
- `last_process_time`: 最后处理时间

## 故障排除

### 常见问题

1. **插件加载失败**
   - 检查插件文件路径是否正确
   - 确认插件文件权限
   - 查看Agent日志获取详细错误信息

2. **配置验证失败**
   - 检查配置参数类型和范围
   - 确认必需参数已提供
   - 参考配置示例进行对比

3. **异常检测不准确**
   - 调整敏感度参数
   - 增加历史数据积累时间
   - 检查阈值设置是否合理

### 日志级别

- `INFO`: 正常处理信息
- `WARN`: 检测到异常
- `ERROR`: 插件错误

## 开发和扩展

### 添加新的检测算法

1. 在 `detectStatisticalAnomalies` 方法中添加新算法
2. 更新配置验证逻辑
3. 添加相应的测试用例

### 自定义异常类型

1. 定义新的异常类型常量
2. 在检测方法中生成相应的 `Anomaly`
3. 更新告警生成逻辑

## 版本历史

- **v1.0.0**: 初始版本
  - 基础阈值检测
  - Z-Score和IQR统计分析
  - 模式匹配检测
  - 动态配置支持
