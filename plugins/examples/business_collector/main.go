package main

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"aiops/pkg/pipeline"
)

// BusinessCollectorPlugin 业务数据采集器插件
type BusinessCollectorPlugin struct {
	name     string
	version  string
	config   map[string]interface{}
	interval time.Duration
	running  bool

	// 统计信息
	collectCount int64
	errorCount   int64
	lastError    error
	lastCollect  time.Time

	// 配置选项
	enableActivity bool
	enableUser     bool
	activityCount  int
}

// BusinessCollectorPluginFactory 业务数据采集器插件工厂
type BusinessCollectorPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &BusinessCollectorPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *BusinessCollectorPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.CollectorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &BusinessCollectorPlugin{
		name:           "business_collector",
		version:        "1.0.0",
		config:         config,
		interval:       30 * time.Second,
		enableActivity: true,
		enableUser:     true,
		activityCount:  5, // 默认每次生成5个活动
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *BusinessCollectorPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.CollectorType}
}

// GetPluginInfo 获取插件信息
func (f *BusinessCollectorPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "business_collector",
		Version:  "1.0.0",
		Type:     pipeline.CollectorType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "Business data collector for generating sample activity and user data",
			"author":      "AIOps Team",
			"category":    "business",
		},
	}
}

// ValidateConfig 验证配置
func (f *BusinessCollectorPluginFactory) ValidateConfig(config map[string]interface{}) error {
	return nil
}

// 错误定义
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
)

// GetName 获取插件名称
func (p *BusinessCollectorPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *BusinessCollectorPlugin) GetType() pipeline.PluginType {
	return pipeline.CollectorType
}

// GetVersion 获取插件版本
func (p *BusinessCollectorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *BusinessCollectorPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析配置
	if interval, ok := config["interval"]; ok {
		if intervalStr, ok := interval.(string); ok {
			if d, err := time.ParseDuration(intervalStr); err == nil {
				p.interval = d
			}
		}
	}

	if enableActivity, ok := config["enable_activity"]; ok {
		if enable, ok := enableActivity.(bool); ok {
			p.enableActivity = enable
		}
	}

	if enableUser, ok := config["enable_user"]; ok {
		if enable, ok := enableUser.(bool); ok {
			p.enableUser = enable
		}
	}

	if activityCount, ok := config["activity_count"]; ok {
		if count, ok := activityCount.(float64); ok {
			p.activityCount = int(count)
		}
	}

	return nil
}

// Start 启动插件
func (p *BusinessCollectorPlugin) Start(ctx context.Context) error {
	p.running = true
	return nil
}

// Stop 停止插件
func (p *BusinessCollectorPlugin) Stop() error {
	p.running = false
	return nil
}

// Health 健康检查
func (p *BusinessCollectorPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}
	return nil
}

// Process 处理数据（采集器不需要实现）
func (p *BusinessCollectorPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	return data, nil
}

// Collect 采集业务数据
func (p *BusinessCollectorPlugin) Collect(ctx context.Context) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.collectCount++
		p.lastCollect = time.Now()
	}()

	businessEntities := make([]*pipeline.BusinessEntity, 0)

	// 生成活动数据
	if p.enableActivity {
		activities := p.generateActivityData()
		businessEntities = append(businessEntities, activities...)
	}

	// 生成用户数据
	if p.enableUser {
		users := p.generateUserData()
		businessEntities = append(businessEntities, users...)
	}

	// 创建流水线数据
	data := &pipeline.PipelineData{
		ID:           fmt.Sprintf("business_data_%d", time.Now().UnixNano()),
		Type:         pipeline.BusinessDataType,
		Source:       p.name,
		DeviceID:     "business_system",
		Timestamp:    time.Now(),
		BusinessData: businessEntities,
		Metadata: map[string]interface{}{
			"collected_at":     time.Now(),
			"collector":        p.name,
			"entity_count":     len(businessEntities),
			"activity_enabled": p.enableActivity,
			"user_enabled":     p.enableUser,
		},
		Context: map[string]interface{}{
			"collection_type": "simulated",
			"data_source":     "business_system",
		},
		Tags: map[string]string{
			"data_type": "business",
			"source":    p.name,
		},
	}

	return data, nil
}

// generateActivityData 生成活动数据
func (p *BusinessCollectorPlugin) generateActivityData() []*pipeline.BusinessEntity {
	activities := make([]*pipeline.BusinessEntity, 0, p.activityCount)

	for i := 0; i < p.activityCount; i++ {
		now := time.Now()

		// 随机生成活动参数
		expectedParticipants := rand.Int63n(50000) + 100 // 100-50000人

		// 10%的概率生成大型活动
		if rand.Float64() < 0.1 {
			expectedParticipants = rand.Int63n(40000) + 10000 // 10000-50000人
		}

		// 创建时间在过去1小时内
		createdTime := now.Add(-time.Duration(rand.Intn(3600)) * time.Second)

		// 开始时间：70%正常（1-24小时后），20%较快（10分钟-1小时），10%立即开始
		var startTime time.Time
		randValue := rand.Float64()
		if randValue < 0.1 { // 10%立即开始
			startTime = createdTime.Add(time.Duration(rand.Intn(300)) * time.Second) // 0-5分钟
		} else if randValue < 0.3 { // 20%较快开始
			startTime = createdTime.Add(time.Duration(rand.Intn(3000)+600) * time.Second) // 10分钟-1小时
		} else { // 70%正常开始
			startTime = createdTime.Add(time.Duration(rand.Intn(82800)+3600) * time.Second) // 1-24小时
		}

		endTime := startTime.Add(time.Duration(rand.Intn(7200)+1800) * time.Second) // 持续30分钟-2小时

		activityID := fmt.Sprintf("activity_%d_%d", now.Unix(), i)

		activity := &pipeline.BusinessEntity{
			ID:        activityID,
			Type:      string(pipeline.ActivityEntityType),
			Name:      fmt.Sprintf("Activity %d", i+1),
			Status:    "created",
			Timestamp: now,
			Properties: map[string]interface{}{
				"activity_id":           activityID,
				"activity_name":         fmt.Sprintf("Sample Activity %d", i+1),
				"activity_type":         getRandomActivityType(),
				"created_time":          createdTime.Format(time.RFC3339),
				"start_time":            startTime.Format(time.RFC3339),
				"end_time":              endTime.Format(time.RFC3339),
				"expected_participants": expectedParticipants,
				"actual_participants":   int64(0), // 活动还未开始
				"creator_id":            fmt.Sprintf("user_%d", rand.Intn(1000)),
				"description":           fmt.Sprintf("This is a sample activity %d for testing", i+1),
			},
			Metadata: map[string]interface{}{
				"generated_at": now,
				"source":       "business_collector",
				"simulation":   true,
			},
			Tags: map[string]string{
				"entity_type": "activity",
				"status":      "created",
			},
		}

		activities = append(activities, activity)
	}

	return activities
}

// generateUserData 生成用户数据
func (p *BusinessCollectorPlugin) generateUserData() []*pipeline.BusinessEntity {
	users := make([]*pipeline.BusinessEntity, 0, 2) // 生成2个用户示例

	for i := range 2 {
		now := time.Now()
		userID := fmt.Sprintf("user_%d_%d", now.Unix(), i)

		user := &pipeline.BusinessEntity{
			ID:        userID,
			Type:      string(pipeline.UserEntityType),
			Name:      fmt.Sprintf("User %d", i+1),
			Status:    "active",
			Timestamp: now,
			Properties: map[string]interface{}{
				"user_id":       userID,
				"username":      fmt.Sprintf("user%d", i+1),
				"user_type":     getRandomUserType(),
				"register_time": now.Add(-time.Duration(rand.Intn(365*24)) * time.Hour).Format(time.RFC3339),
				"last_login":    now.Add(-time.Duration(rand.Intn(24)) * time.Hour).Format(time.RFC3339),
			},
			Metadata: map[string]interface{}{
				"generated_at": now,
				"source":       "business_collector",
				"simulation":   true,
			},
			Tags: map[string]string{
				"entity_type": "user",
				"status":      "active",
			},
		}

		users = append(users, user)
	}

	return users
}

// getRandomActivityType 获取随机活动类型
func getRandomActivityType() string {
	types := []string{"conference", "workshop", "webinar", "training", "meeting", "event"}
	return types[rand.Intn(len(types))]
}

// getRandomUserType 获取随机用户类型
func getRandomUserType() string {
	types := []string{"admin", "organizer", "participant", "viewer"}
	return types[rand.Intn(len(types))]
}

// GetCollectInterval 获取采集间隔
func (p *BusinessCollectorPlugin) GetCollectInterval() time.Duration {
	return p.interval
}

// SetCollectInterval 设置采集间隔
func (p *BusinessCollectorPlugin) SetCollectInterval(interval time.Duration) error {
	p.interval = interval
	return nil
}

// GetConfig 获取插件配置
func (p *BusinessCollectorPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新插件配置
func (p *BusinessCollectorPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.Initialize(config)
}

// GetInputSchema 获取输入数据模式
func (p *BusinessCollectorPlugin) GetInputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields:      map[string]pipeline.FieldSchema{},
		Required:    []string{},
		Description: "Business collector does not require input data",
		Version:     "1.0.0",
	}
}

// GetOutputSchema 获取输出数据模式
func (p *BusinessCollectorPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"business_data": {
				Type:        "array",
				Description: "Generated business entity data",
				Required:    true,
			},
		},
		Required:    []string{"business_data"},
		Description: "Business collector output schema",
		Version:     "1.0.0",
	}
}

// GetMetrics 获取插件指标
func (p *BusinessCollectorPlugin) GetMetrics() *pipeline.PluginMetrics {
	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.collectCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0,
		LastProcessTime: p.lastCollect,
		Status: func() string {
			if p.running {
				return "running"
			}
			return "stopped"
		}(),
		CustomMetrics: map[string]interface{}{
			"collect_interval": p.interval.String(),
			"activity_enabled": p.enableActivity,
			"user_enabled":     p.enableUser,
			"activity_count":   p.activityCount,
			"last_error": func() string {
				if p.lastError != nil {
					return p.lastError.Error()
				}
				return ""
			}(),
		},
	}
}
