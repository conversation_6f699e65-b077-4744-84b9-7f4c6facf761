package main

import (
	"context"
	"fmt"
	"time"

	"aiops/pkg/pipeline"
)

// EmailAlerterPlugin 邮件告警器插件
type EmailAlerterPlugin struct {
	name    string
	version string
	config  map[string]any
	running bool

	// 邮件配置
	smtpServer string
	smtpPort   int
	username   string
	password   string
	recipients []string

	// 统计信息
	processedCount int64
	sentCount      int64
	errorCount     int64
	lastError      error
	lastProcess    time.Time
}

// EmailAlerterPluginFactory 邮件告警器插件工厂
type EmailAlerterPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &EmailAlerterPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *EmailAlerterPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.AlerterType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &EmailAlerterPlugin{
		name:       "email_alerter",
		version:    "1.0.0",
		config:     config,
		recipients: make([]string, 0),
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *EmailAlerterPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.AlerterType}
}

// GetPluginInfo 获取插件信息
func (f *EmailAlerterPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "email_alerter",
		Version:  "1.0.0",
		Type:     pipeline.AlerterType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "Email notification alerter",
			"author":      "DevInsight Team",
			"category":    "alerter",
		},
	}
}

// ValidateConfig 验证配置
func (f *EmailAlerterPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"smtp_server", "recipients"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}
	return nil
}

// ==================== 插件实现 ====================

// GetName 获取插件名称
func (p *EmailAlerterPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *EmailAlerterPlugin) GetType() pipeline.PluginType {
	return pipeline.AlerterType
}

// GetVersion 获取插件版本
func (p *EmailAlerterPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *EmailAlerterPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析SMTP配置
	if server, ok := config["smtp_server"]; ok {
		if serverStr, ok := server.(string); ok {
			p.smtpServer = serverStr
		}
	}

	if port, ok := config["smtp_port"]; ok {
		if portFloat, ok := port.(float64); ok {
			p.smtpPort = int(portFloat)
		}
	} else {
		p.smtpPort = 587 // 默认端口
	}

	if username, ok := config["username"]; ok {
		if usernameStr, ok := username.(string); ok {
			p.username = usernameStr
		}
	}

	if password, ok := config["password"]; ok {
		if passwordStr, ok := password.(string); ok {
			p.password = passwordStr
		}
	}

	// 解析收件人列表
	if recipients, ok := config["recipients"]; ok {
		switch v := recipients.(type) {
		case string:
			p.recipients = []string{v}
		case []interface{}:
			p.recipients = make([]string, 0, len(v))
			for _, recipient := range v {
				if recipientStr, ok := recipient.(string); ok {
					p.recipients = append(p.recipients, recipientStr)
				}
			}
		case []string:
			p.recipients = v
		}
	}

	return nil
}

// Start 启动插件
func (p *EmailAlerterPlugin) Start(ctx context.Context) error {
	p.running = true
	return nil
}

// Stop 停止插件
func (p *EmailAlerterPlugin) Stop() error {
	p.running = false
	return nil
}

// Health 健康检查
func (p *EmailAlerterPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}

	// 检查配置
	if p.smtpServer == "" {
		return fmt.Errorf("SMTP server not configured")
	}

	if len(p.recipients) == 0 {
		return fmt.Errorf("no recipients configured")
	}

	return nil
}

// Process 处理数据
func (p *EmailAlerterPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.processedCount++
		p.lastProcess = time.Now()
	}()

	// 检查是否有告警数据
	if data.Alerts == nil || len(data.Alerts) == 0 {
		return data, nil
	}

	// 发送每个告警
	for _, alert := range data.Alerts {
		if err := p.sendAlert(alert); err != nil {
			p.errorCount++
			p.lastError = err
			// 记录错误但继续处理其他告警
			continue
		}
		p.sentCount++
	}

	// 清除上次错误（如果所有告警都发送成功）
	if p.lastError == nil {
		p.lastError = nil
	}

	return data, nil
}

// sendAlert 发送告警邮件
func (p *EmailAlerterPlugin) sendAlert(alert *pipeline.Alert) error {
	// 简化实现：这里只是模拟发送邮件
	// 实际实现应该使用真正的SMTP客户端

	subject := fmt.Sprintf("[DevInsight Alert] %s", alert.Title)
	body := fmt.Sprintf(`
告警详情:
- ID: %s
- 类型: %s
- 严重级别: %s
- 状态: %s
- 描述: %s
- 设备ID: %s
- 时间: %s

详细信息:
%v
`, alert.ID, alert.Type, alert.Severity, alert.Status, alert.Description,
		alert.DeviceID, alert.Timestamp.Format("2006-01-02 15:04:05"), alert.Attributes)

	// 模拟发送邮件
	fmt.Printf("模拟发送邮件:\n")
	fmt.Printf("收件人: %v\n", p.recipients)
	fmt.Printf("主题: %s\n", subject)
	fmt.Printf("内容: %s\n", body)
	fmt.Printf("SMTP服务器: %s:%d\n", p.smtpServer, p.smtpPort)
	fmt.Printf("---\n")

	return nil
}

// GetConfig 获取配置
func (p *EmailAlerterPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新配置
func (p *EmailAlerterPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.Initialize(config)
}

// GetInputSchema 获取输入模式
func (p *EmailAlerterPlugin) GetInputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"alerts": {
				Type:        "array",
				Description: "告警数据数组",
				Required:    true,
			},
		},
		Description: "需要包含告警数据的流水线数据",
		Version:     "1.0",
	}
}

// GetOutputSchema 获取输出模式
func (p *EmailAlerterPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"alerts": {
				Type:        "array",
				Description: "处理后的告警数据",
				Required:    false,
			},
		},
		Description: "处理后的流水线数据",
		Version:     "1.0",
	}
}

// GetMetrics 获取插件指标
func (p *EmailAlerterPlugin) GetMetrics() *pipeline.PluginMetrics {
	status := "stopped"
	if p.running {
		status = "running"
	}

	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.processedCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0,
		LastProcessTime: p.lastProcess,
		Status:          status,
		CustomMetrics: map[string]interface{}{
			"sent_count":      p.sentCount,
			"smtp_server":     p.smtpServer,
			"smtp_port":       p.smtpPort,
			"recipient_count": len(p.recipients),
			"last_error":      p.getLastErrorString(),
		},
	}
}

// getLastErrorString 获取最后错误的字符串表示
func (p *EmailAlerterPlugin) getLastErrorString() string {
	if p.lastError != nil {
		return p.lastError.Error()
	}
	return ""
}

// ==================== 告警器专用方法 ====================

// SendAlert 发送告警（告警器接口方法）
func (p *EmailAlerterPlugin) SendAlert(ctx context.Context, alert *pipeline.Alert) error {
	return p.sendAlert(alert)
}

// GetAlertConfig 获取告警配置
func (p *EmailAlerterPlugin) GetAlertConfig() *pipeline.AlertConfig {
	return &pipeline.AlertConfig{
		Channels: []pipeline.AlertChannel{
			{
				Type:    "email",
				Name:    "default_email",
				Enabled: p.running,
				Config: map[string]interface{}{
					"smtp_server": p.smtpServer,
					"smtp_port":   p.smtpPort,
				},
				Recipients: p.recipients,
			},
		},
	}
}

// UpdateAlertConfig 更新告警配置
func (p *EmailAlerterPlugin) UpdateAlertConfig(config *pipeline.AlertConfig) error {
	// 简化实现：从第一个邮件通道更新配置
	for _, channel := range config.Channels {
		if channel.Type == "email" {
			newConfig := make(map[string]interface{})
			newConfig["smtp_server"] = channel.Config["smtp_server"]
			newConfig["smtp_port"] = channel.Config["smtp_port"]
			newConfig["recipients"] = channel.Recipients

			return p.UpdateConfig(newConfig)
		}
	}
	return nil
}

// 定义错误类型
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
)

// main 函数（插件编译时需要）
func main() {
	// 插件作为共享库时不需要main函数
	// 但为了编译通过，保留空的main函数
}
