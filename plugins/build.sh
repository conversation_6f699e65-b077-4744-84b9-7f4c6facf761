#!/bin/bash

# 插件构建脚本
# 用于将 Go 插件编译为动态库 (.so 文件)
# 自动扫描 examples 目录并根据版本添加版本后缀

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"
EXAMPLES_DIR="$SCRIPT_DIR/examples"

# 默认版本，可以通过环境变量覆盖
PLUGIN_VERSION="${PLUGIN_VERSION:-1.0.0}"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "开始构建插件..."
echo "插件版本: $PLUGIN_VERSION"
echo ""

# 获取当前时间戳用于增量构建检查
current_time=$(date +%s)

# 自动扫描 examples 目录下的所有插件项目
build_plugin() {
    local plugin_dir="$1"
    local plugin_name=$(basename "$plugin_dir")
    local main_file="$plugin_dir/main.go"
    local output_file="$BUILD_DIR/${plugin_name}-${PLUGIN_VERSION}.so"

    # 检查是否存在 Go 源文件
    local has_go_files=false
    local has_main_go=false
    local has_go_mod=false
    
    if [ -f "$plugin_dir/go.mod" ]; then
        has_go_mod=true
    fi
    
    if [ -f "$main_file" ]; then
        has_main_go=true
    fi
    
    # 检查是否有其他 Go 文件
    if compgen -G "$plugin_dir/*.go" > /dev/null; then
        has_go_files=true
    fi
    
    # 验证是否可以构建
    if [ "$has_go_mod" = false ] && [ "$has_go_files" = false ]; then
        echo "⚠️  跳过 $plugin_name: 未找到任何 Go 源文件"
        return
    elif [ "$has_go_mod" = false ] && [ "$has_main_go" = false ]; then
        # 没有 go.mod 也没有 main.go，检查是否有其他 Go 文件可以作为入口
        local go_files_count=$(ls "$plugin_dir"/*.go 2>/dev/null | wc -l | tr -d ' ')
        if [ "$go_files_count" -eq 0 ]; then
            echo "⚠️  跳过 $plugin_name: 未找到 Go 源文件"
            return
        fi
        echo "⚠️  警告 $plugin_name: 没有 main.go 文件，将尝试编译所有 Go 文件"
    fi

    # 检查是否需要重新构建（增量构建）
    if [ -f "$output_file" ]; then
        local needs_rebuild=false
        
        # 查找所有相关文件，检查是否有比输出文件更新的
        # 包括 Go 源文件和 go.mod/go.sum 文件
        local check_files=()
        
        # 添加所有 Go 源文件
        while IFS= read -r -d '' go_file; do
            check_files+=("$go_file")
        done < <(find "$plugin_dir" -maxdepth 1 -name "*.go" -print0 2>/dev/null)
        
        # 添加 go.mod 和 go.sum 文件（如果存在）
        if [ -f "$plugin_dir/go.mod" ]; then
            check_files+=("$plugin_dir/go.mod")
        fi
        if [ -f "$plugin_dir/go.sum" ]; then
            check_files+=("$plugin_dir/go.sum")
        fi
        
        # 检查文件是否有更新
        for file in "${check_files[@]}"; do
            if [ "$file" -nt "$output_file" ]; then
                needs_rebuild=true
                break
            fi
        done
        
        if [ "$needs_rebuild" = false ]; then
            echo "⏭️  跳过 $plugin_name: 无需重新构建"
            return
        fi
    fi

    echo "🔨 构建 $plugin_name 插件..."
    cd "$plugin_dir"

    # 构建插件，添加版本信息到 ldflags
    # 确定构建目标：优先使用 go.mod，否则构建所有 Go 文件
    local build_target
    local build_desc
    
    if [ -f "go.mod" ]; then
        # 如果存在 go.mod 文件，编译整个模块
        build_target="./"
        build_desc="Go模块"
    else
        # 没有 go.mod 文件时，查找所有 Go 源文件
        local go_files=(*.go)
        if [ ${#go_files[@]} -eq 1 ] && [ -f "main.go" ]; then
            # 只有一个 main.go 文件
            build_target="main.go"
            build_desc="单文件"
        elif [ ${#go_files[@]} -gt 0 ] && [ "${go_files[0]}" != "*.go" ]; then
            # 有多个 Go 文件，编译所有文件
            build_target="*.go"
            build_desc="多文件 (${#go_files[@]} 个文件)"
        else
            echo "❌ $plugin_name: 未找到任何 Go 源文件"
            return 1
        fi
    fi
    
    echo "   构建类型: $build_desc"
    echo "   构建目标: $build_target"

    if go build -buildmode=plugin \
        -ldflags="-s -w" \
        -o "$output_file" $build_target; then
        echo "✅ ${plugin_name}-${PLUGIN_VERSION}.so 构建完成"
        # 创建不带版本的符号链接，方便开发和测试
        # ln -sf "${plugin_name}-${PLUGIN_VERSION}.so" "$BUILD_DIR/${plugin_name}.so"
    else
        echo "❌ $plugin_name 构建失败"
        return 1
    fi
}

# 扫描并构建所有插件
plugin_count=0
success_count=0

if [ -d "$EXAMPLES_DIR" ]; then
    for plugin_dir in "$EXAMPLES_DIR"/*; do
        if [ -d "$plugin_dir" ]; then
            plugin_count=$((plugin_count + 1))
            if build_plugin "$plugin_dir"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
else
    echo "❌ Examples 目录不存在: $EXAMPLES_DIR"
    exit 1
fi

echo ""
echo "📊 构建统计:"
echo "   发现插件: $plugin_count"
echo "   构建成功: $success_count"
echo "   构建失败: $((plugin_count - success_count))"
echo ""
echo "📁 插件文件位置: $BUILD_DIR"
echo ""

# 显示构建结果
if [ $success_count -gt 0 ]; then
    echo "🎉 构建完成的插件:"
    ls -la "$BUILD_DIR"/*.so 2>/dev/null || echo "   无插件文件"
else
    echo "⚠️  没有成功构建任何插件"
fi
