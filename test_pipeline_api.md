# Pipeline Management API 测试指南

本文档说明如何使用新的命令驱动流水线管理API。

## API 端点概览

### 基本 CRUD 操作
- `GET /api/v1/pipelines` - 获取所有流水线
- `POST /api/v1/pipelines` - 创建流水线
- `GET /api/v1/pipelines/:id` - 获取单个流水线
- `PUT /api/v1/pipelines/:id` - 更新流水线
- `DELETE /api/v1/pipelines/:id` - 删除流水线

### 流水线状态管理
- `GET /api/v1/pipelines/:id/status` - 获取流水线状态
- `POST /api/v1/pipelines/:id/start` - 启动流水线
- `POST /api/v1/pipelines/:id/stop` - 停止流水线
- `POST /api/v1/pipelines/:id/restart` - 重启流水线
- `POST /api/v1/pipelines/:id/reload` - 重新加载流水线配置

### Agent 相关操作
- `GET /api/v1/pipelines/agent/:agentId` - 获取Agent上的流水线
- `POST /api/v1/pipelines/agent/:agentId/start/:pipelineId` - 在Agent上启动特定流水线
- `POST /api/v1/pipelines/agent/:agentId/stop/:pipelineId` - 在Agent上停止特定流水线
- `POST /api/v1/pipelines/agent/:agentId/deploy` - 部署流水线到Agent

### 批量操作
- `POST /api/v1/pipelines/batch/start` - 批量启动流水线
- `POST /api/v1/pipelines/batch/stop` - 批量停止流水线
- `POST /api/v1/pipelines/batch/deploy` - 批量部署流水线

### 模板和验证
- `GET /api/v1/pipelines/templates` - 获取流水线模板
- `POST /api/v1/pipelines/validate` - 验证流水线配置

## 使用示例

### 1. 获取所有流水线
```bash
curl -X GET "http://localhost:8080/api/v1/pipelines" \
  -H "Authorization: dev-token"
```

### 2. 启动流水线
```bash
curl -X POST "http://localhost:8080/api/v1/pipelines/pipeline-001/start" \
  -H "Authorization: dev-token"
```

### 3. 部署流水线到Agent
```bash
curl -X POST "http://localhost:8080/api/v1/pipelines/agent/agent-001/deploy" \
  -H "Authorization: dev-token" \
  -H "Content-Type: application/json" \
  -d '{
    "pipeline_id": "pipeline-001"
  }'
```

### 4. 批量启动流水线
```bash
curl -X POST "http://localhost:8080/api/v1/pipelines/batch/start" \
  -H "Authorization: dev-token" \
  -H "Content-Type: application/json" \
  -d '{
    "pipeline_ids": ["pipeline-001", "pipeline-002"]
  }'
```

### 5. 批量部署流水线
```bash
curl -X POST "http://localhost:8080/api/v1/pipelines/batch/deploy" \
  -H "Authorization: dev-token" \
  -H "Content-Type: application/json" \
  -d '{
    "deployments": [
      {"agent_id": "agent-001", "pipeline_id": "pipeline-001"},
      {"agent_id": "agent-002", "pipeline_id": "pipeline-002"}
    ]
  }'
```

## 核心改进

### 1. 问题解决
✅ **Agent不再自动创建流水线** - Agent现在仅在收到明确的CREATE_PIPELINE命令时才创建流水线

✅ **Control plane保存配置** - 所有流水线配置和插件设置都保存在控制平面的数据库中

✅ **完整的流水线管理** - Control plane可以通过命令完全控制Agent上流水线的生命周期

### 2. 架构改进
- **命令驱动**: 使用`PipelineCommand`而不是自动配置推送
- **状态管理**: 双向状态同步，Agent向控制平面报告状态
- **队列机制**: 每个Agent有独立的命令队列
- **错误处理**: 完善的错误反馈和处理机制

### 3. 命令类型
- `CREATE_PIPELINE` - 创建流水线
- `START_PIPELINE` - 启动流水线
- `STOP_PIPELINE` - 停止流水线
- `DELETE_PIPELINE` - 删除流水线
- `UPDATE_PIPELINE` - 更新流水线配置
- `RELOAD_PIPELINE` - 重新加载配置
- `GET_STATUS` - 获取状态(心跳)

## 测试流程

1. **启动控制平面**:
   ```bash
   cd /Volumes/data/Code/Go/src/aiops
   ./bin/control_plane
   ```

2. **启动Agent**:
   ```bash
   cd /Volumes/data/Code/Go/src/aiops
   ./bin/agent
   ```

3. **测试API**: 使用上述curl命令测试各种功能

4. **观察日志**: 检查控制平面和Agent的日志输出，确认命令正确发送和处理

## 注意事项

- 所有API都需要Authorization头部认证
- 流水线ID和Agent ID必须已存在于系统中
- 批量操作返回每个操作的详细结果
- 配置验证功能目前标记为待实现
- 需要确保gRPC连接正常建立才能发送命令
