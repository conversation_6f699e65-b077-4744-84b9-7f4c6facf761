package test

import (
	"math"
	"testing"
	"time"

	"aiops/pkg/pipeline"

	"github.com/stretchr/testify/assert"
)

// TestActivityAnalysisPlugin 测试活动分析插件
func TestActivityAnalysisPlugin(t *testing.T) {
	// 创建测试配置
	config := map[string]interface{}{
		"max_history_size": 100,
		"analysis": map[string]interface{}{
			"algorithm":   "statistical_anomaly",
			"sensitivity": 0.05,
			"thresholds": map[string]interface{}{
				"large_activity_threshold":  5000.0,
				"immediate_start_threshold": 300.0,
				"z_score_threshold":         2.0,
				"iqr_multiplier":            1.5,
			},
			"parameters": map[string]interface{}{
				"enable_z_score":       true,
				"enable_iqr":           true,
				"enable_pattern_match": true,
			},
		},
	}

	// 这里需要实际的插件实现来进行测试
	// 由于插件是通过.so文件加载的，我们需要构建插件后才能测试
	t.Log("Activity analysis plugin test placeholder")
	t.Log("Config:", config)
}

// TestBusinessDataStructures 测试业务数据结构
func TestBusinessDataStructures(t *testing.T) {
	// 测试 BusinessEntity 结构
	now := time.Now()
	entity := &pipeline.BusinessEntity{
		ID:        "test_activity_001",
		Type:      string(pipeline.ActivityEntityType),
		Name:      "Test Activity",
		Status:    "created",
		Timestamp: now,
		Properties: map[string]any{
			"activity_id":           "test_activity_001",
			"activity_name":         "Test Activity",
			"activity_type":         "conference",
			"created_time":          now.Add(-1 * time.Hour).Format(time.RFC3339),
			"start_time":            now.Add(2 * time.Hour).Format(time.RFC3339),
			"end_time":              now.Add(4 * time.Hour).Format(time.RFC3339),
			"expected_participants": int64(15000),
			"actual_participants":   int64(0),
			"creator_id":            "user_123",
			"description":           "Test activity for analysis",
		},
		Metadata: map[string]interface{}{
			"source":     "test",
			"simulation": true,
		},
		Tags: map[string]string{
			"entity_type": "activity",
			"status":      "created",
		},
	}

	// 验证结构
	assert.Equal(t, "test_activity_001", entity.ID)
	assert.Equal(t, string(pipeline.ActivityEntityType), entity.Type)
	assert.Equal(t, "Test Activity", entity.Name)
	assert.Equal(t, "created", entity.Status)
	assert.NotNil(t, entity.Properties)
	assert.NotNil(t, entity.Metadata)
	assert.NotNil(t, entity.Tags)

	// 验证属性
	assert.Equal(t, int64(15000), entity.Properties["expected_participants"])
	assert.Equal(t, "conference", entity.Properties["activity_type"])
	assert.Equal(t, "user_123", entity.Properties["creator_id"])
}

// TestPipelineDataWithBusinessData 测试包含业务数据的流水线数据
func TestPipelineDataWithBusinessData(t *testing.T) {
	now := time.Now()

	// 创建业务实体
	activity := &pipeline.BusinessEntity{
		ID:        "activity_001",
		Type:      string(pipeline.ActivityEntityType),
		Name:      "Large Conference",
		Status:    "created",
		Timestamp: now,
		Properties: map[string]interface{}{
			"activity_id":           "activity_001",
			"created_time":          now.Add(-5 * time.Minute).Format(time.RFC3339),
			"start_time":            now.Add(1 * time.Minute).Format(time.RFC3339), // 立即开始
			"expected_participants": int64(12000),                                  // 大型活动
		},
	}

	// 创建流水线数据
	pipelineData := &pipeline.PipelineData{
		ID:           "test_data_001",
		Type:         pipeline.BusinessDataType,
		Source:       "test_collector",
		DeviceID:     "test_device",
		Timestamp:    now,
		BusinessData: []*pipeline.BusinessEntity{activity},
		Metadata: map[string]interface{}{
			"test_case": "large_activity_immediate_start",
		},
		Context: map[string]interface{}{
			"test_scenario": "anomaly_detection",
		},
		Tags: map[string]string{
			"data_type": "business",
			"test":      "true",
		},
	}

	// 验证数据结构
	assert.Equal(t, pipeline.BusinessDataType, pipelineData.Type)
	assert.Len(t, pipelineData.BusinessData, 1)
	assert.Equal(t, "activity_001", pipelineData.BusinessData[0].ID)
	assert.Equal(t, int64(12000), pipelineData.BusinessData[0].Properties["expected_participants"])
}

// TestAnomalyDetectionScenarios 测试异常检测场景
func TestAnomalyDetectionScenarios(t *testing.T) {
	scenarios := []struct {
		name                 string
		expectedParticipants int64
		timeToStartMinutes   int
		createdHour          int
		expectedAnomalies    []string
	}{
		{
			name:                 "Normal Activity",
			expectedParticipants: 500,
			timeToStartMinutes:   120, // 2小时后开始
			createdHour:          14,  // 下午2点创建
			expectedAnomalies:    []string{},
		},
		{
			name:                 "Large Activity Immediate Start",
			expectedParticipants: 15000,
			timeToStartMinutes:   2, // 2分钟后开始
			createdHour:          14,
			expectedAnomalies:    []string{"large_activity_immediate_start"},
		},
		{
			name:                 "Suspicious Off-Hours Pattern",
			expectedParticipants: 20000,
			timeToStartMinutes:   1, // 1分钟后开始
			createdHour:          2, // 凌晨2点创建
			expectedAnomalies:    []string{"large_activity_immediate_start", "suspicious_activity_pattern"},
		},
		{
			name:                 "Small Activity Quick Start",
			expectedParticipants: 100,
			timeToStartMinutes:   1,
			createdHour:          10,
			expectedAnomalies:    []string{}, // 小活动快速开始不算异常
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			now := time.Now()

			// 设置创建时间为指定小时
			createdTime := time.Date(now.Year(), now.Month(), now.Day(), scenario.createdHour, 0, 0, 0, now.Location())
			startTime := createdTime.Add(time.Duration(scenario.timeToStartMinutes) * time.Minute)

			activity := &pipeline.BusinessEntity{
				ID:        "test_activity",
				Type:      string(pipeline.ActivityEntityType),
				Name:      scenario.name,
				Status:    "created",
				Timestamp: now,
				Properties: map[string]interface{}{
					"created_time":          createdTime.Format(time.RFC3339),
					"start_time":            startTime.Format(time.RFC3339),
					"expected_participants": scenario.expectedParticipants,
				},
			}

			// 验证测试数据
			assert.Equal(t, scenario.expectedParticipants, activity.Properties["expected_participants"])

			// 计算时间间隔
			timeToStart := startTime.Sub(createdTime)
			assert.Equal(t, time.Duration(scenario.timeToStartMinutes)*time.Minute, timeToStart)

			t.Logf("Scenario: %s", scenario.name)
			t.Logf("  Expected Participants: %d", scenario.expectedParticipants)
			t.Logf("  Time to Start: %v", timeToStart)
			t.Logf("  Created Hour: %d", scenario.createdHour)
			t.Logf("  Expected Anomalies: %v", scenario.expectedAnomalies)
		})
	}
}

// TestStatisticalFunctions 测试统计函数
func TestStatisticalFunctions(t *testing.T) {
	// 测试数据
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}

	// 测试平均值计算
	mean := calculateMean(values)
	expectedMean := 5.5
	assert.InDelta(t, expectedMean, mean, 0.001, "Mean calculation should be correct")

	// 测试标准差计算
	stdDev := calculateStdDev(values, mean)
	expectedStdDev := 3.0276503540974917 // 样本标准差
	assert.InDelta(t, expectedStdDev, stdDev, 0.001, "Standard deviation calculation should be correct")

	// 测试四分位数计算
	q1, q3 := calculateQuartiles(values)
	expectedQ1 := 3.25
	expectedQ3 := 7.75
	assert.InDelta(t, expectedQ1, q1, 0.001, "Q1 calculation should be correct")
	assert.InDelta(t, expectedQ3, q3, 0.001, "Q3 calculation should be correct")
}

// 辅助函数（从插件中复制）
func calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func calculateStdDev(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 0
	}
	sumSquaredDiff := 0.0
	for _, v := range values {
		diff := v - mean
		sumSquaredDiff += diff * diff
	}
	variance := sumSquaredDiff / float64(len(values)-1)
	return math.Sqrt(variance)
}

func calculateQuartiles(sortedValues []float64) (q1, q3 float64) {
	n := len(sortedValues)
	if n == 0 {
		return 0, 0
	}

	// 计算Q1位置
	q1Index := float64(n-1) * 0.25
	q1Lower := int(q1Index)
	q1Upper := q1Lower + 1
	if q1Upper >= n {
		q1Upper = n - 1
	}
	q1Weight := q1Index - float64(q1Lower)
	q1 = sortedValues[q1Lower]*(1-q1Weight) + sortedValues[q1Upper]*q1Weight

	// 计算Q3位置
	q3Index := float64(n-1) * 0.75
	q3Lower := int(q3Index)
	q3Upper := q3Lower + 1
	if q3Upper >= n {
		q3Upper = n - 1
	}
	q3Weight := q3Index - float64(q3Lower)
	q3 = sortedValues[q3Lower]*(1-q3Weight) + sortedValues[q3Upper]*q3Weight

	return q1, q3
}
