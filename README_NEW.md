# AIOps 监控系统 - 简化架构版本

## 概述

这是 AIOps 监控系统的全新简化架构版本，采用单体应用设计，基于任务管理模式，大幅降低了系统复杂性，提高了可维护性和可靠性。

## 🚀 新架构特性

### 1. 单体架构
- **单一二进制文件**：一个可执行文件包含所有功能
- **简化部署**：无需复杂的分布式部署
- **统一配置**：单一配置文件管理所有设置
- **本地存储**：使用 SQLite 数据库，无需外部依赖

### 2. 任务导向设计
- **三种任务类型**：采集(collect)、分析(analyze)、告警(alert)
- **灵活调度**：支持间隔调度、Cron 表达式、事件驱动
- **统一管理**：通过 REST API 管理所有任务

### 3. 内置功能模块
- **系统监控**：CPU、内存、磁盘、网络指标采集
- **MySQL 监控**：数据库性能指标采集
- **异常检测**：统计分析、Z-Score、IQR 算法
- **阈值分析**：多级阈值检查和告警
- **邮件告警**：SMTP 邮件通知

## 📁 项目结构

```
aiops/
├── cmd/main.go                    # 应用入口
├── internal/                      # 内部模块
│   ├── api/                      # HTTP API 服务
│   ├── alerter/                  # 告警器模块
│   ├── analyzer/                 # 分析器模块
│   ├── collector/                # 采集器模块
│   ├── config/                   # 配置管理
│   ├── database/                 # 数据库初始化
│   ├── scheduler/                # 任务调度器
│   ├── storage/                  # 存储层
│   └── task/                     # 任务类型定义
├── config/aiops.yaml             # 主配置文件
├── examples/tasks/               # 示例任务配置
├── web/index.html                # Web UI
└── data/                         # 数据目录
```

## 🛠️ 快速开始

### 1. 编译运行

```bash
# 安装依赖
go mod tidy

# 编译
go build -o aiops cmd/main.go

# 运行
./aiops
```

### 2. 访问系统

- **Web UI**: http://localhost:8080
- **API 文档**: http://localhost:8080/health
- **健康检查**: http://localhost:8080/health

### 3. 配置文件

编辑 `config/aiops.yaml`：

```yaml
# HTTP 服务器配置
server:
  port: 8080

# 数据库配置
database:
  driver: sqlite
  dsn: ./data/aiops.db

# 任务配置
tasks:
  default_interval: 30s
  max_concurrent: 10

# 邮件配置
email:
  smtp_host: smtp.example.com
  smtp_port: 587
  smtp_username: <EMAIL>
  smtp_password: your_password
  from_email: <EMAIL>
```

## 📋 任务管理

### 任务类型

1. **采集任务 (collect)**
   - 系统指标采集
   - MySQL 数据库监控
   - 自定义指标采集

2. **分析任务 (analyze)**
   - 异常检测分析
   - 阈值检查分析
   - 趋势分析

3. **告警任务 (alert)**
   - 邮件告警
   - 条件触发告警

### API 接口

#### 任务管理
```bash
# 创建任务
POST /api/v1/tasks

# 获取任务列表
GET /api/v1/tasks

# 获取单个任务
GET /api/v1/tasks/{id}

# 更新任务
PUT /api/v1/tasks/{id}

# 删除任务
DELETE /api/v1/tasks/{id}

# 立即执行任务
POST /api/v1/tasks/{id}/start
```

#### 监控数据
```bash
# 获取指标数据
GET /api/v1/metrics

# 获取告警事件
GET /api/v1/alerts

# 获取日志
GET /api/v1/logs
```

### 示例任务配置

#### 系统监控任务
```json
{
  "name": "系统指标采集",
  "type": "collect",
  "config": {
    "collector_type": "system",
    "metrics": ["cpu", "memory", "disk", "network"]
  },
  "schedule": {
    "type": "interval",
    "interval": "30s"
  },
  "enabled": true
}
```

#### 异常检测任务
```json
{
  "name": "CPU异常检测",
  "type": "analyze",
  "config": {
    "analyzer_type": "anomaly_detection",
    "metric": "cpu_usage_percent",
    "algorithm": "statistical",
    "threshold": 2.0,
    "window": "5m"
  },
  "schedule": {
    "type": "interval",
    "interval": "1m"
  },
  "enabled": true
}
```

#### 邮件告警任务
```json
{
  "name": "邮件告警",
  "type": "alert",
  "config": {
    "alert_type": "email",
    "recipients": ["<EMAIL>"],
    "conditions": [
      {
        "metric": "cpu_usage_percent",
        "operator": ">",
        "value": 90
      }
    ]
  },
  "schedule": {
    "type": "event_driven"
  },
  "enabled": true
}
```

## 🔧 扩展开发

### 添加新的采集器

1. 在 `internal/collector/` 目录下创建新的采集器文件
2. 实现 `Collector` 接口
3. 在 `manager.go` 中注册新采集器

### 添加新的分析器

1. 在 `internal/analyzer/` 目录下创建新的分析器文件
2. 实现 `Analyzer` 接口
3. 在 `manager.go` 中注册新分析器

### 添加新的告警器

1. 在 `internal/alerter/` 目录下创建新的告警器文件
2. 实现 `Alerter` 接口
3. 在 `manager.go` 中注册新告警器

## 🆚 与旧架构对比

| 特性 | 旧架构 | 新架构 |
|------|--------|--------|
| 部署复杂度 | 高（Agent + Control Plane） | 低（单一应用） |
| 配置管理 | 复杂（多个配置文件） | 简单（单一配置） |
| 网络依赖 | 需要 gRPC 通信 | 无网络依赖 |
| 故障点 | 多个（网络、服务） | 少（本地应用） |
| 学习成本 | 高（流水线概念） | 低（任务概念） |
| 扩展性 | 复杂（插件系统） | 简单（内置模块） |

## 📈 性能优化

- **并发控制**：工作池限制并发任务数量
- **资源管理**：自动清理过期数据
- **缓存机制**：内存缓存热点数据
- **批量处理**：批量保存指标数据

## 🔒 安全考虑

- **输入验证**：严格的参数校验
- **错误处理**：完善的错误处理机制
- **日志记录**：详细的操作日志
- **权限控制**：API 访问控制（可扩展）

## 📝 更新日志

### v3.0.0 - 简化架构版本
- 重新设计为单体架构
- 采用任务导向的设计模式
- 内置常用监控功能
- 简化配置和部署流程
- 提供 Web UI 界面
- 完整的 REST API

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
